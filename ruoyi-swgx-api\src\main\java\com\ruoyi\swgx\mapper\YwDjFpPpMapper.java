package com.ruoyi.swgx.mapper;

import com.ruoyi.swgx.domain.YwDjFpPp;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 单据发票匹配关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface YwDjFpPpMapper {
    
    /**
     * 查询单据发票匹配关系
     * 
     * @param id 单据发票匹配关系主键
     * @return 单据发票匹配关系
     */
    public YwDjFpPp selectYwDjFpPpById(String id);

    /**
     * 查询单据发票匹配关系列表
     * 
     * @param ywDjFpPp 单据发票匹配关系
     * @return 单据发票匹配关系集合
     */
    public List<YwDjFpPp> selectYwDjFpPpList(YwDjFpPp ywDjFpPp);

    /**
     * 根据单据ID查询匹配关系
     * 
     * @param djId 单据ID
     * @return 单据发票匹配关系集合
     */
    public List<YwDjFpPp> selectByDjId(String djId);

    /**
     * 根据单据明细ID查询匹配关系
     *
     * @param djmxId 单据明细ID
     * @return 单据发票匹配关系集合
     */
    public List<YwDjFpPp> selectByDjmxId(String djmxId);

    /**
     * 根据单据明细ID和发票明细ID查询匹配关系
     *
     * @param djmxId 单据明细ID
     * @param fpmxId 发票明细ID
     * @return 单据发票匹配关系
     */
    public YwDjFpPp selectByDjmxAndFpmx(@Param("djmxId") String djmxId, @Param("fpmxId") String fpmxId);

    /**
     * 新增单据发票匹配关系
     * 
     * @param ywDjFpPp 单据发票匹配关系
     * @return 结果
     */
    public int insertYwDjFpPp(YwDjFpPp ywDjFpPp);

    /**
     * 修改单据发票匹配关系
     * 
     * @param ywDjFpPp 单据发票匹配关系
     * @return 结果
     */
    public int updateYwDjFpPp(YwDjFpPp ywDjFpPp);

    /**
     * 删除单据发票匹配关系
     * 
     * @param id 单据发票匹配关系主键
     * @return 结果
     */
    public int deleteYwDjFpPpById(String id);

    /**
     * 批量删除单据发票匹配关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYwDjFpPpByIds(String[] ids);
}
