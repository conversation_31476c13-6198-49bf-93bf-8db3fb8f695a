<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.swgx.mapper.DpFpxxMapper">
    
    <resultMap type="DpFpxx" id="DpFpxxResult">
        <result property="id"           column="ID"           />
        <result property="qyId"         column="QY_ID"        />
        <result property="sksbh"        column="SKSBH"        />
        <result property="fplxdm"       column="FPLXDM"       />
        <result property="sfwzzfp"      column="SFWZZFP"      />
        <result property="fpDm"         column="FP_DM"        />
        <result property="fpHm"         column="FP_HM"        />
        <result property="qdfphm"       column="QDFPHM"       />
        <result property="fpzt"         column="FPZT"         />
        <result property="scbz"         column="SCBZ"         />
        <result property="kprq"         column="KPRQ"         />
        <result property="tspz"         column="TSPZ"         />
        <result property="jym"          column="JYM"          />
        <result property="skm"          column="SKM"          />
        <result property="xsfNsrsbh"    column="XSF_NSRSBH"   />
        <result property="xsfMc"        column="XSF_MC"       />
        <result property="xsfDzdh"      column="XSF_DZDH"     />
        <result property="xsfYhzh"      column="XSF_YHZH"     />
        <result property="gmfNsrsbh"    column="GMF_NSRSBH"   />
        <result property="gmfMc"        column="GMF_MC"       />
        <result property="gmfDzdh"      column="GMF_DZDH"     />
        <result property="gmfYhzh"      column="GMF_YHZH"     />
        <result property="gmfLxfs"      column="GMF_LXFS"     />
        <result property="zhsl"         column="ZHSL"         />
        <result property="jshj"         column="JSHJ"         />
        <result property="hjje"         column="HJJE"         />
        <result property="hjse"         column="HJSE"         />
        <result property="kpr"          column="KPR"          />
        <result property="skr"          column="SKR"          />
        <result property="fhr"          column="FHR"          />
        <result property="bz"           column="BZ"           />
        <result property="jmbbh"        column="JMBBH"        />
        <result property="zyspmc"       column="ZYSPMC"       />
        <result property="spsm"         column="SPSM"         />
        <result property="qdbz"         column="QDBZ"         />
        <result property="ssyf"         column="SSYF"         />
        <result property="kpjh"         column="KPJH"         />
        <result property="tzdbh"        column="TZDBH"        />
        <result property="yfpdm"        column="YFPDM"        />
        <result property="yfphm"        column="YFPHM"        />
        <result property="yqdfphm"      column="YQDFPHM"      />
        <result property="zfrq"         column="ZFRQ"         />
        <result property="zfr"          column="ZFR"          />
        <result property="qmcs"         column="QMCS"         />
        <result property="qmz"          column="QMZ"          />
        <result property="ykfsje"       column="YKFSJE"       />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
    </resultMap>

    <resultMap id="DpFpxxDpFpxxMxResult" type="DpFpxx" extends="DpFpxxResult">
        <collection property="dpFpxxMxList" ofType="DpFpxxMx" column="ID" select="selectDpFpxxMxByFpid" />
    </resultMap>

    <resultMap type="DpFpxxMx" id="DpFpxxMxResult">
        <result property="id"           column="ID"           />
        <result property="qyId"         column="QY_ID"        />
        <result property="fpid"         column="FPID"         />
        <result property="fphxz"        column="FPHXZ"        />
        <result property="spmc"         column="SPMC"         />
        <result property="spsm"         column="SPSM"         />
        <result property="ggxh"         column="GGXH"         />
        <result property="dw"           column="DW"           />
        <result property="spbm"         column="SPBM"         />
        <result property="zzstsgl"      column="ZZSTSGL"      />
        <result property="yhzcbs"       column="YHZCBS"       />
        <result property="lslbs"        column="LSLBS"        />
        <result property="spsl"         column="SPSL"         />
        <result property="dj"           column="DJ"           />
        <result property="je"           column="JE"           />
        <result property="sl"           column="SL"           />
        <result property="se"           column="SE"           />
        <result property="hsbz"         column="HSBZ"         />
        <result property="mxlx"         column="MXLX"         />
        <result property="serialNumber" column="serial_number" />
        <result property="ssdzgsid"     column="ssdzgsid"     />
        <result property="jzjtlxDm"     column="jzjtlxDm"     />
        <result property="mtzldm"       column="MTZLDM"       />
        <result property="cxmyxqx"      column="CXMYXQX"      />
        <result property="lzmxxh"       column="LZMXXH"       />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
    </resultMap>

    <sql id="selectDpFpxxVo">
        select ID, QY_ID, SKSBH, FPLXDM, SFWZZFP, FP_DM, FP_HM, QDFPHM, FPZT, SCBZ, KPRQ, TSPZ, JYM, SKM, 
               XSF_NSRSBH, XSF_MC, XSF_DZDH, XSF_YHZH, GMF_NSRSBH, GMF_MC, GMF_DZDH, GMF_YHZH, GMF_LXFS, 
               ZHSL, JSHJ, HJJE, HJSE, KPR, SKR, FHR, BZ, JMBBH, ZYSPMC, SPSM, QDBZ, SSYF, KPJH, TZDBH, 
               YFPDM, YFPHM, YQDFPHM, ZFRQ, ZFR, QMCS, QMZ, YKFSJE, create_by, create_time, update_by, update_time
        from swgx_dp_fpxx
    </sql>

    <sql id="selectDpFpxxMxVo">
        select ID, QY_ID, FPID, FPHXZ, SPMC, SPSM, GGXH, DW, SPBM, ZZSTSGL, YHZCBS, LSLBS, SPSL, DJ, JE, SL, SE,
               HSBZ, MXLX, serial_number, ssdzgsid, jzjtlxDm, MTZLDM, CXMYXQX, LZMXXH,
               create_by, create_time, update_by, update_time
        from swgx_dp_fpxx_mx
    </sql>

    <select id="selectDpFpxxList" parameterType="DpFpxx" resultMap="DpFpxxResult">
        <include refid="selectDpFpxxVo"/>
        <where>  
            <if test="qyId != null  and qyId != ''"> and QY_ID = #{qyId}</if>
            <if test="sksbh != null  and sksbh != ''"> and SKSBH = #{sksbh}</if>
            <if test="fplxdm != null  and fplxdm != ''"> and FPLXDM = #{fplxdm}</if>
            <if test="sfwzzfp != null  and sfwzzfp != ''"> and SFWZZFP = #{sfwzzfp}</if>
            <if test="fpDm != null  and fpDm != ''"> and FP_DM = #{fpDm}</if>
            <if test="fpHm != null  and fpHm != ''"> and FP_HM = #{fpHm}</if>
            <if test="fpzt != null"> and FPZT = #{fpzt}</if>
            <if test="scbz != null"> and SCBZ = #{scbz}</if>
            <if test="kprq != null  and kprq != ''"> and KPRQ like concat('%', #{kprq}, '%')</if>
            <if test="xsfNsrsbh != null  and xsfNsrsbh != ''"> and XSF_NSRSBH = #{xsfNsrsbh}</if>
            <if test="xsfMc != null  and xsfMc != ''"> and XSF_MC like concat('%', #{xsfMc}, '%')</if>
            <if test="gmfNsrsbh != null  and gmfNsrsbh != ''"> and GMF_NSRSBH = #{gmfNsrsbh}</if>
            <if test="gmfMc != null  and gmfMc != ''"> and GMF_MC like concat('%', #{gmfMc}, '%')</if>
            <if test="zyspmc != null  and zyspmc != ''"> and ZYSPMC like concat('%', #{zyspmc}, '%')</if>
            <if test="ssyf != null  and ssyf != ''"> and SSYF = #{ssyf}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDpFpxxById" parameterType="String" resultMap="DpFpxxDpFpxxMxResult">
        <include refid="selectDpFpxxVo"/>
        where ID = #{id}
    </select>

    <select id="selectDpFpxxByFpDmAndHm" resultMap="DpFpxxResult">
        <include refid="selectDpFpxxVo"/>
        where FP_DM = #{fpDm} and FP_HM = #{fpHm}
    </select>

    <select id="selectRedFlushableInvoiceList" parameterType="DpFpxx" resultMap="DpFpxxResult">
        <include refid="selectDpFpxxVo"/>
        <where>  
            and FPZT = 0
            <if test="qyId != null  and qyId != ''"> and QY_ID = #{qyId}</if>
            <if test="xsfNsrsbh != null  and xsfNsrsbh != ''"> and XSF_NSRSBH = #{xsfNsrsbh}</if>
            <if test="gmfNsrsbh != null  and gmfNsrsbh != ''"> and GMF_NSRSBH = #{gmfNsrsbh}</if>
            <if test="gmfMc != null  and gmfMc != ''"> and GMF_MC like concat('%', #{gmfMc}, '%')</if>
            <if test="zyspmc != null  and zyspmc != ''"> and ZYSPMC like concat('%', #{zyspmc}, '%')</if>
            <if test="kprq != null  and kprq != ''"> and KPRQ like concat('%', #{kprq}, '%')</if>
        </where>
        order by KPRQ desc
    </select>

    <select id="selectDpFpxxByQyIdAndDateRange" resultMap="DpFpxxResult">
        <include refid="selectDpFpxxVo"/>
        where QY_ID = #{qyId}
        <if test="startDate != null and startDate != ''">
            and KPRQ &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and KPRQ &lt;= #{endDate}
        </if>
        order by KPRQ desc
    </select>

    <select id="selectDpFpxxByGmfNsrsbh" resultMap="DpFpxxResult">
        <include refid="selectDpFpxxVo"/>
        where GMF_NSRSBH = #{gmfNsrsbh}
        order by KPRQ desc
    </select>

    <select id="countInvoiceByStatus" resultType="java.util.Map">
        select FPZT as status, count(*) as count
        from swgx_dp_fpxx
        where QY_ID = #{qyId}
        group by FPZT
    </select>

    <select id="sumInvoiceAmount" resultType="java.util.Map">
        select 
            count(*) as invoiceCount,
            coalesce(sum(JSHJ), 0) as totalAmount,
            coalesce(sum(HJJE), 0) as totalTaxExclusiveAmount,
            coalesce(sum(HJSE), 0) as totalTaxAmount
        from swgx_dp_fpxx
        where QY_ID = #{qyId}
        <if test="startDate != null and startDate != ''">
            and KPRQ &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and KPRQ &lt;= #{endDate}
        </if>
    </select>

    <insert id="insertDpFpxx" parameterType="DpFpxx">
        insert into swgx_dp_fpxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="qyId != null">QY_ID,</if>
            <if test="sksbh != null">SKSBH,</if>
            <if test="fplxdm != null">FPLXDM,</if>
            <if test="sfwzzfp != null">SFWZZFP,</if>
            <if test="fpDm != null">FP_DM,</if>
            <if test="fpHm != null">FP_HM,</if>
            <if test="qdfphm != null">QDFPHM,</if>
            <if test="fpzt != null">FPZT,</if>
            <if test="scbz != null">SCBZ,</if>
            <if test="kprq != null">KPRQ,</if>
            <if test="tspz != null">TSPZ,</if>
            <if test="jym != null">JYM,</if>
            <if test="skm != null">SKM,</if>
            <if test="xsfNsrsbh != null">XSF_NSRSBH,</if>
            <if test="xsfMc != null">XSF_MC,</if>
            <if test="xsfDzdh != null">XSF_DZDH,</if>
            <if test="xsfYhzh != null">XSF_YHZH,</if>
            <if test="gmfNsrsbh != null">GMF_NSRSBH,</if>
            <if test="gmfMc != null">GMF_MC,</if>
            <if test="gmfDzdh != null">GMF_DZDH,</if>
            <if test="gmfYhzh != null">GMF_YHZH,</if>
            <if test="gmfLxfs != null">GMF_LXFS,</if>
            <if test="zhsl != null">ZHSL,</if>
            <if test="jshj != null">JSHJ,</if>
            <if test="hjje != null">HJJE,</if>
            <if test="hjse != null">HJSE,</if>
            <if test="kpr != null">KPR,</if>
            <if test="skr != null">SKR,</if>
            <if test="fhr != null">FHR,</if>
            <if test="bz != null">BZ,</if>
            <if test="jmbbh != null">JMBBH,</if>
            <if test="zyspmc != null">ZYSPMC,</if>
            <if test="spsm != null">SPSM,</if>
            <if test="qdbz != null">QDBZ,</if>
            <if test="ssyf != null">SSYF,</if>
            <if test="kpjh != null">KPJH,</if>
            <if test="tzdbh != null">TZDBH,</if>
            <if test="yfpdm != null">YFPDM,</if>
            <if test="yfphm != null">YFPHM,</if>
            <if test="yqdfphm != null">YQDFPHM,</if>
            <if test="zfrq != null">ZFRQ,</if>
            <if test="zfr != null">ZFR,</if>
            <if test="qmcs != null">QMCS,</if>
            <if test="qmz != null">QMZ,</if>
            <if test="ykfsje != null">YKFSJE,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="qyId != null">#{qyId},</if>
            <if test="sksbh != null">#{sksbh},</if>
            <if test="fplxdm != null">#{fplxdm},</if>
            <if test="sfwzzfp != null">#{sfwzzfp},</if>
            <if test="fpDm != null">#{fpDm},</if>
            <if test="fpHm != null">#{fpHm},</if>
            <if test="qdfphm != null">#{qdfphm},</if>
            <if test="fpzt != null">#{fpzt},</if>
            <if test="scbz != null">#{scbz},</if>
            <if test="kprq != null">#{kprq},</if>
            <if test="tspz != null">#{tspz},</if>
            <if test="jym != null">#{jym},</if>
            <if test="skm != null">#{skm},</if>
            <if test="xsfNsrsbh != null">#{xsfNsrsbh},</if>
            <if test="xsfMc != null">#{xsfMc},</if>
            <if test="xsfDzdh != null">#{xsfDzdh},</if>
            <if test="xsfYhzh != null">#{xsfYhzh},</if>
            <if test="gmfNsrsbh != null">#{gmfNsrsbh},</if>
            <if test="gmfMc != null">#{gmfMc},</if>
            <if test="gmfDzdh != null">#{gmfDzdh},</if>
            <if test="gmfYhzh != null">#{gmfYhzh},</if>
            <if test="gmfLxfs != null">#{gmfLxfs},</if>
            <if test="zhsl != null">#{zhsl},</if>
            <if test="jshj != null">#{jshj},</if>
            <if test="hjje != null">#{hjje},</if>
            <if test="hjse != null">#{hjse},</if>
            <if test="kpr != null">#{kpr},</if>
            <if test="skr != null">#{skr},</if>
            <if test="fhr != null">#{fhr},</if>
            <if test="bz != null">#{bz},</if>
            <if test="jmbbh != null">#{jmbbh},</if>
            <if test="zyspmc != null">#{zyspmc},</if>
            <if test="spsm != null">#{spsm},</if>
            <if test="qdbz != null">#{qdbz},</if>
            <if test="ssyf != null">#{ssyf},</if>
            <if test="kpjh != null">#{kpjh},</if>
            <if test="tzdbh != null">#{tzdbh},</if>
            <if test="yfpdm != null">#{yfpdm},</if>
            <if test="yfphm != null">#{yfphm},</if>
            <if test="yqdfphm != null">#{yqdfphm},</if>
            <if test="zfrq != null">#{zfrq},</if>
            <if test="zfr != null">#{zfr},</if>
            <if test="qmcs != null">#{qmcs},</if>
            <if test="qmz != null">#{qmz},</if>
            <if test="ykfsje != null">#{ykfsje},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDpFpxx" parameterType="DpFpxx">
        update swgx_dp_fpxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="qyId != null">QY_ID = #{qyId},</if>
            <if test="sksbh != null">SKSBH = #{sksbh},</if>
            <if test="fplxdm != null">FPLXDM = #{fplxdm},</if>
            <if test="sfwzzfp != null">SFWZZFP = #{sfwzzfp},</if>
            <if test="fpDm != null">FP_DM = #{fpDm},</if>
            <if test="fpHm != null">FP_HM = #{fpHm},</if>
            <if test="qdfphm != null">QDFPHM = #{qdfphm},</if>
            <if test="fpzt != null">FPZT = #{fpzt},</if>
            <if test="scbz != null">SCBZ = #{scbz},</if>
            <if test="kprq != null">KPRQ = #{kprq},</if>
            <if test="tspz != null">TSPZ = #{tspz},</if>
            <if test="jym != null">JYM = #{jym},</if>
            <if test="skm != null">SKM = #{skm},</if>
            <if test="xsfNsrsbh != null">XSF_NSRSBH = #{xsfNsrsbh},</if>
            <if test="xsfMc != null">XSF_MC = #{xsfMc},</if>
            <if test="xsfDzdh != null">XSF_DZDH = #{xsfDzdh},</if>
            <if test="xsfYhzh != null">XSF_YHZH = #{xsfYhzh},</if>
            <if test="gmfNsrsbh != null">GMF_NSRSBH = #{gmfNsrsbh},</if>
            <if test="gmfMc != null">GMF_MC = #{gmfMc},</if>
            <if test="gmfDzdh != null">GMF_DZDH = #{gmfDzdh},</if>
            <if test="gmfYhzh != null">GMF_YHZH = #{gmfYhzh},</if>
            <if test="gmfLxfs != null">GMF_LXFS = #{gmfLxfs},</if>
            <if test="zhsl != null">ZHSL = #{zhsl},</if>
            <if test="jshj != null">JSHJ = #{jshj},</if>
            <if test="hjje != null">HJJE = #{hjje},</if>
            <if test="hjse != null">HJSE = #{hjse},</if>
            <if test="kpr != null">KPR = #{kpr},</if>
            <if test="skr != null">SKR = #{skr},</if>
            <if test="fhr != null">FHR = #{fhr},</if>
            <if test="bz != null">BZ = #{bz},</if>
            <if test="jmbbh != null">JMBBH = #{jmbbh},</if>
            <if test="zyspmc != null">ZYSPMC = #{zyspmc},</if>
            <if test="spsm != null">SPSM = #{spsm},</if>
            <if test="qdbz != null">QDBZ = #{qdbz},</if>
            <if test="ssyf != null">SSYF = #{ssyf},</if>
            <if test="kpjh != null">KPJH = #{kpjh},</if>
            <if test="tzdbh != null">TZDBH = #{tzdbh},</if>
            <if test="yfpdm != null">YFPDM = #{yfpdm},</if>
            <if test="yfphm != null">YFPHM = #{yfphm},</if>
            <if test="yqdfphm != null">YQDFPHM = #{yqdfphm},</if>
            <if test="zfrq != null">ZFRQ = #{zfrq},</if>
            <if test="zfr != null">ZFR = #{zfr},</if>
            <if test="qmcs != null">QMCS = #{qmcs},</if>
            <if test="qmz != null">QMZ = #{qmz},</if>
            <if test="ykfsje != null">YKFSJE = #{ykfsje},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where ID = #{id}
    </update>

    <update id="updateInvoiceStatus">
        update swgx_dp_fpxx
        set FPZT = #{fpzt}, update_by = #{updateBy}, update_time = now()
        where ID = #{id}
    </update>

    <delete id="deleteDpFpxxById" parameterType="String">
        delete from swgx_dp_fpxx where ID = #{id}
    </delete>

    <delete id="deleteDpFpxxByIds" parameterType="String">
        delete from swgx_dp_fpxx where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 发票明细相关操作 -->
    <select id="selectDpFpxxMxByFpid" parameterType="String" resultMap="DpFpxxMxResult">
        <include refid="selectDpFpxxMxVo"/>
        where FPID = #{fpid}
        order by serial_number
    </select>

    <select id="selectDpFpxxMxById" parameterType="String" resultMap="DpFpxxMxResult">
        <include refid="selectDpFpxxMxVo"/>
        where ID = #{id}
    </select>

    <select id="selectDpFpxxMxBySpmc" resultMap="DpFpxxMxResult">
        <include refid="selectDpFpxxMxVo"/>
        where SPMC like concat('%', #{spmc}, '%')
        <if test="qyId != null and qyId != ''">
            and QY_ID = #{qyId}
        </if>
        order by create_time desc
    </select>

    <select id="selectDpFpxxMxBySpbm" resultMap="DpFpxxMxResult">
        <include refid="selectDpFpxxMxVo"/>
        where SPBM = #{spbm}
        <if test="qyId != null and qyId != ''">
            and QY_ID = #{qyId}
        </if>
        order by create_time desc
    </select>

    <!-- 查询未初始化匹配状态的电票明细 -->
    <select id="selectUninitializedDetails" resultMap="DpFpxxMxResult">
        <include refid="selectDpFpxxMxVo"/>
        where ID not in (
            select fpmx_id from swgx_dp_fpxx_mx_pp where fpmx_id is not null
        )
        order by create_time desc
    </select>

    <!-- 查询可用于匹配的电票明细 -->
    <select id="selectAvailableDetailsForMatch" resultMap="DpFpxxMxResult">
        select mx.ID, mx.QY_ID, mx.FPID, mx.FPHXZ, mx.SPMC, mx.SPSM, mx.GGXH, mx.DW, mx.SPBM,
               mx.ZZSTSGL, mx.YHZCBS, mx.LSLBS, mx.SPSL, mx.DJ, mx.JE, mx.SL, mx.SE,
               mx.HSBZ, mx.MXLX, mx.serial_number, mx.ssdzgsid, mx.jzjtlxDm, mx.MTZLDM,
               mx.CXMYXQX, mx.LZMXXH, mx.create_by, mx.create_time, mx.update_by, mx.update_time
        from swgx_dp_fpxx_mx mx
        inner join swgx_dp_fpxx fp on mx.FPID = fp.ID
        inner join swgx_dp_fpxx_mx_pp pp on mx.ID = pp.fpmx_id
        where fp.FPZT = 0  -- 正常状态的发票
        and pp.pp_zt in ('0', '1')  -- 未匹配或部分匹配
        and pp.sy_sl > 0  -- 剩余数量大于0
        <if test="gmfNsrsbh != null and gmfNsrsbh != ''">
            and fp.GMF_NSRSBH = #{gmfNsrsbh}
        </if>
        order by fp.KPRQ desc, mx.serial_number
    </select>

    <select id="sumInvoiceDetailAmount" resultType="java.util.Map">
        select
            count(*) as detailCount,
            coalesce(sum(SPSL), 0) as totalQuantity,
            coalesce(sum(JE), 0) as totalAmount,
            coalesce(sum(SE), 0) as totalTaxAmount
        from swgx_dp_fpxx_mx
        where FPID = #{fpid}
    </select>

    <select id="selectProductStatistics" resultType="java.util.Map">
        select
            mx.SPMC as productName,
            mx.SPBM as productCode,
            count(*) as invoiceCount,
            coalesce(sum(mx.SPSL), 0) as totalQuantity,
            coalesce(sum(mx.JE), 0) as totalAmount,
            coalesce(sum(mx.SE), 0) as totalTaxAmount
        from swgx_dp_fpxx_mx mx
        inner join swgx_dp_fpxx fp on mx.FPID = fp.ID
        where mx.QY_ID = #{qyId}
        <if test="startDate != null and startDate != ''">
            and fp.KPRQ &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and fp.KPRQ &lt;= #{endDate}
        </if>
        group by mx.SPMC, mx.SPBM
        order by totalAmount desc
    </select>

    <insert id="batchDpFpxxMx">
        insert into swgx_dp_fpxx_mx(ID, QY_ID, FPID, FPHXZ, SPMC, SPSM, GGXH, DW, SPBM, ZZSTSGL, YHZCBS, LSLBS,
                                    SPSL, DJ, JE, SL, SE, HSBZ, MXLX, serial_number, ssdzgsid, jzjtlxDm, MTZLDM,
                                    CXMYXQX, LZMXXH, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id}, #{item.qyId}, #{item.fpid}, #{item.fphxz}, #{item.spmc}, #{item.spsm}, #{item.ggxh},
             #{item.dw}, #{item.spbm}, #{item.zzstsgl}, #{item.yhzcbs}, #{item.lslbs}, #{item.spsl}, #{item.dj},
             #{item.je}, #{item.sl}, #{item.se}, #{item.hsbz}, #{item.mxlx}, #{item.serialNumber}, #{item.ssdzgsid},
             #{item.jzjtlxDm}, #{item.mtzldm}, #{item.cxmyxqx}, #{item.lzmxxh}, #{item.createBy}, #{item.createTime},
             #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <insert id="insertDpFpxxMx" parameterType="DpFpxxMx">
        insert into swgx_dp_fpxx_mx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="qyId != null">QY_ID,</if>
            <if test="fpid != null">FPID,</if>
            <if test="fphxz != null">FPHXZ,</if>
            <if test="spmc != null">SPMC,</if>
            <if test="spsm != null">SPSM,</if>
            <if test="ggxh != null">GGXH,</if>
            <if test="dw != null">DW,</if>
            <if test="spbm != null">SPBM,</if>
            <if test="zzstsgl != null">ZZSTSGL,</if>
            <if test="yhzcbs != null">YHZCBS,</if>
            <if test="lslbs != null">LSLBS,</if>
            <if test="spsl != null">SPSL,</if>
            <if test="dj != null">DJ,</if>
            <if test="je != null">JE,</if>
            <if test="sl != null">SL,</if>
            <if test="se != null">SE,</if>
            <if test="hsbz != null">HSBZ,</if>
            <if test="mxlx != null">MXLX,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="ssdzgsid != null">ssdzgsid,</if>
            <if test="jzjtlxDm != null">jzjtlxDm,</if>
            <if test="mtzldm != null">MTZLDM,</if>
            <if test="cxmyxqx != null">CXMYXQX,</if>
            <if test="lzmxxh != null">LZMXXH,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="qyId != null">#{qyId},</if>
            <if test="fpid != null">#{fpid},</if>
            <if test="fphxz != null">#{fphxz},</if>
            <if test="spmc != null">#{spmc},</if>
            <if test="spsm != null">#{spsm},</if>
            <if test="ggxh != null">#{ggxh},</if>
            <if test="dw != null">#{dw},</if>
            <if test="spbm != null">#{spbm},</if>
            <if test="zzstsgl != null">#{zzstsgl},</if>
            <if test="yhzcbs != null">#{yhzcbs},</if>
            <if test="lslbs != null">#{lslbs},</if>
            <if test="spsl != null">#{spsl},</if>
            <if test="dj != null">#{dj},</if>
            <if test="je != null">#{je},</if>
            <if test="sl != null">#{sl},</if>
            <if test="se != null">#{se},</if>
            <if test="hsbz != null">#{hsbz},</if>
            <if test="mxlx != null">#{mxlx},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="ssdzgsid != null">#{ssdzgsid},</if>
            <if test="jzjtlxDm != null">#{jzjtlxDm},</if>
            <if test="mtzldm != null">#{mtzldm},</if>
            <if test="cxmyxqx != null">#{cxmyxqx},</if>
            <if test="lzmxxh != null">#{lzmxxh},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDpFpxxMx" parameterType="DpFpxxMx">
        update swgx_dp_fpxx_mx
        <trim prefix="SET" suffixOverrides=",">
            <if test="qyId != null">QY_ID = #{qyId},</if>
            <if test="fpid != null">FPID = #{fpid},</if>
            <if test="fphxz != null">FPHXZ = #{fphxz},</if>
            <if test="spmc != null">SPMC = #{spmc},</if>
            <if test="spsm != null">SPSM = #{spsm},</if>
            <if test="ggxh != null">GGXH = #{ggxh},</if>
            <if test="dw != null">DW = #{dw},</if>
            <if test="spbm != null">SPBM = #{spbm},</if>
            <if test="zzstsgl != null">ZZSTSGL = #{zzstsgl},</if>
            <if test="yhzcbs != null">YHZCBS = #{yhzcbs},</if>
            <if test="lslbs != null">LSLBS = #{lslbs},</if>
            <if test="spsl != null">SPSL = #{spsl},</if>
            <if test="dj != null">DJ = #{dj},</if>
            <if test="je != null">JE = #{je},</if>
            <if test="sl != null">SL = #{sl},</if>
            <if test="se != null">SE = #{se},</if>
            <if test="hsbz != null">HSBZ = #{hsbz},</if>
            <if test="mxlx != null">MXLX = #{mxlx},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="ssdzgsid != null">ssdzgsid = #{ssdzgsid},</if>
            <if test="jzjtlxDm != null">jzjtlxDm = #{jzjtlxDm},</if>
            <if test="mtzldm != null">MTZLDM = #{mtzldm},</if>
            <if test="cxmyxqx != null">CXMYXQX = #{cxmyxqx},</if>
            <if test="lzmxxh != null">LZMXXH = #{lzmxxh},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteDpFpxxMxById" parameterType="String">
        delete from swgx_dp_fpxx_mx where ID = #{id}
    </delete>

    <delete id="deleteDpFpxxMxByFpid" parameterType="String">
        delete from swgx_dp_fpxx_mx where FPID = #{fpid}
    </delete>

    <delete id="deleteDpFpxxMxByIds" parameterType="String">
        delete from swgx_dp_fpxx_mx where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 统计所有电票明细数量 -->
    <select id="countAllDetails" resultType="int">
        select count(*) from swgx_dp_fpxx_mx
    </select>

    <!-- 查询发票匹配信息 -->
    <select id="selectInvoiceMatchInfo" parameterType="String" resultType="java.util.Map">
        select
            mx.ID as id,
            mx.SPMC as spmc,
            mx.GGXH as ggxh,
            mx.DW as dw,
            mx.SPSL as originalQuantity,
            COALESCE(pp.sy_sl, mx.SPSL) as remainingQuantity,
            COALESCE(pp.sy_je, mx.JE) as remainingAmount,
            COALESCE(pp.sy_se, mx.SE) as remainingTax,
            COALESCE(pp.pp_cs, 0) as matchCount,
            COALESCE(pp.pp_zt, '0') as matchStatus,
            pp.last_pp_time as lastMatchTime
        from swgx_dp_fpxx_mx mx
        left join swgx_dp_fpxx_mx_pp pp on mx.ID = pp.fpmx_id
        where mx.FPID = #{invoiceId}
        order by mx.serial_number
    </select>

    <!-- 查询明细匹配详情 -->
    <select id="selectDetailMatchInfo" parameterType="String" resultType="java.util.Map">
        select
            mx.ID as id,
            mx.SPMC as spmc,
            mx.GGXH as ggxh,
            mx.DW as dw,
            mx.SPSL as originalQuantity,
            mx.DJ as unitPrice,
            mx.JE as originalAmount,
            mx.SL as taxRate,
            mx.SE as originalTax,
            COALESCE(pp.sy_sl, mx.SPSL) as remainingQuantity,
            COALESCE(pp.sy_je, mx.JE) as remainingAmount,
            COALESCE(pp.sy_se, mx.SE) as remainingTax,
            COALESCE(pp.pp_cs, 0) as matchCount,
            COALESCE(pp.pp_zt, '0') as matchStatus,
            pp.last_pp_time as lastMatchTime
        from swgx_dp_fpxx_mx mx
        left join swgx_dp_fpxx_mx_pp pp on mx.ID = pp.fpmx_id
        where mx.ID = #{detailId}
    </select>

    <!-- 查询明细匹配历史记录 -->
    <select id="selectDetailMatchHistory" parameterType="String" resultType="java.util.Map">
        select
            pp.pp_rq as matchTime,
            dj.djbh as documentNo,
            pp.pp_sl as matchQuantity,
            pp.pp_je as matchAmount,
            pp.pp_se as matchTax,
            pp.pp_df as matchScore,
            pp.pp_lx as matchType,
            pp.pp_zt as matchStatus
        from swgx_yw_dj_fp_pp pp
        inner join swgx_yw_djxx dj on pp.dj_id = dj.id
        where pp.fpmx_id = #{detailId}
        order by pp.pp_rq desc
    </select>

</mapper>
