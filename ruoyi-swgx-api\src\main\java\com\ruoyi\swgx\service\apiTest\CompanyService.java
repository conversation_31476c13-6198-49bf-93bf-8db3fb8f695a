package com.ruoyi.swgx.service.apiTest;

import com.ruoyi.swgx.common.client.SwgxHttpClient;
import com.ruoyi.swgx.common.config.SwgxApiProperties;
import com.ruoyi.swgx.common.constants.SwgxApiConstants;
import com.ruoyi.swgx.common.dto.SwgxApiResponse;
import com.ruoyi.swgx.common.dto.company.QueryUniqueSignRequest;
import com.ruoyi.swgx.common.dto.company.QueryUniqueSignResponse;
import com.ruoyi.swgx.common.exception.SwgxApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 企业相关服务
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class CompanyService {

    @Autowired
    private SwgxHttpClient httpClient;

    @Autowired
    private SwgxApiProperties properties;

    /**
     * 查询企业唯一标识
     * 企业在百旺金穗云税务共享平台开户后，会生成企业唯一标识，后续相关接口都需要使用该标识。
     *
     * @param nsrmc 纳税人名称
     * @param nsrsbh 纳税人识别号
     * @return 企业唯一标识信息
     */
    public QueryUniqueSignResponse queryUniqueSign(String nsrmc, String nsrsbh) {
        log.info("查询企业唯一标识: nsrmc={}, nsrsbh={}", nsrmc, nsrsbh);

        try {
            QueryUniqueSignRequest request = new QueryUniqueSignRequest(nsrmc, nsrsbh);

            SwgxApiResponse<QueryUniqueSignResponse> response = httpClient.post(
                SwgxApiConstants.ApiPaths.QUERY_UNIQUE_SIGN,
                request,
                QueryUniqueSignResponse.class
            );

            QueryUniqueSignResponse result = response.getData();
            log.info("查询企业唯一标识成功: qyId={}", result.getQyId());

            return result;

        } catch (SwgxApiException e) {
            log.error("查询企业唯一标识失败: code={}, message={}, reason={}",
                e.getCode(), e.getMessage(), e.getReason());

            // 根据错误代码提供更友好的错误信息
            String friendlyMessage = getFriendlyErrorMessage(e.getCode(), e.getMessage(), e.getReason());
            throw new SwgxApiException(e.getCode(), friendlyMessage, e.getReason(), e);

        } catch (Exception e) {
            log.error("查询企业唯一标识时发生未知错误", e);
            throw new SwgxApiException("-1", "系统错误", "查询企业唯一标识时发生未知错误: " + e.getMessage(), e);
        }
    }

    /**
     * 获取友好的错误信息
     */
    private String getFriendlyErrorMessage(String code, String message, String reason) {
        if ("-1".equals(code)) {
            if (reason != null && reason.contains("查询不到企业信息")) {
                return "未找到企业信息，请检查企业名称和纳税人识别号是否正确";
            }
        }

        // 如果有具体的错误原因，优先使用
        if (reason != null && !reason.trim().isEmpty()) {
            return reason;
        }

        // 否则使用原始消息
        return message != null ? message : "查询失败";
    }

    /**
     * 根据纳税人识别号查询企业唯一标识
     * 
     * @param nsrsbh 纳税人识别号
     * @return 企业唯一标识
     */
    public String getCompanyUniqueId(String nsrsbh) {
        // 这里可以从系统配置或数据库中获取企业名称
        // 为了简化，这里假设有一个方法可以根据税号获取企业名称
        String nsrmc = getCompanyNameByTaxNumber(nsrsbh);
        
        QueryUniqueSignResponse response = queryUniqueSign(nsrmc, nsrsbh);
        return response.getQyId();
    }

    /**
     * 根据纳税人识别号获取企业名称
     * 这里应该从系统配置或数据库中获取
     * 
     * @param nsrsbh 纳税人识别号
     * @return 企业名称
     */
    private String getCompanyNameByTaxNumber(String nsrsbh) {
        // TODO: 实现从系统配置或数据库中获取企业名称的逻辑
        // 这里可以从RuoYi的字典配置中获取，或者从数据库中查询
        
        // 临时实现：从系统属性中获取
        return System.getProperty("swgx.company.name", "默认企业名称");
    }
}
