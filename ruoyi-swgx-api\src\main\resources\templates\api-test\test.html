<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百旺金穗云API测试中心 - 河北九赋</title>
    <link rel="shortcut icon" th:href="@{/img/logo.jpg}">
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/animate.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/swgx-test.css}" rel="stylesheet"/>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <!-- 页面标题 -->
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-cloud"></i> 百旺金穗云API测试中心</h5>
                        <div class="ibox-tools">
                            <span class="label label-primary">河北九赋</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <h2 class="no-margins">API测试工具</h2>
                                <p>专业的税务API集成解决方案，提供完整的接口测试功能</p>
                            </div>
                            <div class="col-sm-6">
                                <div class="text-right">
                                    <span class="label label-success">在线</span>
                                    <span class="label label-info">v1.0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能测试卡片 -->
        <div class="row">
            <div class="col-lg-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-building"></i> 企业唯一标识查询</h5>
                        <div class="ibox-tools">
                            <span class="label label-success">可用</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="text-center">
                            <div class="m-b-md">
                                <i class="fa fa-search feature-icon-invoice"></i>
                            </div>
                            <p class="text-muted">查询企业在税务系统中的唯一标识，获取企业基本信息</p>
                            <div class="m-t-md">
                                <a href="/swgx/demo/company" class="btn btn-primary">
                                    <i class="fa fa-play"></i> 开始测试
                                </a>
                            </div>
                        </div>
                        <div class="m-t-md">
                            <small class="text-muted">
                                <i class="fa fa-info-circle"></i>
                                输入企业信息进行查询
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-file-text"></i> 发票开具申请</h5>
                        <div class="ibox-tools">
                            <span class="label label-warning">测试</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="text-center">
                            <div class="m-b-md">
                                <i class="fa fa-file-text-o feature-icon-invoice"></i>
                            </div>
                            <p class="text-muted">提交发票开具申请，支持多种发票类型</p>
                            <div class="m-t-md">
                                <a href="/swgx/demo/invoice" class="btn btn-warning">
                                    <i class="fa fa-play"></i> 开始测试
                                </a>
                            </div>
                        </div>
                        <div class="m-t-md">
                            <small class="text-muted">
                                <i class="fa fa-info-circle"></i>
                                输入发票信息进行开具申请
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-code"></i> 通用API测试工具</h5>
                        <div class="ibox-tools">
                            <span class="label label-success">推荐</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="text-center">
                            <div class="m-b-md">
                                <i class="fa fa-cogs feature-icon-api"></i>
                            </div>
                            <p class="text-muted">通用API测试工具，支持测试所有SWGX模块的API接口，提供完整的请求和响应信息</p>
                            <div class="m-t-md">
                                <a href="/swgx/api-test" class="btn btn-success">
                                    <i class="fa fa-play"></i> 开始测试
                                </a>
                            </div>
                        </div>
                        <div class="m-t-md">
                            <small class="text-muted">
                                <i class="fa fa-info-circle"></i>
                                支持所有HTTP方法，提供请求历史和模板功能
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-book"></i> 接口文档</h5>
                        <div class="ibox-tools">
                            <span class="label label-info">外部</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="text-center">
                            <div class="m-b-md">
                                <i class="fa fa-file-text-o feature-icon-doc"></i>
                            </div>
                            <p class="text-muted">查看完整的API接口文档，包括请求参数、响应格式和示例代码</p>
                            <div class="m-t-md">
                                <a href="https://baiwangjs.apifox.cn/" class="btn btn-info" target="_blank" rel="noopener">
                                    <i class="fa fa-external-link"></i> 查看文档
                                </a>
                            </div>
                        </div>
                        <div class="m-t-md">
                            <small class="text-muted">
                                <i class="fa fa-info-circle"></i>
                                详细的API接口说明和使用示例
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API工具与实用功能 -->
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-wrench"></i> API工具与实用功能</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="widget style1 navy-bg">
                                    <div class="row">
                                        <div class="col-xs-4">
                                            <i class="fa fa-calculator fa-3x"></i>
                                        </div>
                                        <div class="col-xs-8 text-right">
                                            <span>订单号生成</span>
                                            <h3 class="font-bold">智能生成</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="widget style1 yellow-bg">
                                    <div class="row">
                                        <div class="col-xs-4">
                                            <i class="fa fa-money fa-3x"></i>
                                        </div>
                                        <div class="col-xs-8 text-right">
                                            <span>税额计算</span>
                                            <h3 class="font-bold">自动计算</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="widget style1 red-bg">
                                    <div class="row">
                                        <div class="col-xs-4">
                                            <i class="fa fa-shield fa-3x"></i>
                                        </div>
                                        <div class="col-xs-8 text-right">
                                            <span>签名验证</span>
                                            <h3 class="font-bold">HMAC-SHA256</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center m-t-lg">
                            <a href="/swgx/demo/utils" class="btn btn-primary btn-lg">
                                <i class="fa fa-wrench"></i> 工具测试
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script th:src="@{/js/swgx-test.js}"></script>
    <script>
        $(document).ready(function() {
            // 折叠面板事件
            $('.collapse-link').on('click', function() {
                var ibox = $(this).closest('.ibox');
                var content = ibox.find('.ibox-content');
                var icon = $(this).find('i');

                content.slideToggle(200);
                icon.toggleClass('fa-chevron-up').toggleClass('fa-chevron-down');
            });
        });

        function quickTest(type) {
            var url = '';
            var title = '';

            switch(type) {
                case 'company':
                    // 企业查询需要参数，跳转到专门页面
                    window.location.href = '/swgx/demo/company';
                    return;
                case 'invoice':
                    url = '/swgx/demo/quickApplyInvoice';
                    title = '快速发票开具';
                    break;
                case 'utils':
                    url = '/swgx/demo/utilsApi';
                    title = '工具演示';
                    break;
            }

            if (url && typeof SwgxTest !== 'undefined') {
                SwgxTest.request({
                    url: url,
                    type: 'POST',
                    onSuccess: function(data) {
                        layer.open({
                            title: title + ' - 测试结果',
                            type: 1,
                            area: ['600px', '400px'],
                            content: '<div style="padding: 20px;"><pre>' + SwgxTest.formatJson(data) + '</pre></div>'
                        });
                    },
                    onError: function(message) {
                        layer.msg('测试失败: ' + message, {icon: 2});
                    }
                });
            }
        }
    </script>
</body>
</html>
