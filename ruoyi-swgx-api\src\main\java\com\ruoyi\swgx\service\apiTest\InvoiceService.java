package com.ruoyi.swgx.service.apiTest;

import com.ruoyi.swgx.common.client.SwgxHttpClient;
import com.ruoyi.swgx.common.constants.SwgxApiConstants;
import com.ruoyi.swgx.common.dto.SwgxApiResponse;
import com.ruoyi.swgx.common.dto.invoice.ApplyInvoiceRequest;
import com.ruoyi.swgx.common.dto.invoice.ApplyInvoiceResponse;
import com.ruoyi.swgx.common.dto.invoice.InvoiceCallbackData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 发票相关服务
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class InvoiceService {

    @Autowired
    private SwgxHttpClient httpClient;

    @Autowired
    private CompanyConfigService companyConfigService;

    /**
     * 发票开具申请(V2)
     * 支持税控增值税专用发票、增值税普通发票、增值税普通发票（卷式）、增值税电子普通发票、增值税电子专用发票的开具，
     * 另外包括税控红字发票开具；支持数电票（增值税专用发票）、数电票（普通发票）、数电纸质发票等的开具以及特殊票种开具；
     * 该接口暂不支持数电红字发票开具
     * 
     * @param request 发票开具申请请求
     * @return 发票开具申请响应
     */
    public ApplyInvoiceResponse applyInvoice(ApplyInvoiceRequest request) {
        log.info("发票开具申请: ddlsh={}, fplxdm={}, gmfMc={}", 
                request.getDdlsh(), request.getFplxdm(), request.getGmfMc());

        // 验证企业唯一标识
        validateCompanyUniqueId(request);

        // 发送请求
        SwgxApiResponse<ApplyInvoiceResponse> response = httpClient.post(
            SwgxApiConstants.ApiPaths.APPLY_INVOICE_V2, 
            request, 
            ApplyInvoiceResponse.class
        );

        ApplyInvoiceResponse result = response.getData();
        log.info("发票开具申请成功: ddlsh={}, jfmUrl={}", request.getDdlsh(), result.getJfmUrl());
        
        return result;
    }

    /**
     * 快速发票开具申请
     * 自动获取企业唯一标识，简化调用流程
     *
     * @param request 发票开具申请请求（qyId可以为空）
     * @return 发票开具申请响应
     */
    public ApplyInvoiceResponse quickApplyInvoice(ApplyInvoiceRequest request) {
        // 如果没有企业唯一标识，则自动获取
        if (request.getQyId() == null || request.getQyId().trim().isEmpty()) {
            String qyId = companyConfigService.getCompanyUniqueId();
            request.setQyId(qyId);
            log.info("自动获取企业唯一标识: qyId={}", qyId);
        }

        return applyInvoice(request);
    }

    /**
     * 快速发票开具申请（兼容旧版本）
     *
     * @param request 发票开具申请请求
     * @param nsrsbh 纳税人识别号（已废弃，系统会使用配置中的企业信息）
     * @return 发票开具申请响应
     * @deprecated 使用 quickApplyInvoice(ApplyInvoiceRequest request) 替代
     */
    @Deprecated
    public ApplyInvoiceResponse quickApplyInvoice(ApplyInvoiceRequest request, String nsrsbh) {
        log.warn("使用了已废弃的方法 quickApplyInvoice(request, nsrsbh)，建议使用 quickApplyInvoice(request)");
        return quickApplyInvoice(request);
    }

    /**
     * 处理发票开具回调
     * 
     * @param callbackData 回调数据
     */
    public void handleInvoiceCallback(InvoiceCallbackData callbackData) {
        log.info("收到发票开具回调: ddlsh={}, status={}", 
                callbackData.getDdlsh(), callbackData.getStatus());

        // 根据状态处理回调
        switch (callbackData.getStatus()) {
            case SwgxApiConstants.InvoiceStatus.SUCCESS:
                handleInvoiceSuccess(callbackData);
                break;
            case SwgxApiConstants.InvoiceStatus.FAILED:
                handleInvoiceFailure(callbackData);
                break;
            case SwgxApiConstants.InvoiceStatus.PDF_PENDING:
                handleInvoicePdfPending(callbackData);
                break;
            case SwgxApiConstants.InvoiceStatus.NEED_LOGIN:
                handleInvoiceNeedLogin(callbackData);
                break;
            case SwgxApiConstants.InvoiceStatus.NEED_AUTH:
                handleInvoiceNeedAuth(callbackData);
                break;
            default:
                log.warn("未知的发票状态: {}", callbackData.getStatus());
        }
    }

    /**
     * 验证企业唯一标识
     */
    private void validateCompanyUniqueId(ApplyInvoiceRequest request) {
        if (request.getQyId() == null || request.getQyId().trim().isEmpty()) {
            throw new IllegalArgumentException("企业唯一标识不能为空，请先调用查询企业唯一标识接口获取");
        }
    }

    /**
     * 处理发票开具成功
     */
    private void handleInvoiceSuccess(InvoiceCallbackData callbackData) {
        log.info("发票开具成功: ddlsh={}, fphm={}, qdfphm={}", 
                callbackData.getDdlsh(), callbackData.getFphm(), callbackData.getQdfphm());
        
        // TODO: 这里可以实现具体的业务逻辑，比如：
        // 1. 更新订单状态
        // 2. 发送通知
        // 3. 记录日志
        // 4. 调用其他系统接口
    }

    /**
     * 处理发票开具失败
     */
    private void handleInvoiceFailure(InvoiceCallbackData callbackData) {
        log.error("发票开具失败: ddlsh={}, ycyy={}", 
                callbackData.getDdlsh(), callbackData.getYcyy());
        
        // TODO: 实现失败处理逻辑
    }

    /**
     * 处理发票PDF待生成
     */
    private void handleInvoicePdfPending(InvoiceCallbackData callbackData) {
        log.info("发票已开具，PDF待生成: ddlsh={}", callbackData.getDdlsh());
        
        // TODO: 实现PDF待生成处理逻辑
    }

    /**
     * 处理需要登录
     */
    private void handleInvoiceNeedLogin(InvoiceCallbackData callbackData) {
        log.warn("发票开具需要登录: ddlsh={}", callbackData.getDdlsh());
        
        // TODO: 实现需要登录处理逻辑
    }

    /**
     * 处理需要认证
     */
    private void handleInvoiceNeedAuth(InvoiceCallbackData callbackData) {
        log.warn("发票开具需要认证: ddlsh={}", callbackData.getDdlsh());
        
        // TODO: 实现需要认证处理逻辑
    }
}
