package com.ruoyi.swgx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 电票发票信息对象 swgx_dp_fpxx
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DpFpxx extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @NotBlank(message = "主键ID不能为空")
    @Size(max = 50, message = "主键ID长度不能超过50个字符")
    private String id;

    /** 企业ID */
    @Excel(name = "企业ID")
    @Size(max = 50, message = "企业ID长度不能超过50个字符")
    private String qyId;

    /** 税控设备号 */
    @Excel(name = "税控设备号")
    @Size(max = 40, message = "税控设备号长度不能超过40个字符")
    private String sksbh;

    /** 发票类型代码 */
    @Excel(name = "发票类型代码")
    @Size(max = 3, message = "发票类型代码长度不能超过3个字符")
    private String fplxdm;

    /** 是否为纸质发票(Y/N) */
    @Excel(name = "是否为纸质发票", readConverterExp = "Y=是,N=否")
    @Size(max = 10, message = "是否为纸质发票标识长度不能超过10个字符")
    private String sfwzzfp;

    /** 发票代码 */
    @Excel(name = "发票代码")
    @Size(max = 40, message = "发票代码长度不能超过40个字符")
    private String fpDm;

    /** 发票号码 */
    @Excel(name = "发票号码")
    @Size(max = 40, message = "发票号码长度不能超过40个字符")
    private String fpHm;

    /** 全电发票号码 */
    @Excel(name = "全电发票号码")
    @Size(max = 40, message = "全电发票号码长度不能超过40个字符")
    private String qdfphm;

    /** 发票状态 */
    @Excel(name = "发票状态", readConverterExp = "0=正常,1=作废,2=红冲,3=已开具")
    @NotNull(message = "发票状态不能为空")
    private Integer fpzt;

    /** 上传标志 */
    @Excel(name = "上传标志", readConverterExp = "0=未上传,1=已上传")
    private Integer scbz;

    /** 开票日期 */
    @Excel(name = "开票日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Size(max = 30, message = "开票日期长度不能超过30个字符")
    private String kprq;

    /** 特殊票种 */
    @Excel(name = "特殊票种")
    @Size(max = 6, message = "特殊票种长度不能超过6个字符")
    private String tspz;

    /** 校验码 */
    @Size(max = 200, message = "校验码长度不能超过200个字符")
    private String jym;

    /** 税控码 */
    @Size(max = 500, message = "税控码长度不能超过500个字符")
    private String skm;

    /** 销售方纳税人识别号 */
    @Excel(name = "销售方纳税人识别号")
    @Size(max = 30, message = "销售方纳税人识别号长度不能超过30个字符")
    private String xsfNsrsbh;

    /** 销售方名称 */
    @Excel(name = "销售方名称")
    @Size(max = 200, message = "销售方名称长度不能超过200个字符")
    private String xsfMc;

    /** 销售方地址电话 */
    @Excel(name = "销售方地址电话")
    @Size(max = 200, message = "销售方地址电话长度不能超过200个字符")
    private String xsfDzdh;

    /** 销售方银行账号 */
    @Excel(name = "销售方银行账号")
    @Size(max = 200, message = "销售方银行账号长度不能超过200个字符")
    private String xsfYhzh;

    /** 购买方纳税人识别号 */
    @Excel(name = "购买方纳税人识别号")
    @Size(max = 30, message = "购买方纳税人识别号长度不能超过30个字符")
    private String gmfNsrsbh;

    /** 购买方名称 */
    @Excel(name = "购买方名称")
    @Size(max = 200, message = "购买方名称长度不能超过200个字符")
    private String gmfMc;

    /** 购买方地址电话 */
    @Excel(name = "购买方地址电话")
    @Size(max = 200, message = "购买方地址电话长度不能超过200个字符")
    private String gmfDzdh;

    /** 购买方银行账号 */
    @Excel(name = "购买方银行账号")
    @Size(max = 200, message = "购买方银行账号长度不能超过200个字符")
    private String gmfYhzh;

    /** 购买方联系方式 */
    @Excel(name = "购买方联系方式")
    @Size(max = 100, message = "购买方联系方式长度不能超过100个字符")
    private String gmfLxfs;

    /** 总行数量 */
    @Excel(name = "总行数量")
    private BigDecimal zhsl;

    /** 价税合计 */
    @Excel(name = "价税合计")
    private BigDecimal jshj;

    /** 合计金额(不含税) */
    @Excel(name = "合计金额")
    private BigDecimal hjje;

    /** 合计税额 */
    @Excel(name = "合计税额")
    private BigDecimal hjse;

    /** 开票人 */
    @Excel(name = "开票人")
    @Size(max = 50, message = "开票人长度不能超过50个字符")
    private String kpr;

    /** 收款人 */
    @Excel(name = "收款人")
    @Size(max = 50, message = "收款人长度不能超过50个字符")
    private String skr;

    /** 复核人 */
    @Excel(name = "复核人")
    @Size(max = 50, message = "复核人长度不能超过50个字符")
    private String fhr;

    /** 备注 */
    @Excel(name = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String bz;

    /** 加密版本号 */
    @Size(max = 50, message = "加密版本号长度不能超过50个字符")
    private String jmbbh;

    /** 主要商品名称 */
    @Excel(name = "主要商品名称")
    @Size(max = 200, message = "主要商品名称长度不能超过200个字符")
    private String zyspmc;

    /** 商品说明 */
    @Size(max = 500, message = "商品说明长度不能超过500个字符")
    private String spsm;

    /** 清单标志 */
    @Excel(name = "清单标志", readConverterExp = "0=否,1=是")
    private Integer qdbz;

    /** 所属月份 */
    @Excel(name = "所属月份")
    @Size(max = 20, message = "所属月份长度不能超过20个字符")
    private String ssyf;

    /** 开票机号 */
    @Size(max = 50, message = "开票机号长度不能超过50个字符")
    private String kpjh;

    /** 通知单编号 */
    @Size(max = 50, message = "通知单编号长度不能超过50个字符")
    private String tzdbh;

    /** 原发票代码 */
    @Size(max = 20, message = "原发票代码长度不能超过20个字符")
    private String yfpdm;

    /** 原发票号码 */
    @Size(max = 20, message = "原发票号码长度不能超过20个字符")
    private String yfphm;

    /** 原起始发票号码 */
    @Size(max = 20, message = "原起始发票号码长度不能超过20个字符")
    private String yqdfphm;

    /** 作废日期 */
    @Size(max = 20, message = "作废日期长度不能超过20个字符")
    private String zfrq;

    /** 作废人 */
    @Size(max = 50, message = "作废人长度不能超过50个字符")
    private String zfr;

    /** 签名次数 */
    private Integer qmcs;

    /** 签名值 */
    @Size(max = 500, message = "签名值长度不能超过500个字符")
    private String qmz;

    /** 应开发票金额 */
    @Excel(name = "应开发票金额")
    private BigDecimal ykfsje;

    /** 修复时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xfsj;

    /** 备用字段1 */
    private String by1;

    /** 备用字段2 */
    private String by2;

    /** 备用字段3 */
    private String by3;

    /** 纸票打印标记 */
    @Excel(name = "纸票打印标记", readConverterExp = "0=未打印,1=已打印")
    private Integer zpdybj;

    /** 打印人 */
    @Excel(name = "打印人")
    private String dyr;

    /** 打印时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dysj;

    /** 开票终端代码 */
    private String khdId;

    /** 发票来源 */
    @Excel(name = "发票来源", readConverterExp = "1=修复,2=订单开具")
    private Integer fply;

    /** 电票PDF */
    private String pdf;

    /** 关联订单表id */
    private String orderId;

    /** 征税方式 */
    @Excel(name = "征税方式", readConverterExp = "0=普通征税,1=减按征收,2=差额征税")
    private Integer zsfs;

    /** 采集状态 */
    @Excel(name = "采集状态", readConverterExp = "0=未采集,1=已采集")
    private Integer collectFlag;

    /** 扣除额 */
    private BigDecimal kce;

    /** 格式化标志 */
    @Excel(name = "格式化标志", readConverterExp = "0=未格式化,1=格式化")
    private Integer formatFlag;

    /** 所属代账公司id */
    private String ssdzgsid;

    /** 全电票标志 */
    @Excel(name = "全电票标志", readConverterExp = "0=否,1=是")
    private Integer elecBz;

    /** 申请人 */
    @Excel(name = "申请人")
    private String sqr;

    /** 订单来源 */
    private Integer ddly;

    /** 订单创建人代码 */
    private String sqrdm;

    /** 订单创建人部门代码 */
    private String sqrbmdm;

    /** 订单创建人部门名称 */
    private String sqrbmmc;

    /** 附注信息 */
    private String appendixContent;

    /** 会计凭证号 */
    private String kjpzh;

    /** 煤炭品质信息 */
    private String mtpzxx;

    /** 原订单来源 */
    private Integer yddly;

    /** 发票入账状态代码 */
    private String fprzztDm;

    /** 消费税用途代码 */
    private String xfsytDm;

    /** 增值税用途代码 */
    private String zzsytDm;

    /** 发票明细信息 */
    @Valid
    private List<DpFpxxMx> dpFpxxMxList;

    /**
     * 获取发票唯一标识（发票代码+发票号码）
     * 
     * @return 发票唯一标识
     */
    public String getInvoiceUniqueKey() {
        return fpDm + "_" + fpHm;
    }

    /**
     * 判断是否为增值税发票
     * 
     * @return true-是增值税发票，false-不是增值税发票
     */
    public boolean isVatInvoice() {
        return "Y".equals(sfwzzfp);
    }

    /**
     * 判断发票是否可以进行红冲
     * 
     * @return true-可以红冲，false-不可以红冲
     */
    public boolean canRedFlush() {
        // 只有正常状态的发票才能进行红冲
        return fpzt != null && fpzt == 0;
    }

    /**
     * 获取格式化的开票日期
     * 
     * @return 格式化后的开票日期
     */
    public String getFormattedKprq() {
        if (kprq != null && kprq.length() >= 8) {
            return kprq.substring(0, 4) + "-" + kprq.substring(4, 6) + "-" + kprq.substring(6, 8);
        }
        return kprq;
    }
}
