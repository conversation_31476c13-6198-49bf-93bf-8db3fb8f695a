<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改匹配规则配置')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-gz-edit" th:object="${ppGz}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">规则名称</label>
                    <div class="col-sm-8">
                        <input name="gzMc" th:field="*{gzMc}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">规则描述</label>
                    <div class="col-sm-8">
                        <textarea name="gzMs" class="form-control">[[*{gzMs}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">规则类型</label>
                    <div class="col-sm-8">
                        <select name="gzLx" class="form-control" th:with="type=${@dict.getType('swgx_match_rule_type')}" required>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{gzLx}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">规则状态</label>
                    <div class="col-sm-8">
                        <select name="gzZt" class="form-control" th:field="*{gzZt}" required>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">规则优先级</label>
                    <div class="col-sm-8">
                        <input name="gzYxj" th:field="*{gzYxj}" class="form-control" type="number" min="1" max="10" placeholder="1-10，数字越小优先级越高">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">规则内容(JSON格式)</label>
                    <div class="col-sm-8">
                        <textarea name="gzNr" id="jsonContent" class="form-control json-textarea" rows="10" required>[[*{gzNr}]]</textarea>
                        <div class="btn-group btn-group-sm" style="margin-top: 5px;">
                            <button type="button" class="btn btn-default" onclick="formatJson()">
                                <i class="fa fa-magic"></i> 格式化JSON
                            </button>
                            <button type="button" class="btn btn-default" onclick="validateJson()">
                                <i class="fa fa-check"></i> 验证JSON
                            </button>
                            <button type="button" class="btn btn-info" onclick="showJsonExample()">
                                <i class="fa fa-question-circle"></i> 示例
                            </button>
                        </div>
                        <small class="help-block">
                            <i class="fa fa-info-circle"></i>
                            请输入有效的JSON格式内容，包含匹配字段、权重、阈值等配置信息
                        </small>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <style>
        .json-textarea {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
        }
        .json-textarea:focus {
            background-color: #fff;
            border-color: #5cb85c;
        }
        .json-valid {
            border-color: #5cb85c !important;
            background-color: #f0fff0 !important;
        }
        .json-invalid {
            border-color: #d9534f !important;
            background-color: #fff5f5 !important;
        }
        .btn-group {
            margin-top: 5px;
        }
    </style>
    <script th:inline="javascript">
        var prefix = ctx + "swgx/gz";
        $("#form-gz-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                // 验证JSON格式
                if (!validateJson(false)) {
                    $.modal.alertWarning("规则内容JSON格式不正确，请检查后重试");
                    return;
                }
                $.operate.save(prefix + "/edit", $('#form-gz-edit').serialize());
            }
        }

        /**
         * 格式化JSON
         */
        function formatJson() {
            var textarea = document.getElementById('jsonContent');
            var content = textarea.value.trim();

            if (!content) {
                $.modal.alertWarning("请先输入JSON内容");
                return;
            }

            try {
                var jsonObj = JSON.parse(content);
                var formatted = JSON.stringify(jsonObj, null, 2);
                textarea.value = formatted;
                textarea.className = textarea.className.replace(/json-invalid/g, '') + ' json-valid';
                $.modal.msgSuccess("JSON格式化成功");
            } catch (e) {
                textarea.className = textarea.className.replace(/json-valid/g, '') + ' json-invalid';
                $.modal.alertError("JSON格式错误：" + e.message);
            }
        }

        /**
         * 验证JSON
         */
        function validateJson(showMessage = true) {
            var textarea = document.getElementById('jsonContent');
            var content = textarea.value.trim();

            if (!content) {
                if (showMessage) $.modal.alertWarning("请先输入JSON内容");
                return false;
            }

            try {
                JSON.parse(content);
                textarea.className = textarea.className.replace(/json-invalid/g, '') + ' json-valid';
                if (showMessage) $.modal.msgSuccess("JSON格式正确");
                return true;
            } catch (e) {
                textarea.className = textarea.className.replace(/json-valid/g, '') + ' json-invalid';
                if (showMessage) $.modal.alertError("JSON格式错误：" + e.message);
                return false;
            }
        }

        /**
         * 显示JSON示例
         */
        function showJsonExample() {
            var content = `
<div class="form-horizontal">
    <div class="form-group">
        <label class="col-sm-12"><strong>1. 精确匹配规则示例：</strong></label>
        <div class="col-sm-12">
            <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 11px;">{
  "fields": [
    {
      "name": "spmc",
      "weight": 0.4,
      "required": true,
      "matchType": "exact"
    },
    {
      "name": "dj",
      "weight": 0.3,
      "required": true,
      "matchType": "exact"
    },
    {
      "name": "slv",
      "weight": 0.3,
      "required": true,
      "matchType": "exact"
    }
  ],
  "threshold": 0.9,
  "description": "标准精确匹配规则"
}</pre>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-12"><strong>2. 模糊匹配规则示例：</strong></label>
        <div class="col-sm-12">
            <pre style="background-color: #f0f8ff; padding: 10px; border-radius: 4px; font-size: 11px;">{
  "fields": [
    {
      "name": "spmc",
      "weight": 0.35,
      "required": true,
      "matchType": "fuzzy",
      "similarity": 0.7
    },
    {
      "name": "dj",
      "weight": 0.35,
      "required": true,
      "matchType": "tolerance",
      "tolerance": 0.05
    },
    {
      "name": "slv",
      "weight": 0.2,
      "required": true,
      "matchType": "exact"
    },
    {
      "name": "spbm",
      "weight": 0.1,
      "required": false,
      "matchType": "exact"
    }
  ],
  "threshold": 0.75,
  "description": "综合模糊匹配规则"
}</pre>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-12"><strong>字段详细说明：</strong></label>
        <div class="col-sm-12">
            <table class="table table-bordered table-condensed" style="font-size: 12px; margin-top: 10px;">
                <thead>
                    <tr class="info">
                        <th width="15%">字段名</th>
                        <th width="20%">说明</th>
                        <th width="15%">数据类型</th>
                        <th width="50%">取值范围/说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>fields</code></td>
                        <td>匹配字段配置数组</td>
                        <td>Array</td>
                        <td>包含多个字段配置对象的数组</td>
                    </tr>
                    <tr>
                        <td><code>name</code></td>
                        <td>字段名称</td>
                        <td>String</td>
                        <td><strong>spmc</strong>-商品名称, <strong>dj</strong>-单价, <strong>slv</strong>-税率<br/>
                            <strong>spbm</strong>-商品编码, <strong>ggxh</strong>-规格型号, <strong>dw</strong>-单位</td>
                    </tr>
                    <tr>
                        <td><code>weight</code></td>
                        <td>字段权重</td>
                        <td>Number</td>
                        <td>0-1之间的小数，所有字段权重之和应为1.0</td>
                    </tr>
                    <tr>
                        <td><code>required</code></td>
                        <td>是否必填字段</td>
                        <td>Boolean</td>
                        <td><strong>true</strong>-必填, <strong>false</strong>-可选</td>
                    </tr>
                    <tr>
                        <td><code>matchType</code></td>
                        <td>匹配类型</td>
                        <td>String</td>
                        <td><strong>exact</strong>-精确匹配<br/>
                            <strong>fuzzy</strong>-模糊匹配<br/>
                            <strong>tolerance</strong>-容差匹配</td>
                    </tr>
                    <tr>
                        <td><code>similarity</code></td>
                        <td>相似度阈值</td>
                        <td>Number</td>
                        <td>仅用于fuzzy匹配，0-1之间，如0.7表示70%相似度</td>
                    </tr>
                    <tr>
                        <td><code>tolerance</code></td>
                        <td>容差比例</td>
                        <td>Number</td>
                        <td>仅用于tolerance匹配，如0.05表示允许5%误差</td>
                    </tr>
                    <tr>
                        <td><code>threshold</code></td>
                        <td>匹配阈值</td>
                        <td>Number</td>
                        <td>0-1之间，综合匹配得分超过此值才认为匹配成功</td>
                    </tr>
                    <tr>
                        <td><code>description</code></td>
                        <td>规则描述</td>
                        <td>String</td>
                        <td>规则的文字描述，便于理解和维护</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-12"><strong>配置建议：</strong></label>
        <div class="col-sm-12">
            <div class="alert alert-info" style="font-size: 12px; margin-top: 10px;">
                <ul style="margin-bottom: 0;">
                    <li><strong>权重分配</strong>：商品名称通常占30-40%，单价占25-35%，税率占20-30%</li>
                    <li><strong>必填字段</strong>：商品名称、单价、税率建议设为必填</li>
                    <li><strong>匹配阈值</strong>：精确匹配建议0.9以上，模糊匹配建议0.7-0.8</li>
                    <li><strong>模糊匹配</strong>：商品名称相似度建议0.6-0.8，价格容差建议0.05-0.1</li>
                </ul>
            </div>
        </div>
    </div>
</div>
            `;

            $.modal.open("JSON配置示例与说明", content, "900", "700");
        }

        // 页面加载完成后格式化现有的JSON内容
        $(document).ready(function() {
            var textarea = document.getElementById('jsonContent');
            if (textarea.value.trim()) {
                try {
                    var jsonObj = JSON.parse(textarea.value);
                    textarea.value = JSON.stringify(jsonObj, null, 2);
                    textarea.className += ' json-valid';
                } catch (e) {
                    textarea.className += ' json-invalid';
                }
            }
        });
    </script>
</body>
</html>