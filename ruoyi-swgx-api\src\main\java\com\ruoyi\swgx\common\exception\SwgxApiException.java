package com.ruoyi.swgx.common.exception;

/**
 * 百旺金穗云API异常基类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class SwgxApiException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误代码
     */
    private String code;

    /**
     * 错误原因
     */
    private String reason;

    public SwgxApiException() {
        super();
    }

    public SwgxApiException(String message) {
        super(message);
    }

    public SwgxApiException(String message, Throwable cause) {
        super(message, cause);
    }

    public SwgxApiException(String code, String message) {
        super(message);
        this.code = code;
    }

    public SwgxApiException(String code, String message, String reason) {
        super(message);
        this.code = code;
        this.reason = reason;
    }

    public SwgxApiException(String code, String message, String reason, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return "SwgxApiException{" +
                "code='" + code + '\'' +
                ", message='" + getMessage() + '\'' +
                ", reason='" + reason + '\'' +
                '}';
    }
}
