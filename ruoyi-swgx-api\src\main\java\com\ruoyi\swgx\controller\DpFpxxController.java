package com.ruoyi.swgx.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.swgx.domain.DpFpxx;
import com.ruoyi.swgx.domain.DpFpxxMx;
import com.ruoyi.swgx.service.IDpFpxxService;
import com.ruoyi.swgx.service.IMatchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 电票发票信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Controller
@RequestMapping("/swgx/dpFpxx")
@Validated
public class DpFpxxController extends BaseController {
    
    @Autowired
    private IDpFpxxService dpFpxxService;

    @Autowired
    private IMatchService matchService;

    private String prefix = "swgx/dpFpxx";

    /**
     * 电票发票信息管理页面
     */
    @GetMapping()
    public String dpFpxx() {
        return prefix + "/dpFpxx";
    }

    /**
     * 新增电票发票信息页面
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 修改电票发票信息页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        DpFpxx dpFpxx = dpFpxxService.selectDpFpxxById(id);
        mmap.put("dpFpxx", dpFpxx);
        return prefix + "/edit";
    }

    /**
     * 发票详情页面 (GET)
     */
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") String id, ModelMap mmap) {
        mmap.put("id", id);
        return prefix + "/detail";
    }

    /**
     * 发票详情页面 (POST)
     */
    @PostMapping("/detail/{id}")
    public String detailPost(@PathVariable("id") String id, ModelMap mmap) {
        mmap.put("id", id);
        return prefix + "/detail";
    }

    /**
     * 导入数据页面
     */
    @GetMapping("/importData")
    public String importData() {
        return prefix + "/importData";
    }

    /**
     * 查询发票信息列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(DpFpxx dpFpxx) {
        startPage();
        List<DpFpxx> list = dpFpxxService.selectDpFpxxList(dpFpxx);
        return getDataTable(list);
    }

    /**
     * 导出发票信息列表
     */
    @Log(title = "发票信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public void export(HttpServletResponse response, DpFpxx dpFpxx) {
        List<DpFpxx> list = dpFpxxService.exportInvoiceList(dpFpxx);
        ExcelUtil<DpFpxx> util = new ExcelUtil<>(DpFpxx.class);
        util.exportExcel(response, list, "发票信息数据");
    }

    /**
     * 获取发票信息详细信息
     */
    @GetMapping(value = "/getInfo/{id}")
    @ResponseBody
    public AjaxResult getInfo(@PathVariable("id") String id) {
        try {
            // 清理ID，去除可能的引号
            String cleanId = id.replaceAll("^\"|\"$", "");

            DpFpxx dpFpxx = dpFpxxService.selectDpFpxxById(cleanId);
            if (dpFpxx == null) {
                return AjaxResult.error("发票不存在，ID：" + cleanId);
            }
            return AjaxResult.success(dpFpxx);
        } catch (Exception e) {
            log.error("获取发票信息失败，ID：{}", id, e);
            return AjaxResult.error("获取发票信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据发票代码和号码查询发票信息
     */
    @GetMapping("/getByFpDmAndHm")
    @ResponseBody
    public AjaxResult getByFpDmAndHm(@RequestParam("fpDm") String fpDm, @RequestParam("fpHm") String fpHm) {
        DpFpxx dpFpxx = dpFpxxService.selectDpFpxxByFpDmAndHm(fpDm, fpHm);
        if (dpFpxx != null) {
            return AjaxResult.success("查询成功", dpFpxx);
        } else {
            return AjaxResult.error("未找到对应的发票信息");
        }
    }

    /**
     * 新增发票信息
     */
    @Log(title = "发票信息", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult add(DpFpxx dpFpxx) {
        try {
            dpFpxx.setCreateBy(getLoginName());

            // 预处理明细数据，确保必填字段有值
            preprocessDetailDataForAdd(dpFpxx);

            int result = dpFpxxService.insertDpFpxx(dpFpxx);
            return toAjax(result);
        } catch (Exception e) {
            log.error("新增发票信息失败", e);
            return AjaxResult.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 预处理新增明细数据，确保必填字段有值
     */
    private void preprocessDetailDataForAdd(DpFpxx dpFpxx) {
        if (dpFpxx.getDpFpxxMxList() != null) {
            // 为主表生成ID
            if (StringUtils.isEmpty(dpFpxx.getId())) {
                dpFpxx.setId(IdUtils.fastSimpleUUID());
            }

            for (int i = 0; i < dpFpxx.getDpFpxxMxList().size(); i++) {
                DpFpxxMx mx = dpFpxx.getDpFpxxMxList().get(i);

                // 生成明细ID
                if (StringUtils.isEmpty(mx.getId())) {
                    mx.setId(IdUtils.fastSimpleUUID());
                }

                // 设置发票ID
                if (StringUtils.isEmpty(mx.getFpid())) {
                    mx.setFpid(dpFpxx.getId());
                }

                // 设置企业ID
                if (StringUtils.isEmpty(mx.getQyId())) {
                    mx.setQyId(dpFpxx.getQyId());
                }

                // 设置序列号
                if (mx.getSerialNumber() == null) {
                    mx.setSerialNumber(i + 1);
                }

                // 设置创建时间
                mx.setCreateTime(DateUtils.getNowDate());
                mx.setCreateBy(getLoginName());
            }
        }
    }

    /**
     * 修改发票信息
     */
    @Log(title = "发票信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(DpFpxx dpFpxx) {
        try {
            dpFpxx.setUpdateBy(getLoginName());

            // 预处理明细数据，确保必填字段有值
            preprocessDetailData(dpFpxx);



            int result = dpFpxxService.updateDpFpxx(dpFpxx);
            return toAjax(result);
        } catch (Exception e) {
            log.error("修改发票信息失败", e);
            return AjaxResult.error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 预处理明细数据，确保必填字段有值
     */
    private void preprocessDetailData(DpFpxx dpFpxx) {
        if (dpFpxx.getDpFpxxMxList() != null) {
            for (int i = 0; i < dpFpxx.getDpFpxxMxList().size(); i++) {
                DpFpxxMx mx = dpFpxx.getDpFpxxMxList().get(i);

                // 如果ID为空，生成新的ID
                if (StringUtils.isEmpty(mx.getId())) {
                    mx.setId(IdUtils.fastSimpleUUID());
                }

                // 设置发票ID
                if (StringUtils.isEmpty(mx.getFpid())) {
                    mx.setFpid(dpFpxx.getId());
                }

                // 设置企业ID
                if (StringUtils.isEmpty(mx.getQyId())) {
                    mx.setQyId(dpFpxx.getQyId());
                }

                // 设置序列号
                if (mx.getSerialNumber() == null) {
                    mx.setSerialNumber(i + 1);
                }

                // 设置创建时间和更新时间
                if (mx.getCreateTime() == null) {
                    mx.setCreateTime(DateUtils.getNowDate());
                }
                mx.setUpdateTime(DateUtils.getNowDate());
                mx.setUpdateBy(getLoginName());
            }
        }
    }

    /**
     * 更新发票状态
     */
    @Log(title = "发票状态", businessType = BusinessType.UPDATE)
    @PostMapping("/status")
    @ResponseBody
    public AjaxResult updateStatus(@RequestParam("id") String id, @RequestParam("fpzt") Integer fpzt) {
        try {
            int result = dpFpxxService.updateInvoiceStatus(id, fpzt, getLoginName());
            return toAjax(result);
        } catch (Exception e) {
            log.error("更新发票状态失败", e);
            return AjaxResult.error("更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 删除发票信息
     */
    @Log(title = "发票信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(@RequestParam String ids) {
        String[] idArray = ids.split(",");
        try {
            int result = dpFpxxService.deleteDpFpxxByIds(idArray);
            return toAjax(result);
        } catch (Exception e) {
            log.error("删除发票信息失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 查询发票明细信息
     */
    @GetMapping("/detailList/{fpid}")
    @ResponseBody
    public AjaxResult getDetailList(@PathVariable("fpid") String fpid) {
        try {
            // 清理ID，去除可能的引号
            String cleanId = fpid.replaceAll("^\"|\"$", "");

            List<DpFpxxMx> list = dpFpxxService.selectDpFpxxMxByFpid(cleanId);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询发票明细失败，ID：{}", fpid, e);
            return AjaxResult.error("查询明细失败：" + e.getMessage());
        }
    }

    /**
     * 根据商品名称查询发票明细
     */
    @GetMapping("/detailBySpmc")
    @ResponseBody
    public AjaxResult getDetailBySpmc(@RequestParam("spmc") String spmc, @RequestParam("qyId") String qyId) {
        List<DpFpxxMx> list = dpFpxxService.selectDpFpxxMxBySpmc(spmc, qyId);
        return AjaxResult.success(list);
    }

    /**
     * 根据商品编码查询发票明细
     */
    @GetMapping("/detailBySpbm")
    @ResponseBody
    public AjaxResult getDetailBySpbm(@RequestParam("spbm") String spbm, @RequestParam("qyId") String qyId) {
        List<DpFpxxMx> list = dpFpxxService.selectDpFpxxMxBySpbm(spbm, qyId);
        return AjaxResult.success(list);
    }

    /**
     * 统计发票数量按状态分组
     */
    @GetMapping("/statistics/status")
    @ResponseBody
    public AjaxResult getStatusStatistics(@RequestParam("qyId") String qyId) {
        List<Map<String, Object>> statistics = dpFpxxService.countInvoiceByStatus(qyId);
        return AjaxResult.success(statistics);
    }

    /**
     * 统计发票金额汇总
     */
    @GetMapping("/statistics/amount")
    @ResponseBody
    public AjaxResult getAmountStatistics(@RequestParam("qyId") String qyId,
                                         @RequestParam(value = "startDate", required = false) String startDate,
                                         @RequestParam(value = "endDate", required = false) String endDate) {
        Map<String, Object> statistics = dpFpxxService.sumInvoiceAmount(qyId, startDate, endDate);
        return AjaxResult.success(statistics);
    }

    /**
     * 查询商品统计信息
     */
    @GetMapping("/statistics/product")
    @ResponseBody
    public AjaxResult getProductStatistics(@RequestParam("qyId") String qyId,
                                          @RequestParam(value = "startDate", required = false) String startDate,
                                          @RequestParam(value = "endDate", required = false) String endDate) {
        List<Map<String, Object>> statistics = dpFpxxService.selectProductStatistics(qyId, startDate, endDate);
        return AjaxResult.success(statistics);
    }

    /**
     * 批量导入发票信息
     */
    @Log(title = "发票信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<DpFpxx> util = new ExcelUtil<>(DpFpxx.class);
        List<DpFpxx> invoiceList = util.importExcel(file.getInputStream());

        Map<String, Object> result = dpFpxxService.batchImportInvoices(invoiceList, updateSupport);

        String msg = String.format("导入结果：总数 %d，成功 %d，更新 %d，失败 %d",
                result.get("total"), result.get("successCount"),
                result.get("updateCount"), result.get("failCount"));

        return AjaxResult.success(msg, result);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DpFpxx> util = new ExcelUtil<>(DpFpxx.class);
        util.importTemplateExcel(response, "发票信息数据");
    }

    /**
     * 检查发票是否已存在
     */
    @GetMapping("/checkExists")
    @ResponseBody
    public AjaxResult checkExists(@RequestParam("fpDm") String fpDm,
                                 @RequestParam("fpHm") String fpHm,
                                 @RequestParam(value = "excludeId", required = false) String excludeId) {
        boolean exists = dpFpxxService.checkInvoiceExists(fpDm, fpHm, excludeId);
        return AjaxResult.success(exists);
    }

    /**
     * 初始化电票明细匹配状态
     */
    @RequiresPermissions("swgx:dpFpxx:init")
    @Log(title = "初始化电票明细匹配状态", businessType = BusinessType.OTHER)
    @PostMapping("/initInvoiceDetailMatchStatus")
    @ResponseBody
    public AjaxResult initInvoiceDetailMatchStatus()
    {
        try {
            matchService.initInvoiceDetailMatchStatus();
            return AjaxResult.success("电票明细匹配状态初始化完成");
        } catch (Exception e) {
            log.error("初始化电票明细匹配状态失败", e);
            return AjaxResult.error("初始化电票明细匹配状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取发票匹配信息
     */
    @GetMapping("/getMatchInfo/{id}")
    @ResponseBody
    public AjaxResult getMatchInfo(@PathVariable("id") String id) {
        try {
            String cleanId = id.replaceAll("^\"|\"$", "");
            Map<String, Object> matchInfo = dpFpxxService.getInvoiceMatchInfo(cleanId);
            return AjaxResult.success(matchInfo);
        } catch (Exception e) {
            log.error("获取发票匹配信息失败，ID：{}", id, e);
            return AjaxResult.error("获取匹配信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取明细匹配详情
     */
    @GetMapping("/getDetailMatchInfo/{detailId}")
    @ResponseBody
    public AjaxResult getDetailMatchInfo(@PathVariable("detailId") String detailId) {
        try {
            String cleanId = detailId.replaceAll("^\"|\"$", "");
            Map<String, Object> detailMatchInfo = dpFpxxService.getDetailMatchInfo(cleanId);
            return AjaxResult.success(detailMatchInfo);
        } catch (Exception e) {
            log.error("获取明细匹配详情失败，ID：{}", detailId, e);
            return AjaxResult.error("获取明细匹配详情失败：" + e.getMessage());
        }
    }
}
