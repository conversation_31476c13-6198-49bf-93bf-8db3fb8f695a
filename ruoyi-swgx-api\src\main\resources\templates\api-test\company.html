<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业唯一标识查询测试 - 河北九赋</title>
    <link rel="shortcut icon" th:href="@{/img/logo.jpg}">
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/animate.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/swgx-test.css}" rel="stylesheet"/>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <!-- 返回按钮 -->
        <div class="row">
            <div class="col-lg-12">
                <a href="/swgx/demo/test" class="btn btn-outline btn-sm">
                    <i class="fa fa-arrow-left"></i> 返回测试中心
                </a>
            </div>
        </div>

        <!-- 页面标题 -->
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-building"></i> 企业唯一标识查询测试</h5>
                        <div class="ibox-tools">
                            <span class="label label-success">可用</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i>
                            <strong>功能说明：</strong>通过企业名称和纳税人识别号查询企业唯一标识，查询结果会自动缓存24小时。
                        </div>

                        <form id="companyForm" class="form-horizontal">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">企业名称 <span class="text-danger">*</span></label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="nsrmc" name="nsrmc"
                                           placeholder="请输入企业名称">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">纳税人识别号 <span class="text-danger">*</span></label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="nsrsbh" name="nsrsbh"
                                           placeholder="请输入纳税人识别号">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <button type="button" class="btn btn-primary" onclick="queryCompany()">
                                        <i class="fa fa-search"></i> 查询企业唯一标识
                                    </button>
                                    <button type="button" class="btn btn-white" onclick="clearForm()">
                                        <i class="fa fa-refresh"></i> 清空表单
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div id="resultContainer" class="hidden">
                            <div class="alert alert-success" id="resultCard">
                                <h4><i class="fa fa-check-circle"></i> 查询结果</h4>
                                <div id="resultContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-book"></i> 使用说明</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-md-6">
                                <h4><i class="fa fa-cog"></i> 功能特性</h4>
                                <ul class="list-unstyled">
                                    <li><i class="fa fa-check text-success"></i> 支持企业名称和纳税人识别号查询</li>
                                    <li><i class="fa fa-check text-success"></i> 查询结果自动缓存24小时</li>
                                    <li><i class="fa fa-check text-success"></i> HMAC-SHA256安全签名认证</li>
                                    <li><i class="fa fa-check text-success"></i> 完善的错误处理机制</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h4><i class="fa fa-code"></i> API接口信息</h4>
                                <table class="table table-bordered">
                                    <tr>
                                        <td><strong>接口地址</strong></td>
                                        <td><code>/swgx/demo/queryCompany</code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>请求方式</strong></td>
                                        <td><span class="label label-primary">POST</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>返回格式</strong></td>
                                        <td><span class="label label-info">JSON</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>缓存时间</strong></td>
                                        <td><span class="label label-success">24小时</span></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script th:src="@{/js/swgx-test.js}"></script>
    <script>
        $(document).ready(function() {
            // 折叠面板事件
            $('.collapse-link').on('click', function() {
                var ibox = $(this).closest('.ibox');
                var content = ibox.find('.ibox-content');
                var icon = $(this).find('i');

                content.slideToggle(200);
                icon.toggleClass('fa-chevron-up').toggleClass('fa-chevron-down');
            });
        });

        function queryCompany() {
            // 简单表单验证
            var nsrmc = $('#nsrmc').val().trim();
            var nsrsbh = $('#nsrsbh').val().trim();

            if (!nsrmc) {
                layer.msg('请输入企业名称', {icon: 2});
                return;
            }
            if (!nsrsbh) {
                layer.msg('请输入纳税人识别号', {icon: 2});
                return;
            }

            // 显示加载状态
            var loadingIndex = layer.load(1, {shade: [0.1, '#fff']});

            $.ajax({
                url: '/swgx/demo/queryCompany',
                type: 'POST',
                data: {
                    nsrmc: nsrmc,
                    nsrsbh: nsrsbh
                },
                success: function(data) {
                    layer.close(loadingIndex);
                    showResult(data, true);
                    layer.msg('查询成功', {icon: 1});
                },
                error: function(xhr) {
                    layer.close(loadingIndex);
                    var message = xhr.responseJSON ? xhr.responseJSON.msg : '查询失败';
                    showResult(message, false);
                    layer.msg('查询失败: ' + message, {icon: 2});
                }
            });
        }

        function showResult(data, isSuccess) {
            var $container = $('#resultContainer');
            var $card = $('#resultCard');
            var $content = $('#resultContent');

            if (isSuccess) {
                $card.removeClass('alert-danger').addClass('alert-success');
                $card.find('h4').html('<i class="fa fa-check-circle"></i> 查询结果');
                $content.html('<pre class="code-block">' + JSON.stringify(data, null, 2) + '</pre>');
            } else {
                $card.removeClass('alert-success').addClass('alert-danger');
                $card.find('h4').html('<i class="fa fa-times-circle"></i> 查询失败');
                $content.html('<p class="text-danger">' + data + '</p>');
            }

            $container.removeClass('hidden').show();
        }

        function clearForm() {
            $('#nsrmc').val('');
            $('#nsrsbh').val('');
            $('#resultContainer').addClass('hidden').hide();
        }
    </script>
</body>
</html>
