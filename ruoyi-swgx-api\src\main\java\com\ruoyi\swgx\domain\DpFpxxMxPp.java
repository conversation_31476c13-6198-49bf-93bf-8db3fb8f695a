package com.ruoyi.swgx.domain;

import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 电票明细匹配状态对象 swgx_dp_fpxx_mx_pp
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
public class DpFpxxMxPp {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 发票明细ID */
    private String fpmxId;

    /** 剩余可匹配数量 */
    @Excel(name = "剩余可匹配数量")
    private BigDecimal sySl;

    /** 剩余可匹配金额 */
    @Excel(name = "剩余可匹配金额")
    private BigDecimal syJe;

    /** 剩余可匹配税额 */
    @Excel(name = "剩余可匹配税额")
    private BigDecimal sySe;

    /** 匹配次数 */
    @Excel(name = "匹配次数")
    private Integer ppCs;

    /** 匹配状态：0-未匹配，1-部分匹配，2-完全匹配 */
    @Excel(name = "匹配状态", readConverterExp = "0=未匹配,1=部分匹配,2=完全匹配")
    private String ppZt;

    /** 最后匹配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后匹配时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastPpTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
