<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票开具申请测试 - 河北九赋</title>
    <link rel="shortcut icon" th:href="@{/img/logo.jpg}">
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/animate.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/swgx-test.css}" rel="stylesheet"/>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <!-- 返回按钮 -->
        <div class="row">
            <div class="col-lg-12">
                <a href="/swgx/demo/test" class="btn btn-outline btn-sm">
                    <i class="fa fa-arrow-left"></i> 返回测试中心
                </a>
            </div>
        </div>

        <!-- 页面标题 -->
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-file-text"></i> 发票开具申请测试</h5>
                        <div class="ibox-tools">
                            <span class="label label-warning">测试</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i>
                            <strong>注意：</strong>发票开具是异步操作，提交申请后会返回交付码URL，实际开具结果需要通过回调接口获取。
                        </div>

                        <!-- 一键填充按钮 -->
                        <div class="text-center m-b-md">
                            <button type="button" class="btn btn-info" onclick="fillTestData()">
                                <i class="fa fa-magic"></i> 一键填充测试数据
                            </button>
                        </div>

                        <!-- 发票开具表单 -->
                        <form id="invoiceForm">
                            <!-- 基本信息 -->
                            <div class="ibox float-e-margins">
                                <div class="ibox-title">
                                    <h5><i class="fa fa-info-circle"></i> 基本信息</h5>
                                    <div class="ibox-tools">
                                        <a class="collapse-link">
                                            <i class="fa fa-chevron-up"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="ibox-content">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>发票类型 <span class="text-danger">*</span></label>
                                                <select class="form-control" id="fplxdm" name="fplxdm" title="发票类型">
                                                    <option value="02" selected>数电普票</option>
                                                    <option value="01">数电专票</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>开票类型 <span class="text-danger">*</span></label>
                                                <select class="form-control" id="kplx" name="kplx" title="开票类型">
                                                    <option value="0" selected>蓝字发票</option>
                                                    <option value="1">红字发票</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>局端登录账号 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="dlzh" name="dlzh" placeholder="开票员登录电子税务局的账号">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 购买方信息 -->
                            <div class="ibox float-e-margins">
                                <div class="ibox-title">
                                    <h5><i class="fa fa-user"></i> 购买方信息</h5>
                                    <div class="ibox-tools">
                                        <a class="collapse-link">
                                            <i class="fa fa-chevron-up"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="ibox-content">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>购买方名称 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="gmfMc" name="gmfMc" placeholder="请输入购买方名称">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>购买方纳税人识别号</label>
                                                <input type="text" class="form-control" id="gmfNsrsbh" name="gmfNsrsbh" placeholder="请输入纳税人识别号">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>购买方地址</label>
                                                <input type="text" class="form-control" id="gmfDz" name="gmfDz" placeholder="请输入购买方地址">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>购买方电话</label>
                                                <input type="text" class="form-control" id="gmfDh" name="gmfDh" placeholder="请输入购买方电话">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>购买方银行</label>
                                                <input type="text" class="form-control" id="gmfYh" name="gmfYh" placeholder="请输入购买方银行">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>购买方账号</label>
                                                <input type="text" class="form-control" id="gmfZh" name="gmfZh" placeholder="请输入购买方账号">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>购买方手机号</label>
                                                <input type="text" class="form-control" id="gmfMobile" name="gmfMobile" placeholder="用于发送PDF">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 发票明细 -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4><i class="fa fa-list"></i> 发票明细</h4>
                                </div>
                                <div class="panel-body">
                                    <div id="invoiceDetails">
                                        <div class="invoice-detail-item" data-index="0">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>商品名称 <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" name="spmc" placeholder="请输入商品名称">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>商品编码 <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" name="spbm" value="3040407990000000000" placeholder="商品编码">
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label>数量</label>
                                                        <input type="number" class="form-control" name="sl" value="1" step="0.01" placeholder="数量">
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label>单价</label>
                                                        <input type="number" class="form-control" name="dj" step="0.01" placeholder="单价">
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label>税率 <span class="text-danger">*</span></label>
                                                        <select class="form-control" name="slv" title="税率">
                                                            <option value="0.13" selected>13%</option>
                                                            <option value="0.09">9%</option>
                                                            <option value="0.06">6%</option>
                                                            <option value="0.03">3%</option>
                                                            <option value="0">0%</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>规格型号</label>
                                                        <input type="text" class="form-control" name="ggxh" placeholder="规格型号">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>单位</label>
                                                        <input type="text" class="form-control" name="dw" placeholder="单位">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>金额 <span class="text-danger">*</span></label>
                                                        <input type="number" class="form-control" name="je" step="0.01" placeholder="不含税金额">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>操作</label>
                                                        <div>
                                                            <button type="button" class="btn btn-sm btn-success" onclick="addInvoiceDetail()">
                                                                <i class="fa fa-plus"></i> 添加
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-danger hidden" onclick="removeInvoiceDetail(0)">
                                                                <i class="fa fa-minus"></i> 删除
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 开票人员信息 -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4><i class="fa fa-users"></i> 开票人员信息</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>开票人 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="kpr" name="kpr" value="系统开票" placeholder="开票人">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>申请人 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="sqr" name="sqr" value="系统申请" placeholder="申请人">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>收款人</label>
                                                <input type="text" class="form-control" id="skr" name="skr" placeholder="收款人">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>复核人</label>
                                                <input type="text" class="form-control" id="fhr" name="fhr" placeholder="复核人">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 备注信息 -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4><i class="fa fa-comment"></i> 备注信息</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="form-group">
                                        <label>备注</label>
                                        <textarea class="form-control" id="bz" name="bz" rows="3" placeholder="备注信息（最多200个汉字）"></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="button" class="test-btn success" onclick="submitInvoice()">
                                    <i class="fa fa-file-text"></i> 开具发票
                                </button>
                                <button type="button" class="test-btn secondary" onclick="clearInvoiceForm()">
                                    <i class="fa fa-refresh"></i> 清空表单
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div id="resultContainer" class="hidden">
                    <div class="result-card" id="resultCard">
                        <h4><i class="fa fa-check-circle text-success"></i> 申请结果</h4>
                        <div id="resultContent"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="test-card animated fadeInUp">
            <div class="test-card-header info-header">
                <h4><i class="fa fa-book"></i> 使用说明</h4>
            </div>
            <div class="test-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fa fa-file-text"></i> 表单说明</h5>
                        <ul>
                            <li><span class="text-danger">*</span> 标记的字段为必填项</li>
                            <li>系统自动获取企业唯一标识</li>
                            <li>销售方信息从系统配置获取</li>
                            <li>支持多条发票明细</li>
                            <li>支持数电普票和数电专票</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fa fa-cogs"></i> 操作说明</h5>
                        <ul>
                            <li>点击"一键填充测试数据"快速填充表单</li>
                            <li>填写购买方信息和发票明细</li>
                            <li>点击"添加"按钮增加明细行</li>
                            <li>点击"删除"按钮移除明细行</li>
                            <li>点击"开具发票"提交申请</li>
                            <li>点击"清空表单"重置所有字段</li>
                        </ul>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fa fa-info-circle"></i> 重要提示</h5>
                        <ul>
                            <li>发票开具是异步操作</li>
                            <li>请通过回调接口获取开具结果</li>
                            <li>局端登录账号为开票员手机号</li>
                            <li>商品编码请使用标准编码</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fa fa-code"></i> API接口信息</h5>
                        <ul>
                            <li><strong>接口地址：</strong>/swgx/demo/applyInvoice</li>
                            <li><strong>请求方式：</strong>POST</li>
                            <li><strong>数据格式：</strong>JSON</li>
                            <li><strong>返回格式：</strong>JSON</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script th:src="@{/js/swgx-test.js}"></script>
    <script>
        var detailIndex = 0;

        function submitInvoice() {
            // 验证必填字段
            if (!validateInvoiceForm()) {
                return;
            }

            // 收集表单数据
            var formData = collectInvoiceFormData();

            SwgxTest.request({
                url: '/swgx/demo/applyInvoice',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                onSuccess: function(data) {
                    SwgxTest.showResult('resultContainer', data, true);
                    SwgxTest.showSuccess('发票开具申请成功');
                },
                onError: function(message) {
                    SwgxTest.showResult('resultContainer', message, false);
                    // 如果是配置错误，显示更详细的提示
                    if (message && message.includes('企业配置错误')) {
                        SwgxTest.showError('配置错误，请检查系统配置后重试');
                    }
                }
            });
        }

        function validateInvoiceForm() {
            var requiredFields = [
                {id: 'fplxdm', name: '发票类型'},
                {id: 'kplx', name: '开票类型'},
                {id: 'dlzh', name: '局端登录账号'},
                {id: 'gmfMc', name: '购买方名称'},
                {id: 'kpr', name: '开票人'},
                {id: 'sqr', name: '申请人'}
            ];

            for (var i = 0; i < requiredFields.length; i++) {
                var field = requiredFields[i];
                var value = $('#' + field.id).val().trim();
                if (!value) {
                    SwgxTest.showError(field.name + '不能为空');
                    $('#' + field.id).focus();
                    return false;
                }
            }

            // 验证发票明细
            var hasValidDetail = false;
            $('.invoice-detail-item').each(function() {
                var spmc = $(this).find('input[name="spmc"]').val().trim();
                var spbm = $(this).find('input[name="spbm"]').val().trim();
                var je = $(this).find('input[name="je"]').val();

                if (spmc && spbm && je) {
                    hasValidDetail = true;
                    return false; // break
                }
            });

            if (!hasValidDetail) {
                SwgxTest.showError('请至少填写一条完整的发票明细（商品名称、商品编码、金额）');
                return false;
            }

            return true;
        }

        function collectInvoiceFormData() {
            var formData = {};

            // 基本信息
            formData.fplxdm = $('#fplxdm').val() || '02';
            formData.kplx = $('#kplx').val() || '0';
            formData.dlzh = $('#dlzh').val() || '';

            // 购买方信息
            formData.gmfMc = $('#gmfMc').val() || '';
            formData.gmfNsrsbh = $('#gmfNsrsbh').val() || '';
            formData.gmfDz = $('#gmfDz').val() || '';
            formData.gmfDh = $('#gmfDh').val() || '';
            formData.gmfYh = $('#gmfYh').val() || '';
            formData.gmfZh = $('#gmfZh').val() || '';
            formData.gmfMobile = $('#gmfMobile').val() || '';

            // 开票人员信息
            formData.kpr = $('#kpr').val() || '';
            formData.sqr = $('#sqr').val() || '';
            formData.skr = $('#skr').val() || '';
            formData.fhr = $('#fhr').val() || '';

            // 备注
            formData.bz = $('#bz').val() || '';

            // 发票明细
            var details = [];
            $('.invoice-detail-item').each(function(index) {
                var detail = {};
                detail.xh = index + 1;
                detail.fphxz = '0'; // 正常行
                detail.spmc = $(this).find('input[name="spmc"]').val();
                detail.spbm = $(this).find('input[name="spbm"]').val();
                detail.ggxh = $(this).find('input[name="ggxh"]').val() || '';
                detail.dw = $(this).find('input[name="dw"]').val() || '';
                detail.sl = $(this).find('input[name="sl"]').val() || '1';
                detail.dj = $(this).find('input[name="dj"]').val() || '';
                detail.je = $(this).find('input[name="je"]').val();
                detail.slv = $(this).find('select[name="slv"]').val();
                detail.lslbs = '0'; // 正常税率

                if (detail.spmc && detail.spbm && detail.je) {
                    details.push(detail);
                }
            });

            formData.details = details;

            return formData;
        }

        function addInvoiceDetail() {
            detailIndex++;
            var template = $('.invoice-detail-item').first().clone();

            // 清空表单值
            template.find('input').val('');
            template.find('select').val('0.13');
            template.find('input[name="sl"]').val('1');
            template.find('input[name="spbm"]').val('3040407990000000000');

            // 更新索引
            template.attr('data-index', detailIndex);

            // 显示删除按钮
            template.find('.btn-danger').show().attr('onclick', 'removeInvoiceDetail(' + detailIndex + ')');

            $('#invoiceDetails').append(template);
        }

        function removeInvoiceDetail(index) {
            $('.invoice-detail-item[data-index="' + index + '"]').remove();

            // 如果只剩一个明细，隐藏删除按钮
            if ($('.invoice-detail-item').length === 1) {
                $('.invoice-detail-item .btn-danger').hide();
            }
        }

        function clearInvoiceForm() {
            $('#invoiceForm')[0].reset();

            // 重置为默认值
            $('#fplxdm').val('02');
            $('#kplx').val('0');
            $('#kpr').val('系统开票');
            $('#sqr').val('系统申请');

            // 只保留第一个明细项
            $('.invoice-detail-item').not(':first').remove();
            $('.invoice-detail-item').first().find('input').val('');
            $('.invoice-detail-item').first().find('select[name="slv"]').val('0.13');
            $('.invoice-detail-item').first().find('input[name="sl"]').val('1');
            $('.invoice-detail-item').first().find('input[name="spbm"]').val('3040407990000000000');
            $('.invoice-detail-item .btn-danger').hide();

            // 隐藏结果
            $('#resultContainer').addClass('hidden').hide();

            detailIndex = 0;
        }

        function fillTestData() {
            // 基本信息
            $('#fplxdm').val('02'); // 数电普票
            $('#kplx').val('0'); // 蓝字发票
            $('#dlzh').val('13800000001'); // 测试局端登录账号

            // 购买方信息
            $('#gmfMc').val('北京测试科技有限公司');
            $('#gmfNsrsbh').val('91110000123456789X');
            $('#gmfDz').val('北京市朝阳区测试大街123号');
            $('#gmfDh').val('010-12345678');
            $('#gmfYh').val('中国银行北京分行');
            $('#gmfZh').val('1234567890123456789');
            $('#gmfMobile').val('13800000001');

            // 开票人员信息
            $('#kpr').val('系统开票');
            $('#sqr').val('系统申请');
            $('#skr').val('张三');
            $('#fhr').val('李四');

            // 备注信息
            $('#bz').val('测试发票开具，仅用于系统测试');

            // 清空现有明细，只保留第一个
            $('.invoice-detail-item').not(':first').remove();
            $('.invoice-detail-item .btn-danger').hide();
            detailIndex = 0;

            // 填充第一个明细项
            var firstDetail = $('.invoice-detail-item').first();
            firstDetail.find('input[name="spmc"]').val('软件开发服务');
            firstDetail.find('input[name="spbm"]').val('3040407990000000000');
            firstDetail.find('input[name="ggxh"]').val('标准版');
            firstDetail.find('input[name="dw"]').val('项');
            firstDetail.find('input[name="sl"]').val('1');
            firstDetail.find('input[name="dj"]').val('10000.00');
            firstDetail.find('input[name="je"]').val('10000.00');
            firstDetail.find('select[name="slv"]').val('0.06');

            // 添加第二个明细项
            addInvoiceDetail();
            var secondDetail = $('.invoice-detail-item').last();
            secondDetail.find('input[name="spmc"]').val('技术咨询服务');
            secondDetail.find('input[name="spbm"]').val('3040407990000000000');
            secondDetail.find('input[name="ggxh"]').val('专业版');
            secondDetail.find('input[name="dw"]').val('次');
            secondDetail.find('input[name="sl"]').val('2');
            secondDetail.find('input[name="dj"]').val('5000.00');
            secondDetail.find('input[name="je"]').val('10000.00');
            secondDetail.find('select[name="slv"]').val('0.06');

            // 隐藏结果
            $('#resultContainer').addClass('hidden').hide();

            SwgxTest.showSuccess('测试数据填充完成！');
        }
    </script>
</body>
</html>
