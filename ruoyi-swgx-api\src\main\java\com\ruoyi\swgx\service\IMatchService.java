package com.ruoyi.swgx.service;

import com.ruoyi.swgx.domain.dto.MatchRequestDto;
import com.ruoyi.swgx.domain.dto.MatchProgressDto;
import com.ruoyi.swgx.domain.PpRw;

/**
 * 匹配服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IMatchService {
    
    /**
     * 启动匹配任务
     * 
     * @param request 匹配请求参数
     * @return 任务ID
     */
    String startMatchTask(MatchRequestDto request);
    
    /**
     * 获取匹配进度
     * 
     * @param taskId 任务ID
     * @return 匹配进度信息
     */
    MatchProgressDto getMatchProgress(String taskId);
    
    /**
     * 取消匹配任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelMatchTask(String taskId);
    
    /**
     * 执行单个匹配任务
     * 
     * @param task 匹配任务
     */
    void executeMatchTask(PpRw task);
    
    /**
     * 初始化电票明细匹配状态
     * 确保所有电票明细都有对应的匹配状态记录
     */
    void initInvoiceDetailMatchStatus();
}
