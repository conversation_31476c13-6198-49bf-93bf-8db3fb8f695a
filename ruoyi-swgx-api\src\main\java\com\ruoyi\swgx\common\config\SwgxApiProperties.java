package com.ruoyi.swgx.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 百旺金穗云API配置属性
 * 
 * <AUTHOR>
 * {@code @date} 2024-01-01
 */
@Data
@ConfigurationProperties(prefix = "swgx.api")
public class SwgxApiProperties {

    /**
     * API基础URL
     */
    private String baseUrl = "https://api.baiwangjs.com/swgx-saas/agentinvoiceservice-cloudservice-cloudservice/agentiscloud";

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * HTTP客户端配置
     */
    private HttpConfig http = new HttpConfig();

    /**
     * 企业配置
     */
    private CompanyConfig company = new CompanyConfig();

    /**
     * 日志配置
     */
    private LoggingConfig logging = new LoggingConfig();

    @Data
    public static class HttpConfig {
        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 30000;

        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeout = 60000;

        /**
         * 最大连接数
         */
        private int maxConnections = 200;

        /**
         * 每个路由的最大连接数
         */
        private int maxConnectionsPerRoute = 50;
    }

    @Data
    public static class CompanyConfig {
        /**
         * 企业名称
         */
        private String name = "河北九赋";

        /**
         * 纳税人识别号
         */
        private String taxNumber;

        /**
         * 企业唯一标识（必须直接配置）
         */
        private String uniqueId;
    }

    @Data
    public static class LoggingConfig {
        /**
         * 是否启用日志
         */
        private boolean enabled = true;

        /**
         * 日志级别
         */
        private String level = "INFO";
    }
}
