package com.ruoyi.swgx.common.dto.invoice;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 发票开具申请请求
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class ApplyInvoiceRequest {

    /**
     * 企业唯一标识（通过查询企业唯一标识接口获取，只需一次）
     */
    @NotBlank(message = "企业唯一标识不能为空")
    private String qyId;

    /**
     * 订单流水号（要求全局唯一，对同一笔订单，如果开具失败，重新发起这一笔时，需传原ddlsh）
     */
    @NotBlank(message = "订单流水号不能为空")
    private String ddlsh;

    /**
     * 税控设备号
     * 税盘：传盘号（支持多盘）
     * 全电：传销方税号
     * 税控服务器核心板：传开票终端代码（通过接口2.2查询）
     */
    @NotBlank(message = "税控设备号不能为空")
    private String sksbh;

    /**
     * 编码表版本号
     */
    private String bmbBbh;

    /**
     * 发票类型代码
     * 【税控】026：电子发票 007：普通发票 004：专用发票 025：卷票 028：电子专用发票
     * 【数电】01：数电专票 02：数电普票 03：机动车纸票 04：二手车纸票
     */
    @NotBlank(message = "发票类型代码不能为空")
    private String fplxdm;

    /**
     * 开票类型
     * 0：蓝字发票 1：红字发票
     */
    @NotBlank(message = "开票类型不能为空")
    private String kplx;

    /**
     * 发票代码
     */
    private String fpDm;

    /**
     * 发票号码
     */
    private String fpHm;

    /**
     * 特殊票种
     * 00 默认
     */
    private String tspz = "00";

    /**
     * 征收方式
     * 0 普通征税（默认） 1 减按计征 2 差额征收-差额开票 3 差额征收-全额开票
     */
    private String zsfs = "0";

    /**
     * 清单标识
     * 0 非清单（数电传此项） 1 清单(纸质超过8行默认清单票)
     */
    private String qdbz = "0";

    // 销售方信息
    /**
     * 销售方名称
     */
    @NotBlank(message = "销售方名称不能为空")
    private String xsfMc;

    /**
     * 销售方纳税人识别号
     */
    @NotBlank(message = "销售方纳税人识别号不能为空")
    private String xsfNsrsbh;

    /**
     * 销售方地址（卷票不填，其他票种必填）
     */
    @NotBlank(message = "销售方地址不能为空")
    private String xsfDz;

    /**
     * 销售方电话（卷票不填，其他票种必填）
     */
    @NotBlank(message = "销售方电话不能为空")
    private String xsfDh;

    /**
     * 销售方银行（卷票不填，其他票种必填）
     */
    @NotBlank(message = "销售方银行不能为空")
    private String xsfYh;

    /**
     * 销售方账号（卷票不填，其他票种必填）
     */
    @NotBlank(message = "销售方账号不能为空")
    private String xsfZh;

    // 购买方信息
    /**
     * 购买方名称
     */
    @NotBlank(message = "购买方名称不能为空")
    private String gmfMc;

    /**
     * 购买方纳税人识别号
     */
    private String gmfNsrsbh;

    /**
     * 购买方地址
     */
    private String gmfDz;

    /**
     * 购买方电话
     */
    private String gmfDh;

    /**
     * 购买方银行
     */
    private String gmfYh;

    /**
     * 购买方账号
     */
    private String gmfZh;

    /**
     * 购买方手机号（电票填写，发送PDF地址）
     */
    private String gmfMobile;

    /**
     * 购买方邮箱（电票填写，发送PDF地址）
     */
    private String gmfEmail;

    // 开票人员信息
    /**
     * 开票人
     */
    @NotBlank(message = "开票人不能为空")
    private String kpr;

    /**
     * 收款人
     */
    private String skr;

    /**
     * 复核人
     */
    private String fhr;

    /**
     * 申请人
     */
    @NotBlank(message = "申请人不能为空")
    private String sqr;

    // 金额信息
    /**
     * 价税合计（小数点2位）
     */
    private BigDecimal jshj;

    /**
     * 不含税金额（小数点2位）
     */
    private BigDecimal hjje;

    /**
     * 税额（小数点2位）
     */
    private BigDecimal hjse;

    /**
     * 备注（200汉字）
     */
    private String bz;

    /**
     * 局端登录账号（数电票必填，开票员登录电子税务局的账号，一般为手机号）
     */
    @NotBlank(message = "局端登录账号不能为空")
    private String dlzh;

    /**
     * 明细参数（多条明细）
     */
    @NotEmpty(message = "发票明细不能为空")
    @Valid
    private List<InvoiceDetailParam> detailParam;

    // 其他可选参数
    /**
     * 是否自动审核（0 自动审核(默认) 1 订单需要审核）
     */
    private String checkAudit = "0";

    /**
     * 是否为纸质发票（Y是 N不是）
     */
    private String sfwzzfp = "N";

    /**
     * 购买方自然人标识（N 非自然人标识（不传默认） Y自然人标识）
     */
    private String gmfzrrbs = "N";

    /**
     * 是否是事业单位（0是 1否）
     */
    private String spflxconfirm = "1";

    /**
     * 商品信息补充说明（默认1）
     */
    private String spxxbcsm = "1";
}
