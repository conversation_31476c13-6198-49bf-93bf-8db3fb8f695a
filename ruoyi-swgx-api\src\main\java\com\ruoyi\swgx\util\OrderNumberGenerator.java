package com.ruoyi.swgx.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 订单号生成器
 * 生成全局唯一的订单流水号
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class OrderNumberGenerator {

    private static final String PREFIX = "SWGX";
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private static final Random RANDOM = new Random();

    /**
     * 生成订单号
     * 格式：SWGX + 时间戳(14位) + 序列号(4位) + 随机数(4位)
     * 
     * @return 订单号
     */
    public static String generate() {
        StringBuilder orderNumber = new StringBuilder();
        
        // 前缀
        orderNumber.append(PREFIX);
        
        // 时间戳
        orderNumber.append(LocalDateTime.now().format(DATE_FORMAT));
        
        // 序列号（4位，循环使用）
        long seq = SEQUENCE.incrementAndGet() % 10000;
        orderNumber.append(String.format("%04d", seq));
        
        // 随机数（4位）
        int random = RANDOM.nextInt(10000);
        orderNumber.append(String.format("%04d", random));
        
        return orderNumber.toString();
    }

    /**
     * 生成带前缀的订单号
     * 
     * @param prefix 自定义前缀
     * @return 订单号
     */
    public static String generate(String prefix) {
        if (prefix == null || prefix.trim().isEmpty()) {
            return generate();
        }
        
        StringBuilder orderNumber = new StringBuilder();
        
        // 自定义前缀
        orderNumber.append(prefix.toUpperCase());
        
        // 时间戳
        orderNumber.append(LocalDateTime.now().format(DATE_FORMAT));
        
        // 序列号（4位，循环使用）
        long seq = SEQUENCE.incrementAndGet() % 10000;
        orderNumber.append(String.format("%04d", seq));
        
        // 随机数（4位）
        int random = RANDOM.nextInt(10000);
        orderNumber.append(String.format("%04d", random));
        
        return orderNumber.toString();
    }

    /**
     * 生成简单的订单号（只包含时间戳和随机数）
     * 
     * @return 简单订单号
     */
    public static String generateSimple() {
        return System.currentTimeMillis() + "_" + RANDOM.nextInt(100000);
    }

    /**
     * 验证订单号格式是否正确
     * 
     * @param orderNumber 订单号
     * @return 是否有效
     */
    public static boolean isValid(String orderNumber) {
        if (orderNumber == null || orderNumber.trim().isEmpty()) {
            return false;
        }
        
        // 基本长度检查（SWGX + 14位时间戳 + 4位序列号 + 4位随机数 = 26位）
        if (orderNumber.length() < 20) {
            return false;
        }
        
        // 检查是否包含非法字符
        return orderNumber.matches("^[A-Z0-9_]+$");
    }
}
