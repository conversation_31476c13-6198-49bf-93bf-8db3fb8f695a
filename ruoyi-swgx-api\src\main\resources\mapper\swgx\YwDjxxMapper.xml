<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.swgx.mapper.YwDjxxMapper">
    
    <resultMap type="YwDjxx" id="YwDjxxResult">
        <result property="id"    column="id"    />
        <result property="djbh"    column="djbh"    />
        <result property="djlx"    column="djlx"    />
        <result property="djzt"    column="djzt"    />
        <result property="ppzt"    column="ppzt"    />
        <result property="djrq"    column="djrq"    />
        <result property="gmfMc"    column="gmf_mc"    />
        <result property="gmfNsrsbh"    column="gmf_nsrsbh"    />
        <result property="gmfDz"    column="gmf_dz"    />
        <result property="gmfDh"    column="gmf_dh"    />
        <result property="gmfYhzh"    column="gmf_yhzh"    />
        <result property="hjje"    column="hjje"    />
        <result property="hjse"    column="hjse"    />
        <result property="jshj"    column="jshj"    />
        <result property="syHjje"    column="sy_hjje"    />
        <result property="syHjse"    column="sy_hjse"    />
        <result property="syJshj"    column="sy_jshj"    />
        <result property="hcYy"    column="hc_yy"    />
        <result property="hcCs"    column="hc_cs"    />
        <result property="ywxtId"    column="ywxt_id"    />
        <result property="ywxtMc"    column="ywxt_mc"    />
        <result property="bz"    column="bz"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="YwDjxxYwDjmxResult" type="YwDjxx" extends="YwDjxxResult">
        <collection property="ywDjmxList" ofType="YwDjmx" column="id" select="selectYwDjmxList" />
    </resultMap>

    <resultMap type="YwDjmx" id="YwDjmxResult">
        <result property="id"    column="id"    />
        <result property="djId"    column="dj_id"    />
        <result property="xh"    column="xh"    />
        <result property="spMc"    column="sp_mc"    />
        <result property="spBm"    column="sp_bm"    />
        <result property="ggxh"    column="ggxh"    />
        <result property="dw"    column="dw"    />
        <result property="sl"    column="sl"    />
        <result property="dj"    column="dj"    />
        <result property="je"    column="je"    />
        <result property="slv"    column="slv"    />
        <result property="se"    column="se"    />
        <result property="ppZt"    column="pp_zt"    />
        <result property="sySl"    column="sy_sl"    />
        <result property="syJe"    column="sy_je"    />
        <result property="sySe"    column="sy_se"    />
        <result property="ppCs"    column="pp_cs"    />
        <result property="lastPpTime"    column="last_pp_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectYwDjxxVo">
        select id, djbh, djlx, djzt, ppzt, djrq, gmf_mc, gmf_nsrsbh, gmf_dz, gmf_dh, gmf_yhzh, hjje, hjse, jshj, sy_hjje, sy_hjse, sy_jshj, hc_yy, hc_cs, ywxt_id, ywxt_mc, bz, create_by, create_time, update_by, update_time from swgx_yw_djxx
    </sql>

    <select id="selectYwDjxxList" parameterType="YwDjxx" resultMap="YwDjxxResult">
        <include refid="selectYwDjxxVo"/>
        <where>  
            <if test="djbh != null  and djbh != ''"> and djbh = #{djbh}</if>
            <if test="djlx != null  and djlx != ''"> and djlx = #{djlx}</if>
            <if test="djzt != null  and djzt != ''"> and djzt = #{djzt}</if>
            <if test="ppzt != null  and ppzt != ''"> and ppzt = #{ppzt}</if>
            <if test="params.beginDjrq != null and params.beginDjrq != '' and params.endDjrq != null and params.endDjrq != ''"> and djrq between #{params.beginDjrq} and #{params.endDjrq}</if>
            <if test="gmfMc != null  and gmfMc != ''"> and gmf_mc = #{gmfMc}</if>
            <if test="gmfNsrsbh != null  and gmfNsrsbh != ''"> and gmf_nsrsbh = #{gmfNsrsbh}</if>
        </where>
    </select>
    
    <select id="selectYwDjxxById" parameterType="String" resultMap="YwDjxxYwDjmxResult">
        select id, djbh, djlx, djzt, ppzt, djrq, gmf_mc, gmf_nsrsbh, gmf_dz, gmf_dh, gmf_yhzh, hjje, hjse, jshj, sy_hjje, sy_hjse, sy_jshj, hc_yy, hc_cs, ywxt_id, ywxt_mc, bz, create_by, create_time, update_by, update_time
        from swgx_yw_djxx
        where id = #{id}
    </select>

    <select id="selectYwDjmxList" resultMap="YwDjmxResult">
        select id, dj_id, xh, sp_mc, sp_bm, ggxh, dw, sl, dj, je, slv, se, pp_zt, sy_sl, sy_je, sy_se, pp_cs, last_pp_time, create_by, create_time, update_by, update_time
        from swgx_yw_djmx
        where dj_id = #{id}
    </select>

    <insert id="insertYwDjxx" parameterType="YwDjxx">
        insert into swgx_yw_djxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="djbh != null and djbh != ''">djbh,</if>
            <if test="djlx != null and djlx != ''">djlx,</if>
            <if test="djzt != null">djzt,</if>
            <if test="ppzt != null">ppzt,</if>
            <if test="djrq != null">djrq,</if>
            <if test="gmfMc != null and gmfMc != ''">gmf_mc,</if>
            <if test="gmfNsrsbh != null">gmf_nsrsbh,</if>
            <if test="gmfDz != null">gmf_dz,</if>
            <if test="gmfDh != null">gmf_dh,</if>
            <if test="gmfYhzh != null">gmf_yhzh,</if>
            <if test="hjje != null">hjje,</if>
            <if test="hjse != null">hjse,</if>
            <if test="jshj != null">jshj,</if>
            <if test="syHjje != null">sy_hjje,</if>
            <if test="syHjse != null">sy_hjse,</if>
            <if test="syJshj != null">sy_jshj,</if>
            <if test="hcYy != null">hc_yy,</if>
            <if test="hcCs != null">hc_cs,</if>
            <if test="ywxtId != null">ywxt_id,</if>
            <if test="ywxtMc != null">ywxt_mc,</if>
            <if test="bz != null">bz,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="djbh != null and djbh != ''">#{djbh},</if>
            <if test="djlx != null and djlx != ''">#{djlx},</if>
            <if test="djzt != null">#{djzt},</if>
            <if test="ppzt != null">#{ppzt},</if>
            <if test="djrq != null">#{djrq},</if>
            <if test="gmfMc != null and gmfMc != ''">#{gmfMc},</if>
            <if test="gmfNsrsbh != null">#{gmfNsrsbh},</if>
            <if test="gmfDz != null">#{gmfDz},</if>
            <if test="gmfDh != null">#{gmfDh},</if>
            <if test="gmfYhzh != null">#{gmfYhzh},</if>
            <if test="hjje != null">#{hjje},</if>
            <if test="hjse != null">#{hjse},</if>
            <if test="jshj != null">#{jshj},</if>
            <if test="syHjje != null">#{syHjje},</if>
            <if test="syHjse != null">#{syHjse},</if>
            <if test="syJshj != null">#{syJshj},</if>
            <if test="hcYy != null">#{hcYy},</if>
            <if test="hcCs != null">#{hcCs},</if>
            <if test="ywxtId != null">#{ywxtId},</if>
            <if test="ywxtMc != null">#{ywxtMc},</if>
            <if test="bz != null">#{bz},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateYwDjxx" parameterType="YwDjxx">
        update swgx_yw_djxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="djbh != null and djbh != ''">djbh = #{djbh},</if>
            <if test="djlx != null and djlx != ''">djlx = #{djlx},</if>
            <if test="djzt != null">djzt = #{djzt},</if>
            <if test="ppzt != null">ppzt = #{ppzt},</if>
            <if test="djrq != null">djrq = #{djrq},</if>
            <if test="gmfMc != null and gmfMc != ''">gmf_mc = #{gmfMc},</if>
            <if test="gmfNsrsbh != null">gmf_nsrsbh = #{gmfNsrsbh},</if>
            <if test="gmfDz != null">gmf_dz = #{gmfDz},</if>
            <if test="gmfDh != null">gmf_dh = #{gmfDh},</if>
            <if test="gmfYhzh != null">gmf_yhzh = #{gmfYhzh},</if>
            <if test="hjje != null">hjje = #{hjje},</if>
            <if test="hjse != null">hjse = #{hjse},</if>
            <if test="jshj != null">jshj = #{jshj},</if>
            <if test="syHjje != null">sy_hjje = #{syHjje},</if>
            <if test="syHjse != null">sy_hjse = #{syHjse},</if>
            <if test="syJshj != null">sy_jshj = #{syJshj},</if>
            <if test="hcYy != null">hc_yy = #{hcYy},</if>
            <if test="hcCs != null">hc_cs = #{hcCs},</if>
            <if test="ywxtId != null">ywxt_id = #{ywxtId},</if>
            <if test="ywxtMc != null">ywxt_mc = #{ywxtMc},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYwDjxxById" parameterType="String">
        delete from swgx_yw_djxx where id = #{id}
    </delete>

    <delete id="deleteYwDjxxByIds" parameterType="String">
        delete from swgx_yw_djxx where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteYwDjmxByDjIds" parameterType="String">
        delete from swgx_yw_djmx where dj_id in 
        <foreach item="djId" collection="array" open="(" separator="," close=")">
            #{djId}
        </foreach>
    </delete>

    <delete id="deleteYwDjmxByDjId" parameterType="String">
        delete from swgx_yw_djmx where dj_id = #{djId}
    </delete>

    <insert id="batchYwDjmx">
        insert into swgx_yw_djmx( id, dj_id, xh, sp_mc, sp_bm, ggxh, dw, sl, dj, je, slv, se, pp_zt, sy_sl, sy_je, sy_se, pp_cs, last_pp_time, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.djId}, #{item.xh}, #{item.spMc}, #{item.spBm}, #{item.ggxh}, #{item.dw}, #{item.sl}, #{item.dj}, #{item.je}, #{item.slv}, #{item.se}, #{item.ppZt}, #{item.sySl}, #{item.syJe}, #{item.sySe}, #{item.ppCs}, #{item.lastPpTime}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 查询业务单据明细列表（用于匹配） -->
    <select id="selectYwDjmxByDjId" parameterType="String" resultMap="YwDjmxResult">
        select id, dj_id, xh, sp_mc, sp_bm, ggxh, dw, sl, dj, je, slv, se, pp_zt, sy_sl, sy_je, sy_se, pp_cs, last_pp_time, create_by, create_time, update_by, update_time
        from swgx_yw_djmx
        where dj_id = #{djId}
        order by xh
    </select>

    <!-- 根据ID查询业务单据明细 -->
    <select id="selectYwDjmxById" parameterType="String" resultMap="YwDjmxResult">
        select id, dj_id, xh, sp_mc, sp_bm, ggxh, dw, sl, dj, je, slv, se, pp_zt, sy_sl, sy_je, sy_se, pp_cs, last_pp_time, create_by, create_time, update_by, update_time
        from swgx_yw_djmx
        where id = #{id}
    </select>

    <!-- 修改业务单据明细 -->
    <update id="updateYwDjmx" parameterType="YwDjmx">
        update swgx_yw_djmx
        <trim prefix="SET" suffixOverrides=",">
            <if test="xh != null">xh = #{xh},</if>
            <if test="spMc != null and spMc != ''">sp_mc = #{spMc},</if>
            <if test="spBm != null">sp_bm = #{spBm},</if>
            <if test="ggxh != null">ggxh = #{ggxh},</if>
            <if test="dw != null">dw = #{dw},</if>
            <if test="sl != null">sl = #{sl},</if>
            <if test="dj != null">dj = #{dj},</if>
            <if test="je != null">je = #{je},</if>
            <if test="slv != null">slv = #{slv},</if>
            <if test="se != null">se = #{se},</if>
            <if test="ppZt != null">pp_zt = #{ppZt},</if>
            <if test="sySl != null">sy_sl = #{sySl},</if>
            <if test="syJe != null">sy_je = #{syJe},</if>
            <if test="sySe != null">sy_se = #{sySe},</if>
            <if test="ppCs != null">pp_cs = #{ppCs},</if>
            <if test="lastPpTime != null">last_pp_time = #{lastPpTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>