<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通用API测试工具 - 河北九赋</title>
    <link rel="shortcut icon" th:href="@{/img/logo.jpg}">
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/animate.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/swgx-test.css}" rel="stylesheet"/>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <!-- 返回按钮 -->
        <div class="row">
            <div class="col-lg-12">
                <a href="/swgx/demo/test" class="btn btn-outline btn-sm">
                    <i class="fa fa-arrow-left"></i> 返回测试中心
                </a>
            </div>
        </div>

        <!-- 页面标题 -->
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-code"></i> 通用API测试工具</h5>
                        <div class="ibox-tools">
                            <span class="label label-success">推荐</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i>
                            支持测试所有SWGX模块的API接口，提供完整的请求和响应信息
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：请求配置 -->
            <div class="col-lg-8">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-send"></i> API请求配置</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <!-- HTTP方法和完整URL -->
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>HTTP方法</label>
                                    <select class="form-control" id="httpMethod" title="HTTP方法">
                                        <option value="GET">GET</option>
                                        <option value="POST" selected>POST</option>
                                        <option value="PUT">PUT</option>
                                        <option value="DELETE">DELETE</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-10">
                                <div class="form-group">
                                    <label>完整API接口地址</label>
                                    <input type="text" class="form-control api-path-input" id="apiPath"
                                           placeholder="例如: https://api.baiwangjs.com/swgx-saas/agentinvoiceservice-cloudservice-cloudservice/agentiscloud/cloud/queryUniqueSign">
                                    <small class="text-muted">
                                        <i class="fa fa-info-circle"></i>
                                        请输入完整的百旺金穗云API接口地址
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- 快速模板 -->
                        <div class="form-group">
                            <label>快速模板</label>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-info" onclick="loadTemplate('queryUniqueSign')">
                                    <i class="fa fa-building"></i> 企业查询
                                </button>
                                <button type="button" class="btn btn-info" onclick="loadTemplate('applyInvoice')">
                                    <i class="fa fa-file-text"></i> 发票开具
                                </button>
                                <button type="button" class="btn btn-white" onclick="clearRequest()">
                                    <i class="fa fa-refresh"></i> 清空
                                </button>
                            </div>
                        </div>

                                <!-- JSON请求体 -->
                                <div class="form-group">
                                    <label>JSON请求体</label>
                                    <div class="json-tools">
                                        <button type="button" class="btn btn-xs btn-default" onclick="formatJson()">
                                            <i class="fa fa-indent"></i> 格式化
                                        </button>
                                        <button type="button" class="btn btn-xs btn-success" onclick="validateJson()">
                                            <i class="fa fa-check"></i> 验证
                                        </button>
                                    </div>
                                    <textarea class="form-control json-editor" id="requestBody" rows="10"
                                              placeholder="请输入JSON格式的请求体...&#10;&#10;示例：&#10;{&#10;  &quot;nsrmc&quot;: &quot;企业名称&quot;,&#10;  &quot;nsrsbh&quot;: &quot;纳税人识别号&quot;&#10;}"></textarea>
                                    <small class="text-muted">
                                        <i class="fa fa-lightbulb-o"></i>
                                        支持从接口文档直接复制粘贴JSON请求体，或使用上方快速模板
                                    </small>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="text-center">
                                    <button type="button" class="test-btn success" onclick="sendRequest()">
                                        <i class="fa fa-paper-plane"></i> 发送请求
                                    </button>
                                    <button type="button" class="test-btn info" onclick="showTemplateSelector()">
                                        <i class="fa fa-file-code-o"></i> 选择模板
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：历史记录 -->
                    <div class="col-md-4">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4><i class="fa fa-history"></i> 请求历史</h4>
                                <button type="button" class="btn btn-xs btn-default pull-right" onclick="clearHistory()">
                                    <i class="fa fa-trash"></i> 清空
                                </button>
                            </div>
                            <div class="panel-body history-panel-body">
                                <div id="requestHistory">
                                    <div class="text-center text-muted history-empty">
                                        暂无历史记录
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 响应结果 -->
                <div id="responseContainer" class="hidden">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4><i class="fa fa-reply"></i> API响应结果</h4>
                            <div class="pull-right">
                                <span id="responseStatus" class="label label-default"></span>
                                <span id="responseTime" class="label label-info"></span>
                            </div>
                        </div>
                        <div class="panel-body">
                            <!-- 响应头信息 -->
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>响应头信息</h5>
                                    <pre id="responseHeaders" class="response-container"></pre>
                                </div>
                                <div class="col-md-6">
                                    <h5>响应体</h5>
                                    <pre id="responseBody" class="response-container"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="test-card animated fadeInUp">
            <div class="test-card-header warning-header">
                <h4><i class="fa fa-book"></i> 使用说明</h4>
            </div>
            <div class="test-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fa fa-info-circle"></i> 功能特点</h5>
                        <ul>
                            <li>支持所有HTTP方法（GET、POST、PUT、DELETE）</li>
                            <li>输入完整的API接口地址，包含协议和域名</li>
                            <li>提供快速模板按钮，一键加载常用接口</li>
                            <li>支持JSON格式验证和格式化</li>
                            <li>自动保存请求历史记录</li>
                            <li>显示完整的原始响应信息</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fa fa-lightbulb-o"></i> 使用技巧</h5>
                        <ul>
                            <li>使用快速模板按钮快速加载示例请求</li>
                            <li>可以直接从API文档复制JSON请求体</li>
                            <li>使用"格式化"和"验证"按钮处理JSON</li>
                            <li>点击历史记录可以快速重复请求</li>
                            <li>支持测试任意HTTP API接口</li>
                            <li>查看原始响应数据，便于调试</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script th:src="@{/js/swgx-test.js}"></script>
    <script>
        // 请求历史记录
        var requestHistory = JSON.parse(localStorage.getItem('swgx-api-history') || '[]');
        var requestStartTime;

        $(document).ready(function() {
            loadHistory();
        });

        /**
         * 发送API请求
         */
        function sendRequest() {
            var method = $('#httpMethod').val();
            var path = $('#apiPath').val().trim();
            var body = $('#requestBody').val().trim();

            if (!path) {
                SwgxTest.showError('请输入API接口路径');
                return;
            }

            // 验证JSON格式（如果有请求体）
            if (body && !validateJsonSilent(body)) {
                SwgxTest.showError('请求体JSON格式不正确');
                return;
            }

            // 记录请求开始时间
            requestStartTime = new Date().getTime();

            // 调用原始API接口，获取第三方API的原始响应
            var requestData = {
                apiPath: path,
                requestBody: body || '{}'
            };

            var ajaxConfig = {
                url: '/swgx/api-test/raw',
                type: 'POST',
                timeout: 30000,
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(data, textStatus, xhr) {
                    console.log('原始API响应成功:', data, textStatus, xhr);
                    handleResponse(xhr, data, true);
                    saveToHistory(method, path, body, true);
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.log('原始API响应错误:', xhr, textStatus, errorThrown);
                    handleResponse(xhr, null, false);
                    saveToHistory(method, path, body, false);
                },
                complete: function() {
                    // 请求完成后的处理
                }
            };

            // 显示加载状态
            SwgxTest.showLoading('发送请求中...');

            // 发送请求
            $.ajax(ajaxConfig).always(function() {
                SwgxTest.hideLoading();
            });
        }

        /**
         * 处理API响应
         */
        function handleResponse(xhr, data, isSuccess) {
            var responseTime = new Date().getTime() - requestStartTime;

            // 显示响应容器
            $('#responseContainer').removeClass('hidden').show();

            // 设置状态码和响应时间
            var statusClass = isSuccess ? 'label-success' : 'label-danger';
            var statusText = '未知状态';

            if (xhr && xhr.status !== undefined) {
                statusText = xhr.status + ' ' + (xhr.statusText || '');
            } else if (!isSuccess) {
                statusText = '请求失败';
            } else {
                statusText = '200 OK';
            }

            $('#responseStatus').removeClass().addClass('label ' + statusClass).text(statusText);
            $('#responseTime').text(responseTime + 'ms');

            // 设置响应头
            var headers = '无响应头信息';
            if (xhr && typeof xhr.getAllResponseHeaders === 'function') {
                try {
                    headers = xhr.getAllResponseHeaders() || '无响应头信息';
                } catch (e) {
                    headers = '无法获取响应头信息';
                }
            }
            $('#responseHeaders').text(headers);

            // 设置响应体 - 显示原始响应数据
            var responseBody = '无响应内容';

            if (xhr && xhr.responseText) {
                // 优先使用xhr.responseText获取原始响应
                responseBody = xhr.responseText;
                try {
                    // 尝试格式化JSON
                    var jsonData = JSON.parse(xhr.responseText);
                    responseBody = JSON.stringify(jsonData, null, 2);
                } catch (e) {
                    // 如果不是JSON格式，直接显示原始文本
                    responseBody = xhr.responseText;
                }
            } else if (data) {
                // 如果没有xhr.responseText，使用传入的data
                if (typeof data === 'object') {
                    responseBody = JSON.stringify(data, null, 2);
                } else {
                    responseBody = data;
                }
            }

            $('#responseBody').text(responseBody);

            // 滚动到响应区域
            $('html, body').animate({
                scrollTop: $('#responseContainer').offset().top - 100
            }, 500);
        }

        /**
         * 保存请求到历史记录
         */
        function saveToHistory(method, path, body, isSuccess) {
            var historyItem = {
                id: Date.now(),
                method: method,
                path: path,
                body: body,
                timestamp: new Date().toLocaleString(),
                success: isSuccess
            };

            // 添加到历史记录开头
            requestHistory.unshift(historyItem);

            // 限制历史记录数量
            if (requestHistory.length > 50) {
                requestHistory = requestHistory.slice(0, 50);
            }

            // 保存到localStorage
            localStorage.setItem('swgx-api-history', JSON.stringify(requestHistory));

            // 刷新历史记录显示
            loadHistory();
        }

        /**
         * 加载历史记录
         */
        function loadHistory() {
            var container = $('#requestHistory');

            if (requestHistory.length === 0) {
                container.html('<div class="text-center text-muted" style="padding: 20px;">暂无历史记录</div>');
                return;
            }

            var html = '';
            requestHistory.forEach(function(item) {
                var methodClass = 'method-' + item.method.toLowerCase();
                var statusIcon = item.success ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';

                html += '<div class="history-item" onclick="loadFromHistory(' + item.id + ')">';
                html += '<div>';
                html += '<span class="method-badge ' + methodClass + '">' + item.method + '</span>';
                html += '<span class="text-primary">' + item.path + '</span>';
                html += '<i class="fa ' + statusIcon + ' pull-right" style="margin-top: 2px;"></i>';
                html += '</div>';
                html += '<small class="text-muted">' + item.timestamp + '</small>';
                html += '</div>';
            });

            container.html(html);
        }

        /**
         * 从历史记录加载请求
         */
        function loadFromHistory(id) {
            var item = requestHistory.find(function(h) { return h.id === id; });
            if (item) {
                $('#httpMethod').val(item.method);
                $('#apiPath').val(item.path);
                $('#requestBody').val(item.body || '');
                SwgxTest.showSuccess('已加载历史请求');
            }
        }

        /**
         * 清空历史记录
         */
        function clearHistory() {
            layer.confirm('确定要清空所有历史记录吗？', {
                icon: 3,
                title: '确认操作'
            }, function(index) {
                requestHistory = [];
                localStorage.removeItem('swgx-api-history');
                loadHistory();
                layer.close(index);
                SwgxTest.showSuccess('历史记录已清空');
            });
        }

        /**
         * 格式化JSON
         */
        function formatJson() {
            var body = $('#requestBody').val().trim();
            if (!body) {
                SwgxTest.showWarning('请先输入JSON内容');
                return;
            }

            try {
                var parsed = JSON.parse(body);
                var formatted = JSON.stringify(parsed, null, 2);
                $('#requestBody').val(formatted);
                SwgxTest.showSuccess('JSON格式化完成');
            } catch (e) {
                SwgxTest.showError('JSON格式不正确: ' + e.message);
            }
        }

        /**
         * 验证JSON格式
         */
        function validateJson() {
            var body = $('#requestBody').val().trim();
            if (!body) {
                SwgxTest.showWarning('请先输入JSON内容');
                return;
            }

            if (validateJsonSilent(body)) {
                SwgxTest.showSuccess('JSON格式正确');
            } else {
                SwgxTest.showError('JSON格式不正确');
            }
        }

        /**
         * 静默验证JSON格式
         */
        function validateJsonSilent(jsonStr) {
            try {
                JSON.parse(jsonStr);
                return true;
            } catch (e) {
                return false;
            }
        }

        /**
         * 清空请求内容
         */
        function clearRequest() {
            $('#apiPath').val('');
            $('#requestBody').val('');
            $('#responseContainer').addClass('hidden').hide();
            SwgxTest.showSuccess('请求内容已清空');
        }

        /**
         * 显示模板选择器
         */
        function showTemplateSelector() {
            var templateOptions = [
                { value: 'queryUniqueSign', text: '企业唯一标识查询' },
                { value: 'applyInvoice', text: '发票开具申请' }
            ];

            var optionsHtml = templateOptions.map(function(option) {
                return '<div style="margin: 10px 0;"><button type="button" class="btn btn-block btn-outline btn-primary" onclick="loadTemplate(\'' + option.value + '\'); layer.closeAll();">' + option.text + '</button></div>';
            }).join('');

            layer.open({
                title: '选择请求模板',
                type: 1,
                area: ['400px', '300px'],
                content: '<div style="padding: 20px;">' + optionsHtml + '</div>'
            });
        }

        /**
         * 加载请求模板
         */
        function loadTemplate(templateType) {
            console.log('Loading template:', templateType);

            var templates = {
                'applyInvoice': {
                    path: 'https://api.baiwangjs.com/swgx-saas/agentinvoiceservice-cloudservice-cloudservice/agentiscloud/cloud/v2/applyInvoice',
                    method: 'POST',
                    body: JSON.stringify({
                        "fplxdm": "02",
                        "kplx": "0",
                        "dlzh": "13800000001",
                        "gmfMc": "北京测试科技有限公司",
                        "gmfNsrsbh": "91110000123456789X",
                        "gmfDz": "北京市朝阳区测试大街123号",
                        "gmfDh": "010-12345678",
                        "kpr": "系统开票",
                        "sqr": "系统申请",
                        "bz": "测试发票开具",
                        "details": [{
                            "xh": 1,
                            "fphxz": "0",
                            "spmc": "软件开发服务",
                            "spbm": "3040407990000000000",
                            "sl": "1",
                            "dj": "10000.00",
                            "je": "10000.00",
                            "slv": "0.06",
                            "lslbs": "0"
                        }]
                    }, null, 2)
                },
                'queryUniqueSign': {
                    path: 'https://api.baiwangjs.com/swgx-saas/agentinvoiceservice-cloudservice-cloudservice/agentiscloud/cloud/queryUniqueSign',
                    method: 'POST',
                    body: JSON.stringify({
                        "nsrmc": "河北九赋",
                        "nsrsbh": "123456789"
                    }, null, 2)
                }
            };

            if (!templateType) {
                SwgxTest.showWarning('请选择要加载的模板类型');
                return;
            }

            if (templates[templateType]) {
                var template = templates[templateType];
                $('#httpMethod').val(template.method);
                $('#apiPath').val(template.path);
                $('#requestBody').val(template.body);
                SwgxTest.showSuccess('模板 "' + templateType + '" 加载完成');
            } else {
                var availableTemplates = Object.keys(templates).join(', ');
                SwgxTest.showWarning('未找到模板 "' + templateType + '"，可用模板: ' + availableTemplates);
            }
        }
    </script>
</body>
</html>
