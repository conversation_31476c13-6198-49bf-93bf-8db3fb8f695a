package com.ruoyi.swgx.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 电票发票明细对象 swgx_dp_fpxx_mx
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DpFpxxMx extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @NotBlank(message = "主键ID不能为空")
    @Size(max = 50, message = "主键ID长度不能超过50个字符")
    private String id;

    /** 企业ID */
    @Excel(name = "企业ID")
    @Size(max = 50, message = "企业ID长度不能超过50个字符")
    private String qyId;

    /** 发票ID(关联dp_fpxx.ID) */
    @Excel(name = "发票ID")
    @Size(max = 100, message = "发票ID长度不能超过100个字符")
    private String fpid;

    /** 发票行性质 */
    @Excel(name = "发票行性质", readConverterExp = "0=正常行,1=折扣行,2=被折扣行")
    private Integer fphxz;

    /** 商品名称 */
    @Excel(name = "商品名称")
    @Size(max = 150, message = "商品名称长度不能超过150个字符")
    private String spmc;

    /** 商品税目 */
    @Excel(name = "商品税目")
    @Size(max = 30, message = "商品税目长度不能超过30个字符")
    private String spsm;

    /** 规格型号 */
    @Excel(name = "规格型号")
    @Size(max = 64, message = "规格型号长度不能超过64个字符")
    private String ggxh;

    /** 计量单位 */
    @Excel(name = "计量单位")
    @Size(max = 32, message = "计量单位长度不能超过32个字符")
    private String dw;

    /** 商品编码 */
    @Excel(name = "商品编码")
    @Size(max = 40, message = "商品编码长度不能超过40个字符")
    private String spbm;

    /** 增值税特殊管理 */
    @Excel(name = "增值税特殊管理")
    @Size(max = 255, message = "增值税特殊管理长度不能超过255个字符")
    private String zzstsgl;

    /** 优惠政策标识 */
    @Excel(name = "优惠政策标识", readConverterExp = "0=无,1=有")
    private Integer yhzcbs;

    /** 零税率标识 */
    @Excel(name = "零税率标识", readConverterExp = "0=正常税率,1=免税,2=不征税,3=普通零税率")
    private Integer lslbs;

    /** 商品数量(小数点后12位) */
    @Excel(name = "商品数量")
    private BigDecimal spsl;

    /** 单价(小数点后12位) */
    @Excel(name = "单价")
    private BigDecimal dj;

    /** 金额(单位:元,2位小数) */
    @Excel(name = "金额")
    private BigDecimal je;

    /** 税率(6位小数,例1%为0.01) */
    @Excel(name = "税率")
    private BigDecimal sl;

    /** 税额(单位:元,2位小数) */
    @Excel(name = "税额")
    private BigDecimal se;

    /** 含税标志 */
    @Excel(name = "含税标志", readConverterExp = "0=不含税,1=含税")
    private Integer hsbz;

    /** 明细类型 */
    @Excel(name = "明细类型")
    @Size(max = 10, message = "明细类型长度不能超过10个字符")
    private String mxlx;

    /** 序列号 */
    @Excel(name = "序列号")
    @NotNull(message = "序列号不能为空")
    private Integer serialNumber;

    /** 所属地区国税ID */
    @Size(max = 50, message = "所属地区国税ID长度不能超过50个字符")
    private String ssdzgsid;

    /** 减征减免类型代码 */
    @Size(max = 20, message = "减征减免类型代码长度不能超过20个字符")
    private String jzjtlxDm;

    /** 免税政策代码 */
    @Size(max = 20, message = "免税政策代码长度不能超过20个字符")
    private String mtzldm;

    /** 出口贸易有效期限 */
    @Size(max = 50, message = "出口贸易有效期限长度不能超过50个字符")
    private String cxmyxqx;

    /** 绿证明细序号 */
    @Size(max = 50, message = "绿证明细序号长度不能超过50个字符")
    private String lzmxxh;

    /**
     * 计算含税金额
     * 
     * @return 含税金额
     */
    public BigDecimal getTaxInclusiveAmount() {
        if (je != null && se != null) {
            return je.add(se);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 计算实际税率
     * 
     * @return 实际税率（百分比形式）
     */
    public BigDecimal getActualTaxRate() {
        if (je != null && je.compareTo(BigDecimal.ZERO) > 0 && se != null) {
            return se.divide(je, 6, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 验证明细数据的完整性
     * 
     * @return true-数据完整，false-数据不完整
     */
    public boolean isDataComplete() {
        return spmc != null && !spmc.trim().isEmpty() 
            && spsl != null && spsl.compareTo(BigDecimal.ZERO) > 0
            && dj != null && dj.compareTo(BigDecimal.ZERO) >= 0
            && je != null && je.compareTo(BigDecimal.ZERO) >= 0
            && sl != null && sl.compareTo(BigDecimal.ZERO) >= 0
            && se != null && se.compareTo(BigDecimal.ZERO) >= 0;
    }

    /**
     * 判断是否为折扣行
     * 
     * @return true-是折扣行，false-不是折扣行
     */
    public boolean isDiscountLine() {
        return fphxz != null && fphxz == 1;
    }

    /**
     * 判断是否为被折扣行
     * 
     * @return true-是被折扣行，false-不是被折扣行
     */
    public boolean isDiscountedLine() {
        return fphxz != null && fphxz == 2;
    }

    /**
     * 获取格式化的税率显示
     * 
     * @return 格式化的税率字符串
     */
    public String getFormattedTaxRate() {
        if (sl != null) {
            return sl.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString() + "%";
        }
        return "0%";
    }

    /**
     * 计算行小计（数量 * 单价）
     * 
     * @return 行小计金额
     */
    public BigDecimal getLineTotal() {
        if (spsl != null && dj != null) {
            return spsl.multiply(dj);
        }
        return BigDecimal.ZERO;
    }
}
