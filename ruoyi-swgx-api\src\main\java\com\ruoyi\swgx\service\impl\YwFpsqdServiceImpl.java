package com.ruoyi.swgx.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.swgx.domain.YwFpsqdmx;
import com.ruoyi.swgx.mapper.YwFpsqdMapper;
import com.ruoyi.swgx.domain.YwFpsqd;
import com.ruoyi.swgx.service.IYwFpsqdService;
import com.ruoyi.common.core.text.Convert;

/**
 * 发票申请单信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class YwFpsqdServiceImpl implements IYwFpsqdService 
{
    @Autowired
    private YwFpsqdMapper ywFpsqdMapper;

    /**
     * 查询发票申请单信息
     * 
     * @param swguid 发票申请单信息主键
     * @return 发票申请单信息
     */
    @Override
    public YwFpsqd selectYwFpsqdBySwguid(String swguid)
    {
        return ywFpsqdMapper.selectYwFpsqdBySwguid(swguid);
    }

    /**
     * 查询发票申请单信息列表
     * 
     * @param ywFpsqd 发票申请单信息
     * @return 发票申请单信息
     */
    @Override
    public List<YwFpsqd> selectYwFpsqdList(YwFpsqd ywFpsqd)
    {
        return ywFpsqdMapper.selectYwFpsqdList(ywFpsqd);
    }

    /**
     * 新增发票申请单信息
     *
     * @param ywFpsqd 发票申请单信息
     * @return 结果
     */
    @Transactional
    @Override
    public int insertYwFpsqd(YwFpsqd ywFpsqd)
    {
        // 为主表记录生成UUID
        if (StringUtils.isEmpty(ywFpsqd.getSwguid())) {
            ywFpsqd.setSwguid(IdUtils.fastSimpleUUID());
        }
        int rows = ywFpsqdMapper.insertYwFpsqd(ywFpsqd);
        insertYwFpsqdmx(ywFpsqd);
        return rows;
    }

    /**
     * 修改发票申请单信息
     * 
     * @param ywFpsqd 发票申请单信息
     * @return 结果
     */
    @Transactional
    @Override
    public int updateYwFpsqd(YwFpsqd ywFpsqd)
    {
        ywFpsqdMapper.deleteYwFpsqdmxBySWMAINGUID(ywFpsqd.getSwguid());
        insertYwFpsqdmx(ywFpsqd);
        return ywFpsqdMapper.updateYwFpsqd(ywFpsqd);
    }

    /**
     * 批量删除发票申请单信息
     * 
     * @param swguids 需要删除的发票申请单信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteYwFpsqdBySwguids(String swguids)
    {
        ywFpsqdMapper.deleteYwFpsqdmxBySWMAINGUIDs(Convert.toStrArray(swguids));
        return ywFpsqdMapper.deleteYwFpsqdBySwguids(Convert.toStrArray(swguids));
    }

    /**
     * 删除发票申请单信息信息
     * 
     * @param swguid 发票申请单信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteYwFpsqdBySwguid(String swguid)
    {
        ywFpsqdMapper.deleteYwFpsqdmxBySWMAINGUID(swguid);
        return ywFpsqdMapper.deleteYwFpsqdBySwguid(swguid);
    }

    /**
     * 新增发票申请单明细信息
     *
     * @param ywFpsqd 发票申请单信息对象
     */
    public void insertYwFpsqdmx(YwFpsqd ywFpsqd)
    {
        List<YwFpsqdmx> ywFpsqdmxList = ywFpsqd.getYwFpsqdmxList();
        String swguid = ywFpsqd.getSwguid();
        if (StringUtils.isNotNull(ywFpsqdmxList))
        {
            List<YwFpsqdmx> list = new ArrayList<YwFpsqdmx>();
            for (YwFpsqdmx ywFpsqdmx : ywFpsqdmxList)
            {
                // 为明细记录生成UUID
                if (StringUtils.isEmpty(ywFpsqdmx.getSwguid())) {
                    ywFpsqdmx.setSwguid(IdUtils.fastSimpleUUID());
                }
                ywFpsqdmx.setSwmainguid(swguid);
                list.add(ywFpsqdmx);
            }
            if (list.size() > 0)
            {
                ywFpsqdMapper.batchYwFpsqdmx(list);
            }
        }
    }
}
