package com.ruoyi.swgx.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 匹配任务对象 swgx_pp_rw
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PpRw extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private String id;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String rwMc;

    /** 任务类型：SINGLE-单个匹配，BATCH-批量匹配，MERGE-合并匹配 */
    @Excel(name = "任务类型", readConverterExp = "SINGLE=单个匹配,BATCH=批量匹配,MERGE=合并匹配")
    private String rwLx;

    /** 任务状态：PENDING-待执行，RUNNING-执行中，SUCCESS-成功，FAILED-失败，CANCELLED-已取消 */
    @Excel(name = "任务状态", readConverterExp = "PENDING=待执行,RUNNING=执行中,SUCCESS=成功,FAILED=失败,CANCELLED=已取消")
    private String rwZt;

    /** 单据ID列表(JSON数组) */
    private String djIds;

    /** 匹配规则ID列表(JSON数组) */
    private String ppGzIds;

    /** 匹配总数 */
    @Excel(name = "匹配总数")
    private Integer ppCs;

    /** 成功匹配数 */
    @Excel(name = "成功匹配数")
    private Integer cgCs;

    /** 失败匹配数 */
    @Excel(name = "失败匹配数")
    private Integer sbCs;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ksSj;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date jsSj;

    /** 错误信息 */
    private String errorMsg;
}
