html {
	height:100%
}
body.signin {
	height:auto;
	background:url(../img/login-background.jpg) no-repeat center fixed;
	-webkit-background-size:cover;
	-moz-background-size:cover;
	-o-background-size:cover;
	background-size:cover
}
.signinpanel {
	width:750px;
	margin:10% auto 0;
	color:rgba(255,255,255,.95)
}
.signinpanel .logopanel {
	float:none;
	width:auto;
	padding:0;
	background:0 0
}
.signinpanel .signin-info ul {
	list-style:none;
	padding:0;
	margin:20px 0
}
.signinpanel .form-control {
	display:block;
	margin-top:15px
}
.signinpanel .uname {
	background:#fff url(../img/user.png) no-repeat 95% center;
	color:#333
}
.signinpanel .pword {
	background:#fff url(../img/locked.png) no-repeat 95% center;
	color:#333
}
.signinpanel .code {
    background: #fff no-repeat 95% center;color:#333; margin:0 0 15px 0;
}
.signinpanel .btn {
	margin-top:15px
}
.signinpanel form {
	background:rgba(255,255,255,.2);
	border:1px solid rgba(255,255,255,.3);
	-moz-box-shadow:0 3px 0 rgba(12,12,12,.03);
	-webkit-box-shadow:0 3px 0 rgba(12,12,12,.03);
	box-shadow:0 3px 0 rgba(12,12,12,.03);
	-moz-border-radius:3px;
	-webkit-border-radius:3px;
	border-radius:3px;
	padding:30px
}
.signup-footer {
	border-top:solid 1px rgba(255,255,255,.3);
	margin:20px 0;
	padding-top:15px
}
@media screen and (max-width:768px) {
	.signinpanel,.signuppanel {
	margin:0 auto;
	width:380px!important;
	padding:20px
}
.signinpanel form {
	margin-top:20px
}
.signup-footer,.signuppanel .form-control {
	margin-bottom:10px
}
.signup-footer .pull-left,.signup-footer .pull-right {
	float:none!important;
	text-align:center
}
.signinpanel .signin-info ul {
	display:none
}
}@media screen and (max-width:320px) {
	.signinpanel,.signuppanel {
	margin:0 20px;
	width:auto
}
}
/*
登录界面check样式
*/
.checkbox-custom {
    position: relative;
    padding: 0 15px 0 25px;
    margin-bottom: 7px;
    display: inline-block;
}
/*
将初始的checkbox的样式改变
*/
.checkbox-custom input[type="checkbox"] {
    opacity: 0; /*将初始的checkbox隐藏起来*/
    position: absolute;
    cursor: pointer;
    z-index: 2;
    margin: -6px 0 0 0;
    top: 50%;
    left: 3px;
}
/*
设计新的checkbox，位置
*/
.checkbox-custom label:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -9px;
    width: 18px;
    height: 17px;
    display: inline-block;
    border-radius: 2px;
    border: 1px solid #bbb;
    background: #fff;
}
/*
点击初始的checkbox，将新的checkbox关联起来
*/
.checkbox-custom input[type="checkbox"]:checked +label:after {
    position: absolute;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    content: "\e013";
    top: 42%;
    left: 3px;
    margin-top: -5px;
    font-size: 11px;
    line-height: 1;
    width: 16px;
    height: 16px;
    color: #333;
}
.checkbox-custom label {
    cursor: pointer;
    line-height: 1.2;
    font-weight: normal; /*改变了rememberme的字体*/
    margin-bottom: 0;
    text-align: left;
}

.form-control, .form-control:focus, .has-error .form-control:focus,  .has-success .form-control:focus, .has-warning .form-control:focus,  .navbar-collapse, .navbar-form, .navbar-form-custom .form-control:focus,  .navbar-form-custom .form-control:hover, .open .btn.dropdown-toggle,  .panel, .popover, .progress, .progress-bar {
    box-shadow: none;
}

.form-control {
    border-radius: 1px!important;
    padding: 6px 12px!important;
    height: 34px!important;
}

.form-control:focus {
    border-color: #1ab394 !important;
}

body .layer-ext-moon-msg[type="dialog"]{
    min-width: 100px !important;
}
body .layer-ext-moon-msg { 
    background-color: rgba(0,0,0,0.6);
    color: #fff;
    border: none;
}

body .layer-ext-moon-msg .layui-layer-content{
    padding: 12px 25px;
    text-align: center;
}

/* 河北九赋现代化登录页面样式 */

/* 基础样式重置 */
body {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 登录容器 */
.login-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 900px;
    max-width: 90%;
    min-height: 600px;
    display: flex;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 左侧信息区域 */
.login-left {
    flex: 1;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: white;
    position: relative;
}

.login-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* Logo区域 */
.logo-section {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 36px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    overflow: hidden;
}

.company-logo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.company-name {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.company-desc {
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.6;
}

/* 功能特色列表 */
.features {
    margin-top: 40px;
    position: relative;
    z-index: 1;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
    opacity: 0.9;
}

.feature-item i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* 右侧登录表单 */
.login-right {
    flex: 1;
    padding: 60px 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 40px;
    text-align: center;
}

/* 表单样式 */
.form-group {
    margin-bottom: 25px;
    position: relative;
}

.login-container .form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    box-sizing: border-box;
    height: auto;
    margin-top: 0;
}

.login-container .form-control:focus {
    outline: none;
    border-color: #4facfe;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

/* 验证码组 */
.captcha-group {
    display: flex;
    gap: 15px;
    align-items: center;
}

.captcha-input {
    flex: 1;
}

.captcha-image {
    width: 120px;
    height: 50px;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid #e1e5e9;
}

/* 记住我选项 */
.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.remember-me input[type="checkbox"] {
    margin-right: 8px;
    margin-top: 0;
    margin-bottom: 0;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    position: relative;
    top: 0;
    flex-shrink: 0;
}

.remember-me label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
    line-height: 1.4;
    vertical-align: middle;
    -webkit-user-select: none;
    user-select: none;
}

/* 登录按钮 */
.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    border-radius: 10px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    pointer-events: auto;
    z-index: 10;
    position: relative;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn i {
    margin-right: 8px;
}

/* 注册链接 */
.register-link {
    text-align: center;
    margin-top: 20px;
}

.register-link span {
    color: #999;
}

.register-link a {
    color: #4facfe;
    text-decoration: none;
}

/* 页脚样式 - 河北九赋登录页面 */
.login-container .footer {
    text-align: center;
    margin-top: 30px;
    padding: 20px 15px;
    border-top: 1px solid #e8ecef;
    color: #666;
    font-size: 13px;
    line-height: 1.6;
    background: linear-gradient(to bottom, transparent, rgba(248, 249, 250, 0.3));
    border-radius: 0 0 20px 20px;
}

.login-container .footer div:first-child {
    font-weight: 500;
    color: #555;
    margin-bottom: 8px;
    letter-spacing: 0.3px;
}

.login-container .footer-subtitle {
    color: #4facfe;
    font-size: 12px;
    font-weight: 400;
    margin-top: 5px;
    display: block;
    position: relative;
}

.login-container .footer-subtitle::before {
    content: "◆";
    color: #4facfe;
    font-size: 8px;
    margin-right: 8px;
    opacity: 0.7;
}

.login-container .footer-subtitle::after {
    content: "◆";
    color: #4facfe;
    font-size: 8px;
    margin-left: 8px;
    opacity: 0.7;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .login-container .footer {
        margin-top: 25px;
        padding: 15px 10px;
        font-size: 12px;
    }

    .login-container .footer-subtitle {
        font-size: 11px;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
        width: 95%;
        min-height: 500px;
    }

    .login-left {
        padding: 40px 30px;
        min-height: 300px;
    }

    .login-right {
        padding: 40px 30px;
    }

    .company-name {
        font-size: 24px;
    }

    .login-title {
        font-size: 24px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-container {
    animation: fadeInUp 0.6s ease-out;
}

/* 验证码刷新动画 */
.captcha-image.refreshing {
    transform: rotate(360deg);
    transition: transform 0.5s ease;
}

/* 表单验证状态 */
.form-group.has-success .form-control {
    border-color: #5cb85c;
}

.form-group.has-warning .form-control {
    border-color: #f0ad4e;
}

.form-group.focused .form-control {
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}