package com.ruoyi.swgx.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 匹配规则配置对象 swgx_pp_gz
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PpGz extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private String id;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String gzMc;

    /** 规则描述 */
    @Excel(name = "规则描述")
    private String gzMs;

    /** 规则类型：EXACT-精确匹配，FUZZY-模糊匹配 */
    @Excel(name = "规则类型", readConverterExp = "EXACT=精确匹配,FUZZY=模糊匹配")
    private String gzLx;

    /** 规则状态：1-启用，0-禁用 */
    @Excel(name = "规则状态", readConverterExp = "1=启用,0=禁用")
    private String gzZt;

    /** 规则优先级(1-10，数字越小优先级越高) */
    @Excel(name = "规则优先级")
    private Integer gzYxj;

    /** 规则内容(JSON格式) */
    private String gzNr;
}
