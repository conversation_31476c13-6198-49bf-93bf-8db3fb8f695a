/**
 * SWGX通用工具类
 * 统一管理数据格式化和显示精度
 */
var SwgxCommon = {
    
    // 数据显示精度配置
    precision: {
        quantity: 12,   // 数量字段小数位数（与数据库decimal(22,12)一致）
        price: 12,      // 单价字段小数位数（与数据库decimal(22,12)一致）
        amount: 2,      // 金额字段小数位数
        taxRate: 3,     // 税率字段小数位数（与数据库decimal(6,3)一致）
        taxAmount: 2    // 税额字段小数位数
    },
    
    /**
     * 格式化数量
     * @param value 数值
     * @returns {string} 格式化后的字符串
     */
    formatQuantity: function(value) {
        return value ? parseFloat(value).toFixed(this.precision.quantity) : '0.' + '0'.repeat(this.precision.quantity);
    },
    
    /**
     * 格式化单价
     * @param value 数值
     * @param showCurrency 是否显示货币符号
     * @returns {string} 格式化后的字符串
     */
    formatPrice: function(value, showCurrency) {
        var formatted = value ? parseFloat(value).toFixed(this.precision.price) : '0.' + '0'.repeat(this.precision.price);
        return showCurrency ? '¥' + formatted : formatted;
    },
    
    /**
     * 格式化金额
     * @param value 数值
     * @param showCurrency 是否显示货币符号
     * @returns {string} 格式化后的字符串
     */
    formatAmount: function(value, showCurrency) {
        var formatted = value ? parseFloat(value).toFixed(this.precision.amount) : '0.' + '0'.repeat(this.precision.amount);
        return showCurrency ? '¥' + formatted : formatted;
    },
    
    /**
     * 格式化税率
     * @param value 数值
     * @param showPercent 是否显示百分号
     * @returns {string} 格式化后的字符串
     */
    formatTaxRate: function(value, showPercent) {
        var formatted = value ? parseFloat(value).toFixed(this.precision.taxRate) : '0.' + '0'.repeat(this.precision.taxRate);
        return showPercent ? (parseFloat(value) * 100).toFixed(2) + '%' : formatted;
    },
    
    /**
     * 格式化税额
     * @param value 数值
     * @param showCurrency 是否显示货币符号
     * @returns {string} 格式化后的字符串
     */
    formatTaxAmount: function(value, showCurrency) {
        var formatted = value ? parseFloat(value).toFixed(this.precision.taxAmount) : '0.' + '0'.repeat(this.precision.taxAmount);
        return showCurrency ? '¥' + formatted : formatted;
    },
    
    /**
     * 数值比较（考虑精度）
     * @param value1 数值1
     * @param value2 数值2
     * @param precision 比较精度
     * @returns {boolean} 是否相等
     */
    isEqual: function(value1, value2, precision) {
        if (!value1 && !value2) return true;
        if (!value1 || !value2) return false;
        
        precision = precision || 6;
        var diff = Math.abs(parseFloat(value1) - parseFloat(value2));
        var tolerance = Math.pow(10, -precision);
        return diff < tolerance;
    },
    
    /**
     * 获取Bootstrap表格的格式化器
     */
    getTableFormatters: function() {
        return {
            // 数量格式化器
            quantity: function(value) {
                return SwgxCommon.formatQuantity(value);
            },
            
            // 单价格式化器
            price: function(value) {
                return SwgxCommon.formatPrice(value, true);
            },
            
            // 金额格式化器
            amount: function(value) {
                return SwgxCommon.formatAmount(value, true);
            },
            
            // 税率格式化器
            taxRate: function(value) {
                return SwgxCommon.formatTaxRate(value, true);
            },
            
            // 税额格式化器
            taxAmount: function(value) {
                return SwgxCommon.formatTaxAmount(value, true);
            }
        };
    }
};

// 全局可用
window.SwgxCommon = SwgxCommon;
