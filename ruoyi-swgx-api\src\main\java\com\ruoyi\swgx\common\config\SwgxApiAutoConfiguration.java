package com.ruoyi.swgx.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 百旺金穗云API自动配置
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Configuration
@EnableCaching
@EnableConfigurationProperties(SwgxApiProperties.class)
@ComponentScan(basePackages = "com.ruoyi.swgx")
@ConditionalOnProperty(prefix = "swgx.api", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SwgxApiAutoConfiguration {

    @PostConstruct
    public void init() {
        log.info("百旺金穗云API模块已启用");
    }
}
