<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>河北九赋管理系统</title>
    <meta name="description" content="河北九赋后台管理系统">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/login.css" th:href="@{/css/login.css}" rel="stylesheet"/>
    <link href="../static/ruoyi/css/ry-ui.css" th:href="@{/ruoyi/css/ry-ui.css?v=4.8.1}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/img/logo.jpg" th:href="@{/img/logo.jpg}"/>



    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body>
    <div class="login-container">
        <!-- 左侧信息区域 -->
        <div class="login-left">
            <div class="logo-section">
                <div class="logo-icon">
                    <img src="../static/img/logo.jpg" th:src="@{/img/logo.jpg}" alt="河北九赋" class="company-logo" />
                </div>
                <div class="company-name">河北九赋</div>
                <div class="company-desc">专业的税务信息化服务提供商</div>
            </div>

            <div class="features">
                <div class="feature-item">
                    <i class="fa fa-file-text-o"></i>
                    <span>税控发票管理系统</span>
                </div>
                <div class="feature-item">
                    <i class="fa fa-calculator"></i>
                    <span>财税一体化解决方案</span>
                </div>
                <div class="feature-item">
                    <i class="fa fa-shield"></i>
                    <span>国家金税工程技术支持</span>
                </div>
                <div class="feature-item">
                    <i class="fa fa-cogs"></i>
                    <span>企业税务信息化服务</span>
                </div>
                <div class="feature-item">
                    <i class="fa fa-headphones"></i>
                    <span>专业技术支持与培训</span>
                </div>
            </div>
        </div>

        <!-- 右侧登录表单 -->
        <div class="login-right">
            <div class="login-title">欢迎登录</div>

            <form id="signupForm" autocomplete="off">
                <div class="form-group">
                    <input type="text" name="username" class="form-control" placeholder="请输入用户名" value="admin" />
                </div>

                <div class="form-group">
                    <input type="password" name="password" class="form-control" placeholder="请输入密码" value="admin123" />
                </div>

                <div class="form-group" th:if="${captchaEnabled==true}">
                    <div class="captcha-group">
                        <input type="text" name="validateCode" class="form-control captcha-input code" placeholder="验证码" maxlength="5" />
                        <a href="javascript:void(0);" title="点击更换验证码">
                            <img th:src="@{/captcha/captchaImage(type=${captchaType})}" class="captcha-image imgcode"/>
                        </a>
                    </div>
                </div>

                <div class="remember-me" th:if="${isRemembered}">
                    <input type="checkbox" id="rememberme" name="rememberme">
                    <label for="rememberme">记住我</label>
                </div>

                <button type="submit" class="login-btn" id="btnSubmit" data-loading="正在验证登录，请稍候...">
                    <i class="fa fa-sign-in"></i>立即登录
                </button>

                <div th:if="${isAllowRegister}" class="register-link">
                    <span>还没有账号？</span>
                    <a th:href="@{/register}">立即注册</a>
                </div>
            </form>

            <div class="footer">
                <div>Copyright © 2025 河北九赋软件技术有限公司 版权所有</div>
            </div>
        </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; var captchaEnabled = [[${captchaEnabled}]];</script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/ruoyi/js/ry-ui.js" th:src="@{/ruoyi/js/ry-ui.js?v=4.8.1}"></script>
<script src="../static/ruoyi/login.js" th:src="@{/ruoyi/login.js}"></script>

</body>
</html>
