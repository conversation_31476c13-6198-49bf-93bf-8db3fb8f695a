package com.ruoyi.swgx.common.dto.invoice;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 发票明细参数
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class InvoiceDetailParam {

    /**
     * 序号（数字型，正整数，从1开始）
     */
    @NotNull(message = "序号不能为空")
    private Integer xh;

    /**
     * 发票行性质
     * 0 正常行
     * 1 折扣行 (折扣票金额负，正票折扣行: 金额、税额是负数，规格型号、单位、单价、数量必须为空)
     * 2 被折扣行(折扣票金额正)
     */
    @NotBlank(message = "发票行性质不能为空")
    private String fphxz;

    /**
     * 含税标识
     * 0:不含税 1:含税 (默认)
     * 与明细内dj和je节点的值相关联
     */
    private String hsbz = "1";

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String spmc;

    /**
     * 商品编码
     */
    @NotBlank(message = "商品编码不能为空")
    private String spbm;

    /**
     * 自行编码（商品简码）
     */
    private String x;

    /**
     * 规格型号
     */
    private String ggxh;

    /**
     * 单位
     */
    private String dw;

    /**
     * 单价（最长保留10位小数）
     */
    private BigDecimal dj;

    /**
     * 数量（最长保留10位小数）
     */
    private BigDecimal sl;

    /**
     * 金额（保留2位小数）
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal je;

    /**
     * 税额（保留2位小数）
     */
    private BigDecimal se;

    /**
     * 税率（最多3位小数）
     */
    @NotNull(message = "税率不能为空")
    private BigDecimal slv;

    /**
     * 零税率标识
     * 0 是正常税率 1是免税 2是不征税 3普通零税率
     */
    @NotBlank(message = "零税率标识不能为空")
    private String lslbs;

    /**
     * 优惠政策标识
     * 0不使用 1使用
     */
    private String yhzcbs;

    /**
     * 增值税特殊管理
     * 优惠政策标识yhzcbs为1时，必须填写内容
     */
    private String zzstsgl;

    /**
     * 即征即退类型代码
     * 01 软件行业
     */
    private String jzjtlxDm;
}
