/**
 * 百旺金穗云API测试页面通用JavaScript函数
 * <AUTHOR>
 * @date 2024-01-01
 */

// 通用工具函数
var SwgxTest = {
    
    /**
     * 显示加载动画
     */
    showLoading: function() {
        if (typeof layer !== 'undefined') {
            layer.load(1, {shade: [0.3, '#000']});
        }
    },
    
    /**
     * 关闭加载动画
     */
    hideLoading: function() {
        if (typeof layer !== 'undefined') {
            layer.closeAll('loading');
        }
    },
    
    /**
     * 显示成功消息
     */
    showSuccess: function(message) {
        if (typeof layer !== 'undefined') {
            layer.msg(message, {icon: 1});
        } else {
            alert(message);
        }
    },
    
    /**
     * 显示错误消息
     */
    showError: function(message) {
        if (typeof layer !== 'undefined') {
            layer.msg(message, {icon: 2});
        } else {
            alert(message);
        }
    },
    
    /**
     * 显示信息消息
     */
    showInfo: function(message) {
        if (typeof layer !== 'undefined') {
            layer.msg(message, {icon: 0});
        } else {
            alert(message);
        }
    },

    /**
     * 显示警告消息
     */
    showWarning: function(message) {
        if (typeof layer !== 'undefined') {
            layer.msg(message, {icon: 3});
        } else {
            alert(message);
        }
    },
    
    /**
     * 禁用按钮
     */
    disableButtons: function() {
        $('.test-btn').prop('disabled', true);
    },
    
    /**
     * 启用按钮
     */
    enableButtons: function() {
        $('.test-btn').prop('disabled', false);
    },
    
    /**
     * 显示结果
     */
    showResult: function(containerId, data, isSuccess) {
        var container = $('#' + containerId);
        var resultCard = container.find('.result-card');
        var resultContent = container.find('#resultContent, .result-content');
        
        if (isSuccess) {
            resultCard.removeClass('result-error').addClass('result-success');
            resultCard.find('h4').html('<i class="fa fa-check-circle text-success"></i> 操作成功');
            
            var html = '<div class="row">';
            if (typeof data === 'object' && data !== null) {
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        var value = data[key];
                        if (typeof value === 'string' && (key.toLowerCase().includes('url') || value.startsWith('http'))) {
                            html += '<div class="col-md-12"><strong>' + key + ':</strong> <a href="' + value + '" target="_blank" rel="noopener">' + value + '</a></div>';
                        } else if (typeof value === 'object') {
                            html += '<div class="col-md-12"><strong>' + key + ':</strong><pre>' + JSON.stringify(value, null, 2) + '</pre></div>';
                        } else {
                            html += '<div class="col-md-6"><strong>' + key + ':</strong> ' + value + '</div>';
                        }
                    }
                }
            } else {
                html += '<div class="col-md-12">' + data + '</div>';
            }
            html += '</div>';
            
            resultContent.html(html);
        } else {
            resultCard.removeClass('result-success').addClass('result-error');
            resultCard.find('h4').html('<i class="fa fa-times-circle text-danger"></i> 操作失败');

            // 处理多行错误信息
            var errorHtml = '<div class="text-danger">';
            if (typeof data === 'string' && data.includes('\n')) {
                // 将换行符转换为HTML换行
                errorHtml += data.replace(/\n/g, '<br>');
            } else {
                errorHtml += data;
            }
            errorHtml += '</div>';

            resultContent.html(errorHtml);
        }
        
        container.removeClass('hidden').show();
        
        // 滚动到结果区域
        $('html, body').animate({
            scrollTop: container.offset().top - 100
        }, 500);
    },
    
    /**
     * AJAX请求封装
     */
    request: function(options) {
        var defaults = {
            type: 'GET',
            dataType: 'json',
            timeout: 30000,
            beforeSend: function() {
                SwgxTest.disableButtons();
                SwgxTest.showLoading();
            },
            complete: function() {
                SwgxTest.enableButtons();
                SwgxTest.hideLoading();
            },
            success: function(result, textStatus, xhr) {
                console.log('收到响应:', result);
                if (result.code == 200) {
                    if (options.onSuccess) {
                        // 传递完整的参数：data, textStatus, xhr
                        options.onSuccess(result.data, textStatus, xhr);
                    }
                    if (options.successMessage) {
                        SwgxTest.showSuccess(options.successMessage);
                    }
                } else {
                    console.log('错误响应:', result);
                    if (options.onError) {
                        // 传递xhr对象以便获取响应信息
                        options.onError(result.msg, xhr);
                    } else {
                        SwgxTest.showError('操作失败: ' + result.msg);
                    }
                }
            },
            error: function(xhr, status, error) {
                var message = '请求失败';
                if (status === 'timeout') {
                    message = '请求超时，请稍后重试';
                } else if (xhr.status === 404) {
                    message = '接口不存在';
                } else if (xhr.status === 500) {
                    message = '服务器内部错误';
                } else {
                    message = '网络错误: ' + error;
                }
                
                if (options.onError) {
                    // 传递xhr对象以便获取更多响应信息
                    options.onError(message, xhr);
                } else {
                    SwgxTest.showError(message);
                }
            }
        };
        
        var settings = $.extend({}, defaults, options);
        return $.ajax(settings);
    },
    
    /**
     * 格式化JSON显示
     */
    formatJson: function(obj) {
        return JSON.stringify(obj, null, 2);
    },
    
    /**
     * 生成订单号
     */
    generateOrderNumber: function(prefix) {
        prefix = prefix || 'SWGX';
        var timestamp = new Date().getTime();
        var random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return prefix + timestamp + random;
    },
    
    /**
     * 计算税额
     */
    calculateTax: function(amount, taxRate) {
        if (!amount || amount <= 0) {
            return null;
        }
        
        var taxAmount = (amount * taxRate);
        var totalAmount = amount + taxAmount;
        
        return {
            amount: parseFloat(amount).toFixed(2),
            taxRate: (taxRate * 100).toFixed(1) + '%',
            taxAmount: taxAmount.toFixed(2),
            totalAmount: totalAmount.toFixed(2)
        };
    },
    
    /**
     * 验证表单
     */
    validateForm: function(formId, rules) {
        var form = $('#' + formId);
        var isValid = true;
        var firstErrorField = null;
        
        for (var fieldName in rules) {
            var field = form.find('[name="' + fieldName + '"]');
            var rule = rules[fieldName];
            var value = field.val().trim();
            
            // 必填验证
            if (rule.required && !value) {
                SwgxTest.showError(rule.message || fieldName + '不能为空');
                if (!firstErrorField) {
                    firstErrorField = field;
                }
                isValid = false;
                break;
            }
            
            // 长度验证
            if (rule.minLength && value.length < rule.minLength) {
                SwgxTest.showError(rule.message || fieldName + '长度不能少于' + rule.minLength + '个字符');
                if (!firstErrorField) {
                    firstErrorField = field;
                }
                isValid = false;
                break;
            }
            
            if (rule.maxLength && value.length > rule.maxLength) {
                SwgxTest.showError(rule.message || fieldName + '长度不能超过' + rule.maxLength + '个字符');
                if (!firstErrorField) {
                    firstErrorField = field;
                }
                isValid = false;
                break;
            }
            
            // 正则验证
            if (rule.pattern && !rule.pattern.test(value)) {
                SwgxTest.showError(rule.message || fieldName + '格式不正确');
                if (!firstErrorField) {
                    firstErrorField = field;
                }
                isValid = false;
                break;
            }
        }
        
        // 聚焦到第一个错误字段
        if (firstErrorField) {
            firstErrorField.focus();
        }
        
        return isValid;
    },
    
    /**
     * 初始化页面动画
     */
    initAnimations: function() {
        // 页面加载动画
        $('.test-card').each(function(index) {
            var card = $(this);
            setTimeout(function() {
                card.addClass('animated fadeInUp');
            }, index * 200);
        });
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    SwgxTest.initAnimations();
    
    // 回车键提交表单
    $('input').keypress(function(e) {
        if (e.which == 13) {
            var submitBtn = $(this).closest('form').find('.test-btn[onclick*="submit"], .test-btn[onclick*="query"], .test-btn[onclick*="apply"]').first();
            if (submitBtn.length > 0) {
                submitBtn.click();
            }
        }
    });
});
