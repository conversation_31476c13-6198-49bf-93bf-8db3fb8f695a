<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>河北九赋后台管理系统</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
    <link href="../static/css/main.css" th:href="@{/css/main.css}" rel="stylesheet"/>
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <!-- 顶部统计卡片 -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-success pull-right">在线</span>
                        <h5>系统状态</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins">运行正常</h1>
                        <div class="stat-percent font-bold text-success">
                            <i class="fa fa-check"></i>
                        </div>
                        <small>系统运行状态良好</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-info pull-right">版本</span>
                        <h5>系统版本</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins" th:text="${version}">v4.8.1</h1>
                        <div class="stat-percent font-bold text-info">
                            <i class="fa fa-code"></i>
                        </div>
                        <small>当前系统版本</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-primary pull-right">实时</span>
                        <h5>当前时间</h5>
                    </div>
                    <div class="ibox-content">
                        <h2 class="no-margins" id="currentTime">--:--:--</h2>
                        <div class="stat-percent font-bold text-primary">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <small>系统当前时间</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-warning pull-right">操作</span>
                        <h5>快速操作</h5>
                    </div>
                    <div class="ibox-content">
                        <button type="button" class="btn btn-primary btn-block" id="refreshData">
                            <i class="fa fa-refresh"></i> 刷新数据
                        </button>
                        <small>刷新页面数据</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 系统公告 -->
            <div class="col-lg-8">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-bullhorn"></i> 系统公告</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="feed-activity-list" id="noticeList">
                            <!-- 公告列表将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="col-lg-4">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-info-circle"></i> 系统信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-xs-4">
                                <small class="stats-label">浏览器</small>
                                <h4 id="browserInfo">-</h4>
                            </div>
                            <div class="col-xs-8">
                                <small class="stats-label">操作系统</small>
                                <h4 id="platformInfo">-</h4>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-4">
                                <small class="stats-label">分辨率</small>
                                <h4 id="screenInfo">-</h4>
                            </div>
                            <div class="col-xs-8">
                                <small class="stats-label">语言</small>
                                <h4 id="languageInfo">-</h4>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 公司信息 -->
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5><i class="fa fa-building"></i> 公司信息</h5>
                    </div>
                    <div class="ibox-content text-center">
                        <div class="m-b-sm">
                            <img src="../static/img/logo.jpg" th:src="@{/img/logo.jpg}" alt="河北九赋" class="img-circle company-logo-circle">
                        </div>
                        <h3 class="font-bold">河北九赋科技有限公司</h3>
                        <p class="text-muted">致力于为企业提供高效、安全、稳定的管理系统解决方案</p>
                        <div class="m-t-xs">
                            <span class="label label-primary">企业级</span>
                            <span class="label label-success">安全稳定</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script th:src="@{/ruoyi/js/ry-ui.js}"></script>
    <script th:src="@{/js/main.js}"></script>
    <script>
        var ctx = /*[[${#httpServletRequest.contextPath}]]*/ '';
        if (ctx.length > 0) {
            ctx = ctx + '/';
        } else {
            ctx = '/';
        }
    </script>
</body>
</html>
