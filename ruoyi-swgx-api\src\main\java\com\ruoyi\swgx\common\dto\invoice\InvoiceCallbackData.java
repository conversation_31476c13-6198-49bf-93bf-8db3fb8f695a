package com.ruoyi.swgx.common.dto.invoice;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 发票开具回调数据
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class InvoiceCallbackData {

    /**
     * 业务类型代码（fpkj）
     */
    private String ywlxdm;

    /**
     * 订单流水号
     */
    private String ddlsh;

    /**
     * 请求状态
     * 1开具成功 2 开具失败 3 已开具未生成PDF（税控电子票） 1004 需要登录 1003 需要认证
     */
    private String status;

    /**
     * 全电发票号码
     */
    private String qdfphm;

    /**
     * 发票代码
     */
    private String fpdm;

    /**
     * 发票号码
     */
    private String fphm;

    /**
     * 开票日期（yyyymmddhhmmsssss）
     */
    private String kprq;

    /**
     * 价税合计
     */
    private BigDecimal jshj;

    /**
     * 合计金额
     */
    private BigDecimal hjje;

    /**
     * 合计税额
     */
    private BigDecimal hjse;

    /**
     * 单据编号
     */
    private String djbh;

    /**
     * 原始单据编号
     */
    private List<String> djbhs;

    /**
     * 异常原因
     */
    private String ycyy;

    // 全量回调报文额外字段
    /**
     * 征收方式
     */
    private String zsfs;

    /**
     * 二维码（Base64编码）
     */
    private String ewm;

    /**
     * 发票类型代码
     */
    private String fplxdm;

    /**
     * 购方电话
     */
    private String gmfDh;

    /**
     * 购方地址
     */
    private String gmfDz;

    /**
     * 购方税号
     */
    private String gmfNsrsbh;

    /**
     * 购买方名称
     */
    private String gmfMc;

    /**
     * 购方银行名称
     */
    private String gmfYh;

    /**
     * 购方银行账号
     */
    private String gmfZh;

    /**
     * 销方电话
     */
    private String xsfDh;

    /**
     * 销方地址
     */
    private String xsfDz;

    /**
     * 销方名称
     */
    private String xsfMc;

    /**
     * 销方银行名称
     */
    private String xsfYh;

    /**
     * 销方银行账号
     */
    private String xsfZh;

    /**
     * 发票明细信息
     */
    private List<InvoiceDetailCallbackParam> detailParam;

    @Data
    public static class InvoiceDetailCallbackParam {
        /**
         * 发票行性质
         */
        private String fphxz;

        /**
         * 商品名称
         */
        private String spmc;

        /**
         * 商品编码
         */
        private String spbm;

        /**
         * 不含税金额
         */
        private BigDecimal bhsJe;

        /**
         * 税率
         */
        private BigDecimal slv;

        /**
         * 税额
         */
        private BigDecimal se;

        /**
         * 价税合计
         */
        private BigDecimal je;

        /**
         * 零税率标识
         */
        private String lslbs;
    }
}
