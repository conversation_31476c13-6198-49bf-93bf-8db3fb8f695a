<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('电票发票信息管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>发票代码：</label>
                                <input type="text" name="fpDm" placeholder="请输入发票代码"/>
                            </li>
                            <li>
                                <label>发票号码：</label>
                                <input type="text" name="fpHm" placeholder="请输入发票号码"/>
                            </li>
                            <li>
                                <label>发票状态：</label>
                                <select name="fpzt" th:with="type=${@dict.getType('dp_fpxx_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>购方名称：</label>
                                <input type="text" name="gmfMc" placeholder="请输入购买方名称"/>
                            </li>
                            <li>
                                <label>开票日期：</label>
                                <input type="text" class="time-input" name="kprq" placeholder="请选择开票日期"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="swgx:dpFpxx:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="swgx:dpFpxx:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="swgx:dpFpxx:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="swgx:dpFpxx:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="importData()" shiro:hasPermission="swgx:dpFpxx:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-info" onclick="initInvoiceDetailMatchStatus()" shiro:hasPermission="swgx:dpFpxx:init">
                    <i class="fa fa-refresh"></i> 初始化匹配状态
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('swgx:dpFpxx:edit')}]];
        var removeFlag = [[${@permission.hasPermi('swgx:dpFpxx:remove')}]];
        var initFlag = [[${@permission.hasPermi('swgx:dpFpxx:init')}]];
        var prefix = ctx + "swgx/dpFpxx";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/{ids}",
                exportUrl: prefix + "/export",
                modalName: "电票发票信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'fpDm',
                    title: '发票代码',
                    sortable: true
                },
                {
                    field: 'fpHm',
                    title: '发票号码',
                    sortable: true
                },
                {
                    field: 'fpzt',
                    title: '发票状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel([[${@dict.getType('dp_fpxx_status')}]], value);
                    }
                },
                {
                    field: 'kprq',
                    title: '开票日期',
                    sortable: true,
                    formatter: function(value, row, index) {
                        if (value && value.length >= 8) {
                            return value.substring(0, 4) + '-' + value.substring(4, 6) + '-' + value.substring(6, 8);
                        }
                        return value;
                    }
                },
                {
                    field: 'xsfMc',
                    title: '销售方名称'
                },
                {
                    field: 'gmfMc',
                    title: '购买方名称'
                },
                {
                    field: 'jshj',
                    title: '价税合计',
                    align: 'right',
                    formatter: function(value, row, index) {
                        return value ? parseFloat(value).toFixed(2) : '0.00';
                    }
                },
                {
                    field: 'hjje',
                    title: '合计金额',
                    align: 'right',
                    formatter: function(value, row, index) {
                        return value ? parseFloat(value).toFixed(2) : '0.00';
                    }
                },
                {
                    field: 'hjse',
                    title: '合计税额',
                    align: 'right',
                    formatter: function(value, row, index) {
                        return value ? parseFloat(value).toFixed(2) : '0.00';
                    }
                },
                {
                    field: 'zyspmc',
                    title: '主要商品名称'
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 200,
                    fixed: 'right',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');

                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 查看发票详情
        function viewDetail(id) {
            var url = prefix + "/detail/" + id;
            // 使用弹窗形式打开详情页面
            $.modal.open("发票详情", url, '1100', '600');
        }

        // 导入数据
        function importData() {
            var url = prefix + "/importData";
            $.modal.open("导入发票数据", url);
        }

        // 统计信息
        function showStatistics() {
            var url = ctx + "swgx/dpFpxx/statistics";
            $.modal.openTab("发票统计", url);
        }

        /**
         * 初始化电票明细匹配状态
         */
        function initInvoiceDetailMatchStatus() {
            var confirmMsg = "确认要初始化电票明细匹配状态吗？<br/>" +
                "<div class='text-info' style='margin-top: 10px;'>" +
                "<i class='fa fa-info-circle'></i> 说明：<br/>" +
                "• 此操作会为所有未初始化的电票明细创建匹配状态记录<br/>" +
                "• 只有初始化后的电票明细才能参与发票匹配<br/>" +
                "• 已初始化的明细不会重复处理<br/>" +
                "• 数据量大时可能需要较长时间，请耐心等待" +
                "</div>";

            $.modal.confirm(confirmMsg, function() {
                $.modal.loading("正在初始化电票明细匹配状态，请稍候...");

                $.ajax({
                    url: prefix + "/initInvoiceDetailMatchStatus",
                    type: "post",
                    dataType: "json",
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess("电票明细匹配状态初始化完成！<br/>现在可以在业务单据管理页面执行匹配操作了。");
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.msgError("初始化请求失败，请检查网络连接或联系管理员");
                    }
                });
            });
        }
    </script>
</body>
</html>
