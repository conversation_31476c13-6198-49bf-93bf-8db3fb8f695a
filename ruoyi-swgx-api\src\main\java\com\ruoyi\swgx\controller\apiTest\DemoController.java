package com.ruoyi.swgx.controller.apiTest;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.swgx.common.config.SwgxApiProperties;
import com.ruoyi.swgx.common.dto.company.QueryUniqueSignResponse;
import com.ruoyi.swgx.common.dto.invoice.ApplyInvoiceRequest;
import com.ruoyi.swgx.common.dto.invoice.ApplyInvoiceResponse;
import com.ruoyi.swgx.common.dto.invoice.InvoiceDetailParam;
import com.ruoyi.swgx.common.exception.SwgxApiException;
import com.ruoyi.swgx.service.apiTest.CompanyService;
import com.ruoyi.swgx.service.apiTest.InvoiceService;
import com.ruoyi.swgx.util.OrderNumberGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 百旺金穗云API演示控制器
 * 展示如何使用API接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Controller
@RequestMapping("/swgx/demo")
public class DemoController extends BaseController {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private SwgxApiProperties properties;

    /**
     * 测试页面主页
     */
    @GetMapping("/test")
    public String testPage() {
        return "swgx/test";
    }

    /**
     * 企业查询测试页面
     */
    @GetMapping("/company")
    public String companyPage() {
        return "swgx/company";
    }

    /**
     * 发票开具测试页面
     */
    @GetMapping("/invoice")
    public String invoicePage() {
        return "swgx/invoice";
    }

    /**
     * 工具测试页面
     */
    @GetMapping("/utils")
    public String utilsPage() {
        return "swgx/utils";
    }

    /**
     * 查询企业唯一标识
     */
    @PostMapping("/queryCompany")
    @ResponseBody
    public AjaxResult queryCompany(String nsrmc, String nsrsbh) {
        try {
            if (StringUtils.isEmpty(nsrmc)) {
                return AjaxResult.error("企业名称不能为空");
            }
            if (StringUtils.isEmpty(nsrsbh)) {
                return AjaxResult.error("纳税人识别号不能为空");
            }

            log.info("查询企业唯一标识: nsrmc={}, nsrsbh={}", nsrmc, nsrsbh);

            QueryUniqueSignResponse response = companyService.queryUniqueSign(nsrmc, nsrsbh);

            Map<String, Object> result = new HashMap<>();
            result.put("企业名称", response.getNsrmc());
            result.put("纳税人识别号", response.getNsrsbh());
            result.put("企业唯一标识", response.getQyId());
            result.put("说明", "企业唯一标识已自动缓存24小时，无需重复查询");

            return AjaxResult.success("查询成功", result);

        } catch (Exception e) {
            log.error("查询企业唯一标识失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 发票开具申请（完整表单）
     */
    @PostMapping("/applyInvoice")
    @ResponseBody
    public AjaxResult applyInvoice(@RequestBody Map<String, Object> requestData) {
        try {
            log.info("发票开具申请: {}", requestData);

            // 构建发票开具请求
            ApplyInvoiceRequest request = buildFullInvoiceRequest(requestData);

            ApplyInvoiceResponse response = invoiceService.quickApplyInvoice(request);

            Map<String, Object> result = new HashMap<>();
            result.put("订单流水号", request.getDdlsh());
            result.put("交付码URL", response.getJfmUrl());
            result.put("固定交付码URL", response.getGdJfmUrl());
            result.put("说明", "发票开具是异步操作，请通过回调接口获取开具结果");

            return AjaxResult.success("申请成功", result);

        } catch (SwgxApiException e) {
            log.error("发票开具申请失败 - SwgxApiException: code={}, message={}, reason={}",
                    e.getCode(), e.getMessage(), e.getReason(), e);
            String errorMessage = getDetailedErrorMessage(e);
            return AjaxResult.error(errorMessage);
        } catch (Exception e) {
            log.error("发票开具申请失败 - 其他异常", e);
            String errorMessage = getDetailedErrorMessage(e);
            return AjaxResult.error(errorMessage);
        }
    }

    /**
     * 快速发票开具申请
     */
    @PostMapping("/quickApplyInvoice")
    @ResponseBody
    public AjaxResult quickApplyInvoice(String gfmc, String gfnsrsbh, String spmc, String je) {
        try {
            // 参数验证
            if (StringUtils.isEmpty(gfmc)) {
                return AjaxResult.error("购买方名称不能为空");
            }
            if (StringUtils.isEmpty(gfnsrsbh)) {
                return AjaxResult.error("购买方纳税人识别号不能为空");
            }
            if (StringUtils.isEmpty(spmc)) {
                return AjaxResult.error("商品名称不能为空");
            }
            if (StringUtils.isEmpty(je)) {
                return AjaxResult.error("金额不能为空");
            }

            log.info("快速发票开具申请: gfmc={}, gfnsrsbh={}, spmc={}, je={}", gfmc, gfnsrsbh, spmc, je);

            // 构建发票开具请求，使用用户输入的数据
            ApplyInvoiceRequest request = buildInvoiceRequest(gfmc, gfnsrsbh, spmc, je);

            ApplyInvoiceResponse response = invoiceService.quickApplyInvoice(request);

            Map<String, Object> result = new HashMap<>();
            result.put("购买方名称", gfmc);
            result.put("购买方纳税人识别号", gfnsrsbh);
            result.put("商品名称", spmc);
            result.put("金额", je);
            result.put("订单流水号", request.getDdlsh());
            result.put("交付码URL", response.getJfmUrl());
            result.put("说明", "系统已自动从配置中获取企业唯一标识并开具发票");

            return AjaxResult.success("申请成功", result);

        } catch (Exception e) {
            log.error("快速发票开具申请失败", e);
            String errorMessage = getDetailedErrorMessage(e);
            return AjaxResult.error(errorMessage);
        }
    }

    /**
     * 工具类使用演示
     */
    @GetMapping("/utilsApi")
    @ResponseBody
    public AjaxResult utilsDemo() {
        Map<String, Object> result = new HashMap<>();

        // 订单号生成工具
        Map<String, String> orderGeneration = new HashMap<>();
        orderGeneration.put("SWGX订单号", OrderNumberGenerator.generate("SWGX"));
        orderGeneration.put("INV订单号", OrderNumberGenerator.generate("INV"));
        orderGeneration.put("默认订单号", OrderNumberGenerator.generate());
        result.put("订单号生成工具", orderGeneration);

        // 发票金额计算工具
        Map<String, Object> calculationTools = new HashMap<>();
        calculationTools.put("说明", "支持多种税率的金额计算");
        calculationTools.put("支持税率", Arrays.asList("13%", "9%", "6%", "3%"));
        calculationTools.put("计算公式", "税额 = 不含税金额 × 税率，价税合计 = 不含税金额 + 税额");
        result.put("金额计算工具", calculationTools);

        // 发票类型验证工具
        Map<String, Object> validationTools = new HashMap<>();
        validationTools.put("数电普票", "02");
        validationTools.put("数电专票", "01");
        validationTools.put("说明", "验证发票类型代码是否有效");
        result.put("发票类型验证", validationTools);

        // 系统信息
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("当前时间", System.currentTimeMillis());
        systemInfo.put("系统版本", "1.0.0");
        systemInfo.put("API模块", "ruoyi-swgx-api");
        result.put("系统信息", systemInfo);

        return AjaxResult.success("工具演示", result);
    }

    /**
     * 构建发票开具请求（使用系统配置）
     */
    private ApplyInvoiceRequest buildDemoInvoiceRequest() {
        ApplyInvoiceRequest request = new ApplyInvoiceRequest();

        // 基本信息
        request.setQyId(null); // 让系统自动获取
        request.setDdlsh(OrderNumberGenerator.generate("SWGX"));
        request.setSksbh(""); // 从配置获取
        request.setFplxdm("02"); // 数电普票
        request.setKplx("0"); // 蓝字发票

        // 销售方信息（从配置获取）
        request.setXsfMc("河北九赋");
        request.setXsfNsrsbh(""); // 从配置获取
        request.setXsfDz(""); // 从配置获取
        request.setXsfDh(""); // 从配置获取
        request.setXsfYh(""); // 从配置获取
        request.setXsfZh(""); // 从配置获取

        // 购买方信息（需要用户输入）
        request.setGmfMc(""); // 需要用户输入

        // 开票人员信息
        request.setKpr("系统开票");
        request.setSqr("系统申请");
        request.setDlzh("");

        // 发票明细（默认服务项目）
        InvoiceDetailParam detail = new InvoiceDetailParam();
        detail.setXh(1);
        detail.setFphxz("0"); // 正常行
        detail.setSpmc("技术服务费");
        detail.setSpbm("3040407990000000000");
        detail.setJe(new BigDecimal("1000.00"));
        detail.setSlv(new BigDecimal("0.13"));
        detail.setLslbs("0"); // 正常税率

        request.setDetailParam(Arrays.asList(detail));

        return request;
    }

    /**
     * 构建发票开具请求（使用用户输入数据）
     */
    private ApplyInvoiceRequest buildInvoiceRequest(String gfmc, String gfnsrsbh, String spmc, String je) {
        ApplyInvoiceRequest request = new ApplyInvoiceRequest();

        // 基本信息
        request.setQyId(null); // 让系统自动获取
        request.setDdlsh(OrderNumberGenerator.generate("SWGX"));
        request.setSksbh(""); // 从配置获取
        request.setFplxdm("02"); // 数电普票
        request.setKplx("0"); // 蓝字发票

        // 销售方信息（从配置获取）
        request.setXsfMc("河北九赋");
        request.setXsfNsrsbh(""); // 从配置获取
        request.setXsfDz(""); // 从配置获取
        request.setXsfDh(""); // 从配置获取
        request.setXsfYh(""); // 从配置获取
        request.setXsfZh(""); // 从配置获取

        // 购买方信息（使用用户输入）
        request.setGmfMc(gfmc);
        request.setGmfNsrsbh(gfnsrsbh);

        // 开票人员信息
        request.setKpr("系统开票");
        request.setSqr("系统申请");
        request.setDlzh("");

        // 发票明细（使用用户输入）
        InvoiceDetailParam detail = new InvoiceDetailParam();
        detail.setXh(1);
        detail.setFphxz("0"); // 正常行
        detail.setSpmc(spmc);
        detail.setSpbm("3040407990000000000"); // 默认商品编码
        detail.setJe(new BigDecimal(je));
        detail.setSlv(new BigDecimal("0.13")); // 默认13%税率
        detail.setLslbs("0"); // 正常税率

        request.setDetailParam(Arrays.asList(detail));

        return request;
    }

    /**
     * 构建完整的发票开具请求（使用表单数据）
     */
    @SuppressWarnings("unchecked")
    private ApplyInvoiceRequest buildFullInvoiceRequest(Map<String, Object> requestData) {
        ApplyInvoiceRequest request = new ApplyInvoiceRequest();

        // 基本信息
        request.setQyId(null); // 让系统自动获取
        request.setDdlsh(OrderNumberGenerator.generate("SWGX"));
        request.setSksbh(""); // 从配置获取
        request.setFplxdm((String) requestData.get("fplxdm"));
        request.setKplx((String) requestData.get("kplx"));

        // 销售方信息（从配置获取）
        request.setXsfMc("河北九赋");
        request.setXsfNsrsbh("123"); // 从配置获取
        request.setXsfDz("河北省石家庄市"); // 从配置获取
        request.setXsfDh("0311-12345678"); // 从配置获取
        request.setXsfYh("中国银行"); // 从配置获取
        request.setXsfZh("1234567890"); // 从配置获取

        // 购买方信息（使用用户输入）
        request.setGmfMc((String) requestData.get("gmfMc"));
        request.setGmfNsrsbh((String) requestData.get("gmfNsrsbh"));
        request.setGmfDz((String) requestData.get("gmfDz"));
        request.setGmfDh((String) requestData.get("gmfDh"));
        request.setGmfYh((String) requestData.get("gmfYh"));
        request.setGmfZh((String) requestData.get("gmfZh"));
        request.setGmfMobile((String) requestData.get("gmfMobile"));

        // 开票人员信息
        request.setKpr((String) requestData.get("kpr"));
        request.setSqr((String) requestData.get("sqr"));
        request.setSkr((String) requestData.get("skr"));
        request.setFhr((String) requestData.get("fhr"));
        request.setDlzh((String) requestData.get("dlzh"));

        // 备注
        request.setBz((String) requestData.get("bz"));

        // 发票明细
        List<Map<String, Object>> detailsData = (List<Map<String, Object>>) requestData.get("details");
        List<InvoiceDetailParam> details = new ArrayList<>();

        if (detailsData != null) {
            for (Map<String, Object> detailData : detailsData) {
                InvoiceDetailParam detail = new InvoiceDetailParam();
                detail.setXh(((Number) detailData.get("xh")).intValue());
                detail.setFphxz((String) detailData.get("fphxz"));
                detail.setSpmc((String) detailData.get("spmc"));
                detail.setSpbm((String) detailData.get("spbm"));
                detail.setGgxh((String) detailData.get("ggxh"));
                detail.setDw((String) detailData.get("dw"));

                // 处理数值字段
                if (detailData.get("sl") != null) {
                    detail.setSl(new BigDecimal(detailData.get("sl").toString()));
                }
                if (detailData.get("dj") != null) {
                    detail.setDj(new BigDecimal(detailData.get("dj").toString()));
                }
                if (detailData.get("je") != null) {
                    detail.setJe(new BigDecimal(detailData.get("je").toString()));
                }
                if (detailData.get("slv") != null) {
                    detail.setSlv(new BigDecimal(detailData.get("slv").toString()));
                }

                detail.setLslbs((String) detailData.get("lslbs"));
                details.add(detail);
            }
        }

        request.setDetailParam(details);

        return request;
    }

    /**
     * 获取详细的错误信息
     */
    private String getDetailedErrorMessage(Exception e) {
        String message = e.getMessage();
        String reason = null;

        // 如果是SwgxApiException，获取reason信息
        if (e instanceof SwgxApiException) {
            SwgxApiException swgxException = (SwgxApiException) e;
            reason = swgxException.getReason();
            log.info("处理SwgxApiException: code={}, message={}, reason={}",
                    swgxException.getCode(), message, reason);
        }

        // 检查是否是企业信息相关的错误
        if ((message != null && (message.contains("企业信息不存在") || message.contains("企业唯一标识"))) ||
            (reason != null && (reason.contains("企业信息不存在") || reason.contains("企业唯一标识")))) {
            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append("企业配置错误：");

            // 检查具体的配置问题
            try {
                // 检查API密钥配置
                if ("app-id".equals(properties.getAppId()) || "app-secret".equals(properties.getAppSecret())) {
                    errorMsg.append("\n• API密钥未正确配置，请设置有效的 app-id 和 app-secret");
                }

                // 检查企业信息配置
                SwgxApiProperties.CompanyConfig company = properties.getCompany();
                if ("123".equals(company.getTaxNumber()) || StringUtils.isEmpty(company.getTaxNumber())) {
                    errorMsg.append("\n• 纳税人识别号未正确配置，请设置有效的税号");
                }

                if ("unique".equals(company.getUniqueId()) || StringUtils.isEmpty(company.getUniqueId())) {
                    errorMsg.append("\n• 企业唯一标识未配置，请设置有效的企业唯一标识");
                }

                errorMsg.append("\n\n请在 application.yml 中正确配置 swgx.api 相关参数");

            } catch (Exception configEx) {
                log.warn("检查配置时出错", configEx);
            }

            return errorMsg.toString();
        }

        // 检查是否是网络连接错误
        if (message != null && (message.contains("Connection") || message.contains("timeout"))) {
            return "网络连接失败，请检查网络连接或API服务是否可用";
        }

        // 检查是否是认证错误
        if (message != null && (message.contains("认证") || message.contains("授权") || message.contains("签名"))) {
            return "API认证失败，请检查 app-id 和 app-secret 配置是否正确";
        }

        // 返回原始错误信息
        return "申请失败: " + (message != null ? message : "未知错误");
    }
}
