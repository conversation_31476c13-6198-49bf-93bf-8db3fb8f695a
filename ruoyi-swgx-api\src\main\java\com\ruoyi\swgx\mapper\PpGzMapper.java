package com.ruoyi.swgx.mapper;

import java.util.List;
import com.ruoyi.swgx.domain.PpGz;

/**
 * 匹配规则配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface PpGzMapper 
{
    /**
     * 查询匹配规则配置
     * 
     * @param id 匹配规则配置主键
     * @return 匹配规则配置
     */
    public PpGz selectPpGzById(String id);

    /**
     * 查询匹配规则配置列表
     * 
     * @param ppGz 匹配规则配置
     * @return 匹配规则配置集合
     */
    public List<PpGz> selectPpGzList(PpGz ppGz);

    /**
     * 新增匹配规则配置
     * 
     * @param ppGz 匹配规则配置
     * @return 结果
     */
    public int insertPpGz(PpGz ppGz);

    /**
     * 修改匹配规则配置
     * 
     * @param ppGz 匹配规则配置
     * @return 结果
     */
    public int updatePpGz(PpGz ppGz);

    /**
     * 删除匹配规则配置
     * 
     * @param id 匹配规则配置主键
     * @return 结果
     */
    public int deletePpGzById(String id);

    /**
     * 批量删除匹配规则配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePpGzByIds(String[] ids);
}
