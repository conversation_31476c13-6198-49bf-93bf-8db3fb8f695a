package com.ruoyi.swgx.common.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.swgx.common.config.SwgxApiProperties;
import com.ruoyi.swgx.common.constants.SwgxApiConstants;
import com.ruoyi.swgx.common.dto.SwgxApiResponse;
import com.ruoyi.swgx.common.exception.SwgxApiException;
import com.ruoyi.swgx.util.SignatureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.UUID;

/**
 * 百旺金穗云HTTP客户端
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Component
public class SwgxHttpClient {

    @Autowired
    private SwgxApiProperties properties;

    private CloseableHttpClient httpClient;
    private PoolingHttpClientConnectionManager connectionManager;
    private ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        // 初始化连接池
        connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(properties.getHttp().getMaxConnections());
        connectionManager.setDefaultMaxPerRoute(properties.getHttp().getMaxConnectionsPerRoute());

        // 初始化HTTP客户端
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(properties.getHttp().getConnectTimeout())
                .setSocketTimeout(properties.getHttp().getReadTimeout())
                .build();

        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();

        // 初始化JSON处理器
        objectMapper = new ObjectMapper();

        log.info("SwgxHttpClient initialized with baseUrl: {}", properties.getBaseUrl());
    }

    @PreDestroy
    public void destroy() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
            if (connectionManager != null) {
                connectionManager.close();
            }
        } catch (IOException e) {
            log.error("Error closing HTTP client", e);
        }
    }

    /**
     * 发送POST请求
     */
    public <T> SwgxApiResponse<T> post(String path, Object request, Class<T> responseType) {
        String url = properties.getBaseUrl() + path;
        
        try {
            // 序列化请求对象
            String requestJson = objectMapper.writeValueAsString(request);
            String requestBase64 = Base64.getEncoder().encodeToString(requestJson.getBytes(StandardCharsets.UTF_8));

            // 创建HTTP请求
            HttpPost httpPost = new HttpPost(url);
            
            // 设置请求体
            httpPost.setEntity(new StringEntity(requestBase64, StandardCharsets.UTF_8));

            // 设置请求头（需要在设置请求体之后，因为签名需要请求体内容）
            setHeaders(httpPost, url, requestBase64);

            // 记录请求日志
            if (properties.getLogging().isEnabled()) {
                log.info("Sending request to {}: {}", url, requestJson);
            }

            // 发送请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                
                // 记录响应日志
                if (properties.getLogging().isEnabled()) {
                    log.info("Received response from {}: {}", url, responseBody);
                }

                // 解析响应
                SwgxApiResponse<T> apiResponse;
                try {
                    apiResponse = objectMapper.readValue(responseBody,
                        objectMapper.getTypeFactory().constructParametricType(SwgxApiResponse.class, responseType));
                } catch (Exception e) {
                    log.error("Failed to parse response JSON: {}", responseBody, e);
                    throw new SwgxApiException("-1", "响应解析失败", "无法解析API响应: " + e.getMessage());
                }

                // 检查响应状态
                if (!apiResponse.isSuccess()) {
                    String errorMessage = apiResponse.getMessage();
                    String errorReason = apiResponse.getRenson();

                    // 如果message为空，尝试使用msg字段
                    if (errorMessage == null || errorMessage.trim().isEmpty()) {
                        errorMessage = apiResponse.getMsg();
                    }

                    log.warn("API调用失败: code={}, message={}, reason={}",
                        apiResponse.getCode(), errorMessage, errorReason);

                    throw new SwgxApiException(String.valueOf(apiResponse.getCode()), errorMessage, errorReason);
                }

                return apiResponse;
            }

        } catch (Exception e) {
            log.error("Error sending request to {}", url, e);
            if (e instanceof SwgxApiException) {
                throw (SwgxApiException) e;
            }
            throw new SwgxApiException("HTTP_ERROR", "HTTP请求失败", e.getMessage(), e);
        }
    }

    /**
     * 直接返回接口返回的原始数据
     * 注意：请求体会按照百旺API要求进行Base64编码
     *
     * @param url 请求URL
     * @param requestBody 请求体JSON字符串
     * @return API响应的原始字符串
     */
    public String callRawApi(String url, String requestBody) {
        try {
            // 按照百旺API要求，对请求体进行Base64编码
            String requestBase64 = Base64.getEncoder().encodeToString(requestBody.getBytes(StandardCharsets.UTF_8));

            // 创建并配置HTTP请求
            HttpPost httpPost = new HttpPost(url);
            // 修复：使用Base64编码后的数据作为请求体，与签名保持一致
            httpPost.setEntity(new StringEntity(requestBase64, StandardCharsets.UTF_8));
            setHeaders(httpPost, url, requestBase64);

            // 记录请求日志
            if (properties.getLogging().isEnabled()) {
                log.info("Sending raw request to {}: {}", url, requestBody);
                log.debug("Base64 encoded request body: {}", requestBase64);
            }

            // 发送请求并处理响应
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

                // 记录响应日志
                if (properties.getLogging().isEnabled()) {
                    log.info("Received raw response from {}: {}", url, responseBody);
                }

                return responseBody;
            }
        } catch (IOException e) {
            log.error("Error executing raw API call to {}", url, e);
            throw new SwgxApiException("RAW_API_ERROR", "原始API调用失败", e.getMessage(), e);
        }
    }



    /**
     * 设置请求头
     */
    private void setHeaders(HttpPost httpPost, String url, String requestBody) {
        // 设置基本头信息
        httpPost.setHeader(SwgxApiConstants.Headers.CONTENT_TYPE, SwgxApiConstants.Headers.APPLICATION_JSON);
        
        // 设置日期头
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.ENGLISH);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String dateStr = dateFormat.format(new Date());
        httpPost.setHeader(SwgxApiConstants.Headers.DATE, dateStr);
        
        // 设置主机头
        httpPost.setHeader(SwgxApiConstants.Headers.HOST, getHostFromUrl(url));
        
        // 设置随机数
        httpPost.setHeader(SwgxApiConstants.Headers.NONCE, UUID.randomUUID().toString());
        
        // 设置用户代理
        httpPost.setHeader(SwgxApiConstants.Headers.UA, "SwgxApiClient/1.0");
        
        // 设置授权头（这里需要根据实际的签名算法实现）
        String authorization = generateAuthorization(httpPost, requestBody);
        httpPost.setHeader(SwgxApiConstants.Headers.AUTHORIZATION, authorization);
    }

    /**
     * 从URL中提取主机名
     */
    private String getHostFromUrl(String url) {
        try {
            return new java.net.URL(url).getHost();
        } catch (Exception e) {
            return "api.baiwangjs.com";
        }
    }

    /**
     * 生成授权头
     * 使用百旺金穗云的HMAC-SHA256签名算法
     */
    private String generateAuthorization(HttpPost httpPost, String requestBody) {
        try {
            String method = httpPost.getMethod();
            String uri = httpPost.getURI().getPath();
            String queryString = httpPost.getURI().getQuery();

            // 获取请求头信息
            String contentType = httpPost.getFirstHeader(SwgxApiConstants.Headers.CONTENT_TYPE).getValue();
            String date = httpPost.getFirstHeader(SwgxApiConstants.Headers.DATE).getValue();
            String host = httpPost.getFirstHeader(SwgxApiConstants.Headers.HOST).getValue();
            String nonce = httpPost.getFirstHeader(SwgxApiConstants.Headers.NONCE).getValue();
            String ua = httpPost.getFirstHeader(SwgxApiConstants.Headers.UA).getValue();

            // 构建规范化的请求头字符串
            String canonicalHeaders = SignatureUtils.buildCanonicalHeaders(contentType, date, host, nonce, ua);

            // 使用传入的请求体
            String body = requestBody != null ? requestBody : "";

            // 生成签名
            String signature = SignatureUtils.generateSignature(
                method, uri, queryString, canonicalHeaders, body, properties.getAppSecret()
            );

            // 构建Authorization头
            return SignatureUtils.buildAuthorizationHeader(
                properties.getAppId(),
                signature,
                SignatureUtils.getSignedHeaders()
            );

        } catch (Exception e) {
            log.error("生成授权头失败", e);
            // 降级处理，返回简单的Bearer token
            return "Bearer " + properties.getAppId() + ":" + properties.getAppSecret();
        }
    }
}
