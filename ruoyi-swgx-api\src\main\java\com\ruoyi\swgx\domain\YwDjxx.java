package com.ruoyi.swgx.domain;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 业务单据主对象 swgx_yw_djxx
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class YwDjxx extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 单据ID */
    private String id;

    /** 单据编号 */
    @Excel(name = "单据编号")
    private String djbh;

    /** 单据类型 */
    @Excel(name = "单据类型")
    private String djlx;

    /** 单据状态 */
    @Excel(name = "单据状态")
    private String djzt;

    /** 匹配状态 */
    @Excel(name = "匹配状态")
    private String ppzt;

    /** 单据日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "单据日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date djrq;

    /** 购方名称 */
    @Excel(name = "购方名称")
    private String gmfMc;

    /** 购方税号 */
    @Excel(name = "购方税号")
    private String gmfNsrsbh;

    /** 购方地址 */
    private String gmfDz;

    /** 购方电话 */
    private String gmfDh;

    /** 购方银行账号 */
    private String gmfYhzh;

    /** 合计金额 */
    @Excel(name = "合计金额")
    private BigDecimal hjje;

    /** 合计税额 */
    private BigDecimal hjse;

    /** 价税合计 */
    private BigDecimal jshj;

    /** 剩余合计金额 */
    @Excel(name = "剩余合计金额")
    private BigDecimal syHjje;

    /** 剩余合计税额 */
    private BigDecimal syHjse;

    /** 剩余价税合计 */
    private BigDecimal syJshj;

    /** 红冲原因 */
    private String hcYy;

    /** 红冲次数 */
    private Long hcCs;

    /** 业务系统单据ID */
    @Excel(name = "业务系统单据ID")
    private String ywxtId;

    /** 业务系统名称 */
    @Excel(name = "业务系统名称")
    private String ywxtMc;

    /** 备注 */
    private String bz;

    /** 业务单据明细信息 */
    private List<YwDjmx> ywDjmxList;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setDjbh(String djbh) 
    {
        this.djbh = djbh;
    }

    public String getDjbh() 
    {
        return djbh;
    }

    public void setDjlx(String djlx) 
    {
        this.djlx = djlx;
    }

    public String getDjlx() 
    {
        return djlx;
    }

    public void setDjzt(String djzt) 
    {
        this.djzt = djzt;
    }

    public String getDjzt() 
    {
        return djzt;
    }

    public void setPpzt(String ppzt) 
    {
        this.ppzt = ppzt;
    }

    public String getPpzt() 
    {
        return ppzt;
    }

    public void setDjrq(Date djrq) 
    {
        this.djrq = djrq;
    }

    public Date getDjrq() 
    {
        return djrq;
    }

    public void setGmfMc(String gmfMc) 
    {
        this.gmfMc = gmfMc;
    }

    public String getGmfMc() 
    {
        return gmfMc;
    }

    public void setGmfNsrsbh(String gmfNsrsbh) 
    {
        this.gmfNsrsbh = gmfNsrsbh;
    }

    public String getGmfNsrsbh() 
    {
        return gmfNsrsbh;
    }

    public void setGmfDz(String gmfDz) 
    {
        this.gmfDz = gmfDz;
    }

    public String getGmfDz() 
    {
        return gmfDz;
    }

    public void setGmfDh(String gmfDh) 
    {
        this.gmfDh = gmfDh;
    }

    public String getGmfDh() 
    {
        return gmfDh;
    }

    public void setGmfYhzh(String gmfYhzh) 
    {
        this.gmfYhzh = gmfYhzh;
    }

    public String getGmfYhzh() 
    {
        return gmfYhzh;
    }

    public void setHjje(BigDecimal hjje) 
    {
        this.hjje = hjje;
    }

    public BigDecimal getHjje() 
    {
        return hjje;
    }

    public void setHjse(BigDecimal hjse) 
    {
        this.hjse = hjse;
    }

    public BigDecimal getHjse() 
    {
        return hjse;
    }

    public void setJshj(BigDecimal jshj) 
    {
        this.jshj = jshj;
    }

    public BigDecimal getJshj() 
    {
        return jshj;
    }

    public void setSyHjje(BigDecimal syHjje) 
    {
        this.syHjje = syHjje;
    }

    public BigDecimal getSyHjje() 
    {
        return syHjje;
    }

    public void setSyHjse(BigDecimal syHjse) 
    {
        this.syHjse = syHjse;
    }

    public BigDecimal getSyHjse() 
    {
        return syHjse;
    }

    public void setSyJshj(BigDecimal syJshj) 
    {
        this.syJshj = syJshj;
    }

    public BigDecimal getSyJshj() 
    {
        return syJshj;
    }

    public void setHcYy(String hcYy) 
    {
        this.hcYy = hcYy;
    }

    public String getHcYy() 
    {
        return hcYy;
    }

    public void setHcCs(Long hcCs) 
    {
        this.hcCs = hcCs;
    }

    public Long getHcCs() 
    {
        return hcCs;
    }

    public void setYwxtId(String ywxtId) 
    {
        this.ywxtId = ywxtId;
    }

    public String getYwxtId() 
    {
        return ywxtId;
    }

    public void setYwxtMc(String ywxtMc) 
    {
        this.ywxtMc = ywxtMc;
    }

    public String getYwxtMc() 
    {
        return ywxtMc;
    }

    public void setBz(String bz) 
    {
        this.bz = bz;
    }

    public String getBz() 
    {
        return bz;
    }

    public List<YwDjmx> getYwDjmxList()
    {
        return ywDjmxList;
    }

    public void setYwDjmxList(List<YwDjmx> ywDjmxList)
    {
        this.ywDjmxList = ywDjmxList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("djbh", getDjbh())
            .append("djlx", getDjlx())
            .append("djzt", getDjzt())
            .append("ppzt", getPpzt())
            .append("djrq", getDjrq())
            .append("gmfMc", getGmfMc())
            .append("gmfNsrsbh", getGmfNsrsbh())
            .append("gmfDz", getGmfDz())
            .append("gmfDh", getGmfDh())
            .append("gmfYhzh", getGmfYhzh())
            .append("hjje", getHjje())
            .append("hjse", getHjse())
            .append("jshj", getJshj())
            .append("syHjje", getSyHjje())
            .append("syHjse", getSyHjse())
            .append("syJshj", getSyJshj())
            .append("hcYy", getHcYy())
            .append("hcCs", getHcCs())
            .append("ywxtId", getYwxtId())
            .append("ywxtMc", getYwxtMc())
            .append("bz", getBz())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("ywDjmxList", getYwDjmxList())
            .toString();
    }
}
