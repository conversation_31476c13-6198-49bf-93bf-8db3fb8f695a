package com.ruoyi.swgx.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 业务单据明细对象 swgx_yw_djmx
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class YwDjmx extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 明细ID */
    private String id;

    /** 单据ID */
    @Excel(name = "单据ID")
    private String djId;

    /** 序号 */
    @Excel(name = "序号")
    private Long xh;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String spMc;

    /** 商品编码 */
    @Excel(name = "商品编码")
    private String spBm;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String ggxh;

    /** 单位 */
    @Excel(name = "单位")
    private String dw;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal sl;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal dj;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal je;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal slv;

    /** 税额 */
    @Excel(name = "税额")
    private BigDecimal se;

    /** 匹配状态 */
    @Excel(name = "匹配状态")
    private String ppZt;

    /** 剩余可匹配数量 */
    @Excel(name = "剩余可匹配数量")
    private BigDecimal sySl;

    /** 剩余可匹配金额 */
    @Excel(name = "剩余可匹配金额")
    private BigDecimal syJe;

    /** 剩余可匹配税额 */
    @Excel(name = "剩余可匹配税额")
    private BigDecimal sySe;

    /** 匹配次数 */
    @Excel(name = "匹配次数")
    private Long ppCs;

    /** 最后匹配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "最后匹配时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastPpTime;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setDjId(String djId) 
    {
        this.djId = djId;
    }

    public String getDjId() 
    {
        return djId;
    }
    public void setXh(Long xh) 
    {
        this.xh = xh;
    }

    public Long getXh() 
    {
        return xh;
    }
    public void setSpMc(String spMc) 
    {
        this.spMc = spMc;
    }

    public String getSpMc() 
    {
        return spMc;
    }
    public void setSpBm(String spBm) 
    {
        this.spBm = spBm;
    }

    public String getSpBm() 
    {
        return spBm;
    }
    public void setGgxh(String ggxh) 
    {
        this.ggxh = ggxh;
    }

    public String getGgxh() 
    {
        return ggxh;
    }
    public void setDw(String dw) 
    {
        this.dw = dw;
    }

    public String getDw() 
    {
        return dw;
    }
    public void setSl(BigDecimal sl) 
    {
        this.sl = sl;
    }

    public BigDecimal getSl() 
    {
        return sl;
    }
    public void setDj(BigDecimal dj) 
    {
        this.dj = dj;
    }

    public BigDecimal getDj() 
    {
        return dj;
    }
    public void setJe(BigDecimal je) 
    {
        this.je = je;
    }

    public BigDecimal getJe() 
    {
        return je;
    }
    public void setSlv(BigDecimal slv) 
    {
        this.slv = slv;
    }

    public BigDecimal getSlv() 
    {
        return slv;
    }
    public void setSe(BigDecimal se) 
    {
        this.se = se;
    }

    public BigDecimal getSe() 
    {
        return se;
    }
    public void setPpZt(String ppZt) 
    {
        this.ppZt = ppZt;
    }

    public String getPpZt() 
    {
        return ppZt;
    }
    public void setSySl(BigDecimal sySl) 
    {
        this.sySl = sySl;
    }

    public BigDecimal getSySl() 
    {
        return sySl;
    }
    public void setSyJe(BigDecimal syJe) 
    {
        this.syJe = syJe;
    }

    public BigDecimal getSyJe() 
    {
        return syJe;
    }
    public void setSySe(BigDecimal sySe) 
    {
        this.sySe = sySe;
    }

    public BigDecimal getSySe() 
    {
        return sySe;
    }
    public void setPpCs(Long ppCs) 
    {
        this.ppCs = ppCs;
    }

    public Long getPpCs() 
    {
        return ppCs;
    }
    public void setLastPpTime(Date lastPpTime) 
    {
        this.lastPpTime = lastPpTime;
    }

    public Date getLastPpTime() 
    {
        return lastPpTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("djId", getDjId())
            .append("xh", getXh())
            .append("spMc", getSpMc())
            .append("spBm", getSpBm())
            .append("ggxh", getGgxh())
            .append("dw", getDw())
            .append("sl", getSl())
            .append("dj", getDj())
            .append("je", getJe())
            .append("slv", getSlv())
            .append("se", getSe())
            .append("ppZt", getPpZt())
            .append("sySl", getSySl())
            .append("syJe", getSyJe())
            .append("sySe", getSySe())
            .append("ppCs", getPpCs())
            .append("lastPpTime", getLastPpTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
