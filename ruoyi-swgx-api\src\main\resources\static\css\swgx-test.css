/* 百旺金穗云API测试页面样式 - RuoYi风格 */

/* 功能图标样式 */
.feature-icon-invoice {
    font-size: 48px;
    color: #f8ac59;
}

.feature-icon-api {
    font-size: 48px;
    color: #1ab394;
}

.feature-icon-doc {
    font-size: 48px;
    color: #23c6c8;
}

/* 工具栏样式 */
.ibox-tools {
    display: block;
    float: right;
    margin-top: 0;
    position: relative;
    padding: 0;
    text-align: left;
}

.ibox-tools a {
    cursor: pointer;
    margin-left: 5px;
    color: #c4c4c4;
}

.ibox-tools a:hover {
    color: #1ab394;
}

/* 统计小部件样式 */
.widget {
    border-radius: 5px;
    padding: 15px 20px;
    margin-bottom: 10px;
    margin-top: 10px;
}

.widget.style1 h2 {
    font-size: 30px;
}

.widget h2, .widget h3 {
    margin-top: 5px;
    margin-bottom: 0;
}

.navy-bg {
    background-color: #1ab394;
    color: #fff;
}

.yellow-bg {
    background-color: #f8ac59;
    color: #fff;
}

.red-bg {
    background-color: #ed5565;
    color: #fff;
}

/* 折叠面板事件 */
.collapse-link {
    cursor: pointer;
    margin-left: 5px;
    color: #c4c4c4;
}

.collapse-link:hover {
    color: #1ab394;
}

/* API测试工具专用样式 */
.json-editor {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
}

.response-container {
    max-height: 500px;
    overflow-y: auto;
}

.history-item {
    cursor: pointer;
    padding: 8px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.history-item:hover {
    background-color: #f5f5f5;
}

.method-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 8px;
}

.method-get {
    background-color: #1ab394;
    color: white;
}

.method-post {
    background-color: #23c6c8;
    color: white;
}

.method-put {
    background-color: #f8ac59;
    color: white;
}

.method-delete {
    background-color: #ed5565;
    color: white;
}

/* API测试工具样式 */
.json-tools {
    float: right;
    margin-top: -25px;
    margin-bottom: 5px;
}

.api-path-input {
    font-family: 'Courier New', monospace !important;
}

.history-panel-body {
    padding: 0 !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

.history-empty {
    padding: 20px !important;
}

/* 代码块 */
.code-block {
    background: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
    border-radius: 8px;
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    margin-top: 15px;
    overflow-x: auto;
}

/* 表单样式优化 */
.form-control {
    background-color: #fff !important;
    color: #212529 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

.form-control:focus {
    background-color: #fff !important;
    color: #212529 !important;
}

/* 选项卡样式 */
.nav-tabs .nav-link.active {
    background: #1ab394;
    border-color: #1ab394;
    color: white;
}

.nav-tabs .nav-link:hover {
    border-color: #1ab394;
    color: #1ab394;
}
