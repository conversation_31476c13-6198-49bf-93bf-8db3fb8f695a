<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改电票发票信息')" />
    <th:block th:include="include :: datetimepicker-css" />
    <style type="text/css">
        table label.error{position: inherit;}select + label.error{z-index:1;right:40px;}
    </style>
</head>
<body class="gray-bg">
    <div class="main-content">
        <form id="form-dpFpxx-edit" class="form-horizontal">
            <input name="id" type="hidden" th:field="*{dpFpxx.id}" />
            <input name="qyId" type="hidden" th:field="*{dpFpxx.qyId}" />
            <h4 class="form-header h4">发票基本信息</h4>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">发票代码：</label>
                        <div class="col-sm-8">
                            <input name="fpDm" th:field="*{dpFpxx.fpDm}" placeholder="请输入发票代码" class="form-control" type="text" maxlength="20" required>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">发票号码：</label>
                        <div class="col-sm-8">
                            <input name="fpHm" th:field="*{dpFpxx.fpHm}" placeholder="请输入发票号码" class="form-control" type="text" maxlength="20" required>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">发票状态：</label>
                        <div class="col-sm-8">
                            <select name="fpzt" class="form-control" th:with="type=${@dict.getType('dp_fpxx_status')}" required>
                                <option value="">请选择发票状态</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                        th:selected="${dict.dictValue == dpFpxx.fpzt.toString()}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">开票日期：</label>
                        <div class="col-sm-8">
                            <div class="input-group date">
                                <input name="kprq" th:field="*{dpFpxx.kprq}" class="form-control" placeholder="yyyyMMddHHmmss" type="text">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">是否增值税发票：</label>
                        <div class="col-sm-8">
                            <select name="sfwzzfp" class="form-control" th:field="*{dpFpxx.sfwzzfp}">
                                <option value="">请选择</option>
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">发票类型代码：</label>
                        <div class="col-sm-8">
                            <input name="fplxdm" th:field="*{dpFpxx.fplxdm}" placeholder="请输入发票类型代码" class="form-control" type="text" maxlength="10">
                        </div>
                    </div>
                </div>
            </div>

            <h4 class="form-header h4">销售方信息</h4>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">销售方名称：</label>
                        <div class="col-sm-8">
                            <input name="xsfMc" th:field="*{dpFpxx.xsfMc}" placeholder="请输入销售方名称" class="form-control" type="text" maxlength="200">
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">销售方纳税人识别号：</label>
                        <div class="col-sm-8">
                            <input name="xsfNsrsbh" th:field="*{dpFpxx.xsfNsrsbh}" placeholder="请输入销售方纳税人识别号" class="form-control" type="text" maxlength="30">
                        </div>
                    </div>
                </div>
            </div>

            <h4 class="form-header h4">购买方信息</h4>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">购买方名称：</label>
                        <div class="col-sm-8">
                            <input name="gmfMc" th:field="*{dpFpxx.gmfMc}" placeholder="请输入购买方名称" class="form-control" type="text" maxlength="200">
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">购买方纳税人识别号：</label>
                        <div class="col-sm-8">
                            <input name="gmfNsrsbh" th:field="*{dpFpxx.gmfNsrsbh}" placeholder="请输入购买方纳税人识别号" class="form-control" type="text" maxlength="30">
                        </div>
                    </div>
                </div>
            </div>

            <h4 class="form-header h4">金额信息</h4>
            <div class="row">
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">价税合计：</label>
                        <div class="col-sm-6">
                            <input name="jshj" th:field="*{dpFpxx.jshj}" placeholder="0.00" class="form-control" type="number" step="0.01">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">合计金额：</label>
                        <div class="col-sm-6">
                            <input name="hjje" th:field="*{dpFpxx.hjje}" placeholder="0.00" class="form-control" type="number" step="0.01">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">合计税额：</label>
                        <div class="col-sm-6">
                            <input name="hjse" th:field="*{dpFpxx.hjse}" placeholder="0.00" class="form-control" type="number" step="0.01">
                        </div>
                    </div>
                </div>
            </div>

            <h4 class="form-header h4">发票明细信息</h4>
            <div class="row">
                <div class="col-xs-12">
                    <button type="button" class="btn btn-white btn-sm" onclick="addRow()"><i class="fa fa-plus"> 增加</i></button>
                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delRow()"><i class="fa fa-minus"> 删除</i></button>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">开票人：</label>
                        <div class="col-sm-8">
                            <input name="kpr" th:field="*{dpFpxx.kpr}" placeholder="请输入开票人" class="form-control" type="text" maxlength="50">
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">主要商品名称：</label>
                        <div class="col-sm-8">
                            <input name="zyspmc" th:field="*{dpFpxx.zyspmc}" placeholder="请输入主要商品名称" class="form-control" type="text" maxlength="200">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注：</label>
                        <div class="col-sm-10">
                            <textarea name="bz" th:field="*{dpFpxx.bz}" class="form-control" maxlength="500" placeholder="请输入备注"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:src="@{/js/jquery.tmpl.js}"></script>
    <script th:inline="javascript">
        var prefix = ctx + "swgx/dpFpxx";
        var dpFpxxMxData = [[${dpFpxx.dpFpxxMxList}]];
        
        $("#form-dpFpxx-edit").validate({
            focusCleanup: true
        });
        
        function submitHandler() {
            if ($.validate.form()) {
                // 添加确认提示
                $.modal.confirm("确认要保存发票信息吗？", function() {
                    // 使用RuoYi标准的保存方法
                    $.operate.save(prefix + "/edit", $('#form-dpFpxx-edit').serialize());
                });
            }
        }

        $(function() {
            // 初始化明细表格
            var data = dpFpxxMxData || [];

            // 确保明细数据包含必要字段
            if (data && data.length > 0) {
                for (var i = 0; i < data.length; i++) {
                    data[i].index = i; // 设置索引
                    data[i].tempId = 'temp_' + i; // 临时ID用于跟踪
                    if (!data[i].id) {
                        data[i].id = ''; // 新增明细时ID为空，后端会生成
                    }
                    if (!data[i].serialNumber) {
                        data[i].serialNumber = i + 1; // 序列号从1开始
                    }
                    if (!data[i].fpid) {
                        data[i].fpid = $("input[name='id']").val() || ''; // 获取发票ID
                    }
                    if (!data[i].qyId) {
                        data[i].qyId = $("input[name='qyId']").val() || ''; // 获取企业ID
                    }
                }
            }



            var options = {
                data: data,
                pagination: false,
                search: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                clickToSelect: true,
                selectItemName: "btSelectItem",
                columns: [{
                    checkbox: true
                }, {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        var columnId = $.common.sprintf("<input type='hidden' name='dpFpxxMxList[%s].id' value='%s'>", index, row.id || '');
                        var columnFpid = $.common.sprintf("<input type='hidden' name='dpFpxxMxList[%s].fpid' value='%s'>", index, row.fpid || '');
                        var columnSerialNumber = $.common.sprintf("<input type='hidden' name='dpFpxxMxList[%s].serialNumber' value='%s'>", index, row.serialNumber || (index + 1));
                        var columnQyId = $.common.sprintf("<input type='hidden' name='dpFpxxMxList[%s].qyId' value='%s'>", index, row.qyId || '');
                        return columnIndex + $.table.serialNumber(index) + columnId + columnFpid + columnSerialNumber + columnQyId;
                    }
                },
                {
                    field: 'spmc',
                    align: 'center',
                    title: '商品名称',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='dpFpxxMxList[%s].spmc' value='%s' required>", index, value || '');
                        return html;
                    }
                },
                {
                    field: 'ggxh',
                    align: 'center',
                    title: '规格型号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='dpFpxxMxList[%s].ggxh' value='%s'>", index, value || '');
                        return html;
                    }
                },
                {
                    field: 'dw',
                    align: 'center',
                    title: '单位',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='dpFpxxMxList[%s].dw' value='%s'>", index, value || '');
                        return html;
                    }
                },
                {
                    field: 'spsl',
                    align: 'center',
                    title: '数量',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='number' step='0.01' name='dpFpxxMxList[%s].spsl' value='%s' required>", index, value || '');
                        return html;
                    }
                },
                {
                    field: 'dj',
                    align: 'center',
                    title: '单价',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='number' step='0.01' name='dpFpxxMxList[%s].dj' value='%s' required>", index, value || '');
                        return html;
                    }
                },
                {
                    field: 'je',
                    align: 'center',
                    title: '金额',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='number' step='0.01' name='dpFpxxMxList[%s].je' value='%s' required>", index, value || '');
                        return html;
                    }
                },
                {
                    field: 'sl',
                    align: 'center',
                    title: '税率',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='number' step='0.01' name='dpFpxxMxList[%s].sl' value='%s' required>", index, value || '');
                        return html;
                    }
                },
                {
                    field: 'se',
                    align: 'center',
                    title: '税额',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='number' step='0.01' name='dpFpxxMxList[%s].se' value='%s' required>", index, value || '');
                        return html;
                    }
                }]
            };
            $.table.init(options);
        });

        function addRow() {
            var data = $("#bootstrap-table").bootstrapTable('getData');
            var count = data.length;

            // 创建新行数据
            var newRow = {
                index: count,
                tempId: 'temp_' + count + '_' + new Date().getTime(), // 唯一临时ID
                id: '', // 新行ID为空，后端会生成
                fpid: $("input[name='id']").val() || '',
                qyId: $("input[name='qyId']").val() || '',
                serialNumber: count + 1,
                spmc: '',
                ggxh: '',
                dw: '',
                spsl: 1,
                dj: 0,
                je: 0,
                sl: 0,
                se: 0
            };

            // 添加到数据数组
            data.push(newRow);

            // 重新加载表格
            $("#bootstrap-table").bootstrapTable('load', data);
        }

        var sub = {
            delRow: function() {
                var rows = $("#bootstrap-table").bootstrapTable('getSelections');
                if (rows.length == 0) {
                    $.modal.msgWarning("请选择要删除的数据");
                    return;
                }

                $.modal.confirm("确认要删除选中的 " + rows.length + " 条明细记录吗？", function() {
                    // 获取当前所有数据
                    var allData = $("#bootstrap-table").bootstrapTable('getData');
                    var selectedIndexes = [];

                    // 找出要删除的行的索引
                    $.each(rows, function(i, selectedRow) {
                        $.each(allData, function(j, dataRow) {
                            if (dataRow === selectedRow) {
                                selectedIndexes.push(j);
                                return false; // 跳出内层循环
                            }
                        });
                    });

                    // 按索引倒序删除，避免索引变化问题
                    selectedIndexes.sort(function(a, b) { return b - a; });
                    $.each(selectedIndexes, function(i, index) {
                        allData.splice(index, 1);
                    });

                    // 重新设置索引和序列号
                    for (var i = 0; i < allData.length; i++) {
                        allData[i].index = i;
                        allData[i].serialNumber = i + 1;
                        // 保持tempId不变，用于跟踪行
                        if (!allData[i].tempId) {
                            allData[i].tempId = 'temp_' + i + '_' + new Date().getTime();
                        }
                    }

                    // 重新加载数据
                    $("#bootstrap-table").bootstrapTable('load', allData);
                });
            }
        };
    </script>
</body>
</html>
