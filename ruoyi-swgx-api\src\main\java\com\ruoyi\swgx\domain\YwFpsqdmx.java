package com.ruoyi.swgx.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 发票申请单明细对象 swgx_yw_fpsqdmx
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class YwFpsqdmx extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 明细GUID */
    private String swguid;

    /** 主表GUID */
    private String swmainguid;

    /** 外部明细编号 */
    @Excel(name = "外部明细编号")
    private String swoutdetailno;

    /** 重试标志 */
    @Excel(name = "重试标志")
    private Long swcsflag;

    /** 单据编号 */
    @Excel(name = "单据编号")
    private String djbh;

    /** 序号 */
    @Excel(name = "序号")
    private Long xh;

    /** 绿证明细序号 */
    @Excel(name = "绿证明细序号")
    private String lzmxxh;

    /** 发票行性质 */
    @Excel(name = "发票行性质")
    private Long fpxzh;

    /** 含税标志 */
    @Excel(name = "含税标志")
    private Long hsbz;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String spmc;

    /** 商品编码 */
    @Excel(name = "商品编码")
    private String spbm;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String ggxh;

    /** 单位 */
    @Excel(name = "单位")
    private String dw;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal dj;

    /** 数量 */
    @Excel(name = "数量")
    private Long sl;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal je;

    /** 税额 */
    @Excel(name = "税额")
    private BigDecimal se;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal slv;

    /** 零税率标识 */
    @Excel(name = "零税率标识")
    private String lslbs;

    /** 优惠政策标识 */
    @Excel(name = "优惠政策标识")
    private Long yhzcbs;

    /** 自行编码 */
    @Excel(name = "自行编码")
    private String zxbm;

    /** 增值税特殊管理 */
    @Excel(name = "增值税特殊管理")
    private String zzstsgl;

    /** 不动产地址 */
    @Excel(name = "不动产地址")
    private String bdcdz;

    /** 完整地址 */
    @Excel(name = "完整地址")
    private String fulladdress;

    /** 质量期限值 */
    @Excel(name = "质量期限值")
    private String zlqqz;

    /** 扣除税标志 */
    @Excel(name = "扣除税标志")
    private Long kdsbz;

    /** 产权证书号 */
    @Excel(name = "产权证书号")
    private String cqzsh;

    /** 面积单位 */
    @Excel(name = "面积单位")
    private String mjdw;

    /** 委托合同备案编号 */
    @Excel(name = "委托合同备案编号")
    private String wqhtbabh;

    /** 土地增值税项目编号 */
    @Excel(name = "土地增值税项目编号")
    private String tdzzsxmbh;

    /** 核定计税价格 */
    @Excel(name = "核定计税价格")
    private BigDecimal hdjsjg;

    /** 实际成交含税金额 */
    @Excel(name = "实际成交含税金额")
    private BigDecimal sjcjhsje;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String cph;

    /** 创建者 */
    private String create_by;

    /** 创建时间 */
    private Date create_time;

    /** 更新者 */
    private String update_by;

    /** 更新时间 */
    private Date update_time;

    public void setSwguid(String swguid) 
    {
        this.swguid = swguid;
    }

    public String getSwguid() 
    {
        return swguid;
    }
    public void setSwmainguid(String swmainguid) 
    {
        this.swmainguid = swmainguid;
    }

    public String getSwmainguid() 
    {
        return swmainguid;
    }
    public void setSwoutdetailno(String swoutdetailno) 
    {
        this.swoutdetailno = swoutdetailno;
    }

    public String getSwoutdetailno() 
    {
        return swoutdetailno;
    }
    public void setSwcsflag(Long swcsflag) 
    {
        this.swcsflag = swcsflag;
    }

    public Long getSwcsflag() 
    {
        return swcsflag;
    }
    public void setDjbh(String djbh) 
    {
        this.djbh = djbh;
    }

    public String getDjbh() 
    {
        return djbh;
    }
    public void setXh(Long xh) 
    {
        this.xh = xh;
    }

    public Long getXh() 
    {
        return xh;
    }
    public void setLzmxxh(String lzmxxh) 
    {
        this.lzmxxh = lzmxxh;
    }

    public String getLzmxxh() 
    {
        return lzmxxh;
    }
    public void setFpxzh(Long fpxzh) 
    {
        this.fpxzh = fpxzh;
    }

    public Long getFpxzh() 
    {
        return fpxzh;
    }
    public void setHsbz(Long hsbz) 
    {
        this.hsbz = hsbz;
    }

    public Long getHsbz() 
    {
        return hsbz;
    }
    public void setSpmc(String spmc) 
    {
        this.spmc = spmc;
    }

    public String getSpmc() 
    {
        return spmc;
    }
    public void setSpbm(String spbm) 
    {
        this.spbm = spbm;
    }

    public String getSpbm() 
    {
        return spbm;
    }
    public void setGgxh(String ggxh) 
    {
        this.ggxh = ggxh;
    }

    public String getGgxh() 
    {
        return ggxh;
    }
    public void setDw(String dw) 
    {
        this.dw = dw;
    }

    public String getDw() 
    {
        return dw;
    }
    public void setDj(BigDecimal dj) 
    {
        this.dj = dj;
    }

    public BigDecimal getDj() 
    {
        return dj;
    }
    public void setSl(Long sl) 
    {
        this.sl = sl;
    }

    public Long getSl() 
    {
        return sl;
    }
    public void setJe(BigDecimal je) 
    {
        this.je = je;
    }

    public BigDecimal getJe() 
    {
        return je;
    }
    public void setSe(BigDecimal se) 
    {
        this.se = se;
    }

    public BigDecimal getSe() 
    {
        return se;
    }
    public void setSlv(BigDecimal slv) 
    {
        this.slv = slv;
    }

    public BigDecimal getSlv() 
    {
        return slv;
    }
    public void setLslbs(String lslbs) 
    {
        this.lslbs = lslbs;
    }

    public String getLslbs() 
    {
        return lslbs;
    }
    public void setYhzcbs(Long yhzcbs) 
    {
        this.yhzcbs = yhzcbs;
    }

    public Long getYhzcbs() 
    {
        return yhzcbs;
    }
    public void setZxbm(String zxbm) 
    {
        this.zxbm = zxbm;
    }

    public String getZxbm() 
    {
        return zxbm;
    }
    public void setZzstsgl(String zzstsgl) 
    {
        this.zzstsgl = zzstsgl;
    }

    public String getZzstsgl() 
    {
        return zzstsgl;
    }
    public void setBdcdz(String bdcdz) 
    {
        this.bdcdz = bdcdz;
    }

    public String getBdcdz() 
    {
        return bdcdz;
    }
    public void setFulladdress(String fulladdress) 
    {
        this.fulladdress = fulladdress;
    }

    public String getFulladdress() 
    {
        return fulladdress;
    }
    public void setZlqqz(String zlqqz) 
    {
        this.zlqqz = zlqqz;
    }

    public String getZlqqz() 
    {
        return zlqqz;
    }
    public void setKdsbz(Long kdsbz) 
    {
        this.kdsbz = kdsbz;
    }

    public Long getKdsbz() 
    {
        return kdsbz;
    }
    public void setCqzsh(String cqzsh) 
    {
        this.cqzsh = cqzsh;
    }

    public String getCqzsh() 
    {
        return cqzsh;
    }
    public void setMjdw(String mjdw) 
    {
        this.mjdw = mjdw;
    }

    public String getMjdw() 
    {
        return mjdw;
    }
    public void setWqhtbabh(String wqhtbabh) 
    {
        this.wqhtbabh = wqhtbabh;
    }

    public String getWqhtbabh() 
    {
        return wqhtbabh;
    }
    public void setTdzzsxmbh(String tdzzsxmbh) 
    {
        this.tdzzsxmbh = tdzzsxmbh;
    }

    public String getTdzzsxmbh() 
    {
        return tdzzsxmbh;
    }
    public void setHdjsjg(BigDecimal hdjsjg) 
    {
        this.hdjsjg = hdjsjg;
    }

    public BigDecimal getHdjsjg() 
    {
        return hdjsjg;
    }
    public void setSjcjhsje(BigDecimal sjcjhsje) 
    {
        this.sjcjhsje = sjcjhsje;
    }

    public BigDecimal getSjcjhsje() 
    {
        return sjcjhsje;
    }
    public void setCph(String cph) 
    {
        this.cph = cph;
    }

    public String getCph() 
    {
        return cph;
    }
    public void setCreate_by(String create_by) 
    {
        this.create_by = create_by;
    }

    public String getCreate_by() 
    {
        return create_by;
    }
    public void setCreate_time(Date create_time) 
    {
        this.create_time = create_time;
    }

    public Date getCreate_time() 
    {
        return create_time;
    }
    public void setUpdate_by(String update_by) 
    {
        this.update_by = update_by;
    }

    public String getUpdate_by() 
    {
        return update_by;
    }
    public void setUpdate_time(Date update_time) 
    {
        this.update_time = update_time;
    }

    public Date getUpdate_time() 
    {
        return update_time;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("swguid", getSwguid())
            .append("swmainguid", getSwmainguid())
            .append("swoutdetailno", getSwoutdetailno())
            .append("swcsflag", getSwcsflag())
            .append("djbh", getDjbh())
            .append("xh", getXh())
            .append("lzmxxh", getLzmxxh())
            .append("fpxzh", getFpxzh())
            .append("hsbz", getHsbz())
            .append("spmc", getSpmc())
            .append("spbm", getSpbm())
            .append("ggxh", getGgxh())
            .append("dw", getDw())
            .append("dj", getDj())
            .append("sl", getSl())
            .append("je", getJe())
            .append("se", getSe())
            .append("slv", getSlv())
            .append("lslbs", getLslbs())
            .append("yhzcbs", getYhzcbs())
            .append("zxbm", getZxbm())
            .append("zzstsgl", getZzstsgl())
            .append("bdcdz", getBdcdz())
            .append("fulladdress", getFulladdress())
            .append("zlqqz", getZlqqz())
            .append("kdsbz", getKdsbz())
            .append("cqzsh", getCqzsh())
            .append("mjdw", getMjdw())
            .append("wqhtbabh", getWqhtbabh())
            .append("tdzzsxmbh", getTdzzsxmbh())
            .append("hdjsjg", getHdjsjg())
            .append("sjcjhsje", getSjcjhsje())
            .append("cph", getCph())
            .append("create_by", getCreate_by())
            .append("create_time", getCreate_time())
            .append("update_by", getUpdate_by())
            .append("update_time", getUpdate_time())
            .toString();
    }
}
