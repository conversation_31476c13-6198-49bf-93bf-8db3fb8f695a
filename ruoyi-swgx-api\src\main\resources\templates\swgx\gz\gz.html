<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('匹配规则配置列表')" />
    <link th:href="@{/ajax/libs/switchery/switchery.css}" rel="stylesheet"/>
    <script th:src="@{/ajax/libs/switchery/switchery.min.js}"></script>
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>规则名称 </label>
                                <input type="text" name="gzMc"/>
                            </li>
                            <li>
                                <label>规则类型 </label>
                                <select name="gzLx" th:with="type=${@dict.getType('swgx_match_rule_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>规则内容 </label>
                                <input type="text" name="gzNr"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="swgx:gz:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="swgx:gz:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="swgx:gz:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info multiple disabled" onclick="batchEnable()" shiro:hasPermission="swgx:gz:edit">
                    <i class="fa fa-check"></i> 批量启用
                </a>
                <a class="btn btn-warning multiple disabled" onclick="batchDisable()" shiro:hasPermission="swgx:gz:edit">
                    <i class="fa fa-ban"></i> 批量禁用
                </a>
                <a class="btn btn-default" onclick="$.table.exportExcel()" shiro:hasPermission="swgx:gz:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('swgx:gz:edit')}]];
        var removeFlag = [[${@permission.hasPermi('swgx:gz:remove')}]];
        var gzLxDatas = [[${@dict.getType('swgx_match_rule_type')}]];
        var prefix = ctx + "swgx/gz";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "匹配规则配置",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '规则ID',
                    visible: false
                },
                {
                    field: 'gzMc',
                    title: '规则名称',
                    width: 150
                },
                {
                    field: 'gzMs',
                    title: '规则描述',
                    width: 200
                },
                {
                    field: 'gzLx',
                    title: '规则类型',
                    width: 100,
                    align: 'center',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(gzLxDatas, value);
                    }
                },
                {
                    field: 'gzZt',
                    title: '规则状态',
                    width: 100,
                    align: 'center',
                    formatter: function(value, row, index) {
                        var checked = value == '1' ? 'checked' : '';
                        var switchHtml = '<input type="checkbox" class="js-switch" data-id="' + row.id + '" ' + checked + ' onchange="changeStatus(this)">';
                        return switchHtml;
                    }
                },
                {
                    field: 'gzYxj',
                    title: '优先级',
                    width: 80,
                    align: 'center'
                },
                {
                    field: 'gzNr',
                    title: '规则内容',
                    width: 200,
                    formatter: function(value, row, index) {
                        if (!value) return '-';
                        try {
                            var ruleObj = JSON.parse(value);
                            var summary = '';
                            if (ruleObj.fields && ruleObj.fields.length > 0) {
                                var fieldNames = ruleObj.fields.map(f => f.name).join(', ');
                                summary = '匹配字段: ' + fieldNames;
                                if (ruleObj.threshold) {
                                    summary += '<br/>阈值: ' + (ruleObj.threshold * 100) + '%';
                                }
                            } else {
                                summary = '规则配置';
                            }
                            return '<div style="text-align: left;">' + summary +
                                   '<br/><a href="javascript:void(0)" onclick="showRuleDetail(\'' + row.id + '\')" class="btn btn-xs btn-info">查看详情</a></div>';
                        } catch (e) {
                            return '<span class="text-muted">格式错误</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 初始化开关组件
        $(document).ready(function() {
            // 延迟初始化开关，等待表格渲染完成
            setTimeout(function() {
                initSwitches();
            }, 500);
        });

        // 表格刷新后重新初始化开关
        $('#bootstrap-table').on('refresh.bs.table', function () {
            setTimeout(function() {
                initSwitches();
            }, 100);
        });

        // 初始化开关组件
        function initSwitches() {
            $('.js-switch').each(function() {
                if (!$(this).data('switchery')) {
                    var switchery = new Switchery(this, {
                        color: '#1AB394',
                        secondaryColor: '#dfdfdf',
                        jackColor: '#fff',
                        jackSecondaryColor: null,
                        className: 'switchery',
                        disabled: false,
                        disabledOpacity: 0.5,
                        speed: '0.4s',
                        size: 'small'
                    });
                    $(this).data('switchery', switchery);
                }
            });
        }

        // 状态切换
        function changeStatus(element) {
            var id = $(element).data('id');
            var status = element.checked ? '1' : '0';
            var statusText = status == '1' ? '启用' : '禁用';

            $.modal.confirm("确认要" + statusText + "该规则吗？", function() {
                $.ajax({
                    url: prefix + "/changeStatus",
                    type: "post",
                    data: {
                        id: id,
                        status: status
                    },
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess(statusText + "成功");
                        } else {
                            $.modal.msgError(result.msg);
                            // 恢复开关状态
                            var switchery = $(element).data('switchery');
                            if (switchery) {
                                switchery.setPosition(!element.checked);
                            }
                        }
                    },
                    error: function() {
                        $.modal.msgError(statusText + "失败");
                        // 恢复开关状态
                        var switchery = $(element).data('switchery');
                        if (switchery) {
                            switchery.setPosition(!element.checked);
                        }
                    }
                });
            }, function() {
                // 取消时恢复开关状态
                var switchery = $(element).data('switchery');
                if (switchery) {
                    switchery.setPosition(!element.checked);
                }
            });
        }

        // 批量启用
        function batchEnable() {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            $.modal.confirm("确认要启用选中的 " + rows.length + " 条规则吗？", function() {
                $.ajax({
                    url: prefix + "/batchEnable",
                    type: "post",
                    data: {
                        ids: rows.join(",")
                    },
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess("批量启用成功");
                            $.table.refresh();
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.msgError("批量启用失败");
                    }
                });
            });
        }

        // 批量禁用
        function batchDisable() {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            $.modal.confirm("确认要禁用选中的 " + rows.length + " 条规则吗？", function() {
                $.ajax({
                    url: prefix + "/batchDisable",
                    type: "post",
                    data: {
                        ids: rows.join(",")
                    },
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.msgSuccess("批量禁用成功");
                            $.table.refresh();
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.msgError("批量禁用失败");
                    }
                });
            });
        }

        // 显示规则详情
        function showRuleDetail(id) {
            $.ajax({
                url: prefix + "/detail/" + id,
                type: "get",
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        displayRuleDetailDialog(result.data);
                    } else {
                        $.modal.msgError(result.msg);
                    }
                },
                error: function() {
                    $.modal.msgError("获取规则详情失败");
                }
            });
        }

        // 显示规则详情对话框
        function displayRuleDetailDialog(rule) {
            var content = '<div class="row">' +
                '<div class="col-sm-12">' +
                '<h4>基本信息</h4>' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">规则名称：</label>' +
                '<div class="col-sm-9">' + (rule.gzMc || '-') + '</div>' +
                '</div>' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">规则类型：</label>' +
                '<div class="col-sm-9">' + (rule.gzLx == 'EXACT' ? '精确匹配' : '模糊匹配') + '</div>' +
                '</div>' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">规则状态：</label>' +
                '<div class="col-sm-9">' + (rule.gzZt == '1' ? '<span class="label label-success">启用</span>' : '<span class="label label-danger">禁用</span>') + '</div>' +
                '</div>' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">优先级：</label>' +
                '<div class="col-sm-9">' + (rule.gzYxj || '-') + '</div>' +
                '</div>' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">规则描述：</label>' +
                '<div class="col-sm-9">' + (rule.gzMs || '-') + '</div>' +
                '</div>' +
                '<hr>' +
                '<h4>规则配置详情</h4>' +
                '<div class="form-group">' +
                '<div class="col-sm-12">' + formatRuleContent(rule.gzNr) + '</div>' +
                '</div>' +
                '</div></div>';

            layer.open({
                title: "规则详情",
                type: 1,
                area: ['800px', '600px'],
                content: content,
                btn: ['关闭'],
                btn1: function(index) {
                    layer.close(index);
                }
            });
        }

        // 格式化规则内容
        function formatRuleContent(jsonStr) {
            if (!jsonStr) return '<div class="alert alert-info">无规则配置</div>';

            try {
                var ruleObj = JSON.parse(jsonStr);
                var html = '<div class="panel panel-default">';

                if (ruleObj.fields && ruleObj.fields.length > 0) {
                    html += '<div class="panel-heading"><strong>匹配字段配置</strong></div>';
                    html += '<div class="panel-body">';
                    html += '<table class="table table-bordered table-striped">';
                    html += '<thead><tr><th>字段名称</th><th>权重</th><th>是否必需</th><th>匹配类型</th><th>其他参数</th></tr></thead>';
                    html += '<tbody>';

                    ruleObj.fields.forEach(function(field) {
                        html += '<tr>';
                        html += '<td>' + getFieldDisplayName(field.name) + '</td>';
                        html += '<td>' + (field.weight ? (field.weight * 100).toFixed(1) + '%' : '-') + '</td>';
                        html += '<td>' + (field.required ? '<span class="label label-success">是</span>' : '<span class="label label-default">否</span>') + '</td>';
                        html += '<td>' + getMatchTypeDisplayName(field.matchType) + '</td>';
                        html += '<td>' + getFieldParams(field) + '</td>';
                        html += '</tr>';
                    });

                    html += '</tbody></table>';
                    html += '</div>';
                }

                if (ruleObj.threshold) {
                    html += '<div class="panel-heading"><strong>匹配阈值</strong></div>';
                    html += '<div class="panel-body">';
                    html += '<p>最低匹配得分：<strong>' + (ruleObj.threshold * 100) + '%</strong></p>';
                    html += '</div>';
                }

                if (ruleObj.description) {
                    html += '<div class="panel-heading"><strong>规则说明</strong></div>';
                    html += '<div class="panel-body">';
                    html += '<p>' + ruleObj.description + '</p>';
                    html += '</div>';
                }

                html += '</div>';
                return html;

            } catch (e) {
                return '<div class="alert alert-danger">规则配置格式错误：' + e.message + '</div>';
            }
        }

        // 获取字段显示名称
        function getFieldDisplayName(fieldName) {
            var fieldMap = {
                'spmc': '商品名称',
                'spbm': '商品编码',
                'ggxh': '规格型号',
                'dw': '单位',
                'dj': '单价',
                'slv': '税率'
            };
            return fieldMap[fieldName] || fieldName;
        }

        // 获取匹配类型显示名称
        function getMatchTypeDisplayName(matchType) {
            var typeMap = {
                'exact': '精确匹配',
                'fuzzy': '模糊匹配',
                'tolerance': '容差匹配'
            };
            return typeMap[matchType] || matchType;
        }

        // 获取字段参数
        function getFieldParams(field) {
            var params = [];
            if (field.similarity) {
                params.push('相似度: ' + (field.similarity * 100) + '%');
            }
            if (field.tolerance) {
                params.push('容差: ' + (field.tolerance * 100) + '%');
            }
            return params.length > 0 ? params.join('<br/>') : '-';
        }
    </script>
</body>
</html>