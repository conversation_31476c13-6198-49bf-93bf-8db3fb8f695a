<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.swgx.mapper.PpGzMapper">
    
    <resultMap type="PpGz" id="PpGzResult">
        <result property="id"    column="id"    />
        <result property="gzMc"    column="gz_mc"    />
        <result property="gzMs"    column="gz_ms"    />
        <result property="gzLx"    column="gz_lx"    />
        <result property="gzZt"    column="gz_zt"    />
        <result property="gzYxj"    column="gz_yxj"    />
        <result property="gzNr"    column="gz_nr"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPpGzVo">
        select id, gz_mc, gz_ms, gz_lx, gz_zt, gz_yxj, gz_nr, create_by, create_time, update_by, update_time from swgx_pp_gz
    </sql>

    <select id="selectPpGzList" parameterType="PpGz" resultMap="PpGzResult">
        <include refid="selectPpGzVo"/>
        <where>  
            <if test="gzMc != null  and gzMc != ''"> and gz_mc = #{gzMc}</if>
            <if test="gzMs != null  and gzMs != ''"> and gz_ms = #{gzMs}</if>
            <if test="gzLx != null  and gzLx != ''"> and gz_lx = #{gzLx}</if>
            <if test="gzZt != null  and gzZt != ''"> and gz_zt = #{gzZt}</if>
            <if test="gzYxj != null "> and gz_yxj = #{gzYxj}</if>
            <if test="gzNr != null  and gzNr != ''"> and gz_nr = #{gzNr}</if>
        </where>
    </select>
    
    <select id="selectPpGzById" parameterType="String" resultMap="PpGzResult">
        <include refid="selectPpGzVo"/>
        where id = #{id}
    </select>

    <insert id="insertPpGz" parameterType="PpGz">
        insert into swgx_pp_gz
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="gzMc != null and gzMc != ''">gz_mc,</if>
            <if test="gzMs != null">gz_ms,</if>
            <if test="gzLx != null and gzLx != ''">gz_lx,</if>
            <if test="gzZt != null">gz_zt,</if>
            <if test="gzYxj != null">gz_yxj,</if>
            <if test="gzNr != null and gzNr != ''">gz_nr,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="gzMc != null and gzMc != ''">#{gzMc},</if>
            <if test="gzMs != null">#{gzMs},</if>
            <if test="gzLx != null and gzLx != ''">#{gzLx},</if>
            <if test="gzZt != null">#{gzZt},</if>
            <if test="gzYxj != null">#{gzYxj},</if>
            <if test="gzNr != null and gzNr != ''">#{gzNr},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePpGz" parameterType="PpGz">
        update swgx_pp_gz
        <trim prefix="SET" suffixOverrides=",">
            <if test="gzMc != null and gzMc != ''">gz_mc = #{gzMc},</if>
            <if test="gzMs != null">gz_ms = #{gzMs},</if>
            <if test="gzLx != null and gzLx != ''">gz_lx = #{gzLx},</if>
            <if test="gzZt != null">gz_zt = #{gzZt},</if>
            <if test="gzYxj != null">gz_yxj = #{gzYxj},</if>
            <if test="gzNr != null and gzNr != ''">gz_nr = #{gzNr},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePpGzById" parameterType="String">
        delete from swgx_pp_gz where id = #{id}
    </delete>

    <delete id="deletePpGzByIds" parameterType="String">
        delete from swgx_pp_gz where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>