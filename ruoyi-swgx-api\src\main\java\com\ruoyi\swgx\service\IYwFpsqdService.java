package com.ruoyi.swgx.service;

import java.util.List;
import com.ruoyi.swgx.domain.YwFpsqd;

/**
 * 发票申请单信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IYwFpsqdService 
{
    /**
     * 查询发票申请单信息
     * 
     * @param swguid 发票申请单信息主键
     * @return 发票申请单信息
     */
    public YwFpsqd selectYwFpsqdBySwguid(String swguid);

    /**
     * 查询发票申请单信息列表
     * 
     * @param ywFpsqd 发票申请单信息
     * @return 发票申请单信息集合
     */
    public List<YwFpsqd> selectYwFpsqdList(YwFpsqd ywFpsqd);

    /**
     * 新增发票申请单信息
     * 
     * @param ywFpsqd 发票申请单信息
     * @return 结果
     */
    public int insertYwFpsqd(YwFpsqd ywFpsqd);

    /**
     * 修改发票申请单信息
     * 
     * @param ywFpsqd 发票申请单信息
     * @return 结果
     */
    public int updateYwFpsqd(YwFpsqd ywFpsqd);

    /**
     * 批量删除发票申请单信息
     * 
     * @param swguids 需要删除的发票申请单信息主键集合
     * @return 结果
     */
    public int deleteYwFpsqdBySwguids(String swguids);

    /**
     * 删除发票申请单信息信息
     * 
     * @param swguid 发票申请单信息主键
     * @return 结果
     */
    public int deleteYwFpsqdBySwguid(String swguid);
}
