package com.ruoyi.swgx.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.swgx.domain.DpFpxx;
import com.ruoyi.swgx.domain.DpFpxxMx;
import com.ruoyi.swgx.mapper.DpFpxxMapper;
import com.ruoyi.swgx.service.IDpFpxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 电票发票信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
public class DpFpxxServiceImpl implements IDpFpxxService {
    
    @Autowired
    private DpFpxxMapper dpFpxxMapper;

    /**
     * 查询发票信息
     * 
     * @param id 发票信息主键
     * @return 发票信息
     */
    @Override
    public DpFpxx selectDpFpxxById(String id) {
        DpFpxx dpFpxx = dpFpxxMapper.selectDpFpxxById(id);
        if (dpFpxx != null) {
            dpFpxx.setDpFpxxMxList(dpFpxxMapper.selectDpFpxxMxByFpid(id));
        }
        return dpFpxx;
    }

    /**
     * 根据发票代码和号码查询发票信息
     * 
     * @param fpDm 发票代码
     * @param fpHm 发票号码
     * @return 发票信息
     */
    @Override
    public DpFpxx selectDpFpxxByFpDmAndHm(String fpDm, String fpHm) {
        DpFpxx dpFpxx = dpFpxxMapper.selectDpFpxxByFpDmAndHm(fpDm, fpHm);
        if (dpFpxx != null) {
            dpFpxx.setDpFpxxMxList(dpFpxxMapper.selectDpFpxxMxByFpid(dpFpxx.getId()));
        }
        return dpFpxx;
    }

    /**
     * 查询发票信息列表
     * 
     * @param dpFpxx 发票信息
     * @return 发票信息集合
     */
    @Override
    public List<DpFpxx> selectDpFpxxList(DpFpxx dpFpxx) {
        return dpFpxxMapper.selectDpFpxxList(dpFpxx);
    }

    /**
     * 根据企业ID和时间范围查询发票信息
     * 
     * @param qyId 企业ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 发票信息集合
     */
    @Override
    public List<DpFpxx> selectDpFpxxByQyIdAndDateRange(String qyId, String startDate, String endDate) {
        return dpFpxxMapper.selectDpFpxxByQyIdAndDateRange(qyId, startDate, endDate);
    }

    /**
     * 根据购买方纳税人识别号查询发票信息
     * 
     * @param gmfNsrsbh 购买方纳税人识别号
     * @return 发票信息集合
     */
    @Override
    public List<DpFpxx> selectDpFpxxByGmfNsrsbh(String gmfNsrsbh) {
        return dpFpxxMapper.selectDpFpxxByGmfNsrsbh(gmfNsrsbh);
    }

    /**
     * 统计发票数量按状态分组
     * 
     * @param qyId 企业ID
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> countInvoiceByStatus(String qyId) {
        return dpFpxxMapper.countInvoiceByStatus(qyId);
    }

    /**
     * 统计发票金额汇总
     * 
     * @param qyId 企业ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 金额汇总统计
     */
    @Override
    public Map<String, Object> sumInvoiceAmount(String qyId, String startDate, String endDate) {
        return dpFpxxMapper.sumInvoiceAmount(qyId, startDate, endDate);
    }

    /**
     * 新增发票信息
     * 
     * @param dpFpxx 发票信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertDpFpxx(DpFpxx dpFpxx) {
        // 设置主键ID
        if (StringUtils.isEmpty(dpFpxx.getId())) {
            dpFpxx.setId(IdUtils.fastSimpleUUID());
        }
        
        // 设置创建时间
        dpFpxx.setCreateTime(DateUtils.getNowDate());
        
        // 验证发票数据
        String validateResult = validateInvoiceData(dpFpxx);
        if (StringUtils.isNotEmpty(validateResult)) {
            throw new RuntimeException("发票数据验证失败：" + validateResult);
        }
        
        // 检查发票是否已存在
        if (checkInvoiceExists(dpFpxx.getFpDm(), dpFpxx.getFpHm(), null)) {
            throw new RuntimeException("发票已存在：" + dpFpxx.getFpDm() + "-" + dpFpxx.getFpHm());
        }
        
        // 插入发票主表
        int result = dpFpxxMapper.insertDpFpxx(dpFpxx);
        
        // 插入发票明细
        insertDpFpxxMx(dpFpxx);
        
        log.info("新增发票信息成功，发票代码：{}，发票号码：{}", dpFpxx.getFpDm(), dpFpxx.getFpHm());
        return result;
    }

    /**
     * 修改发票信息
     * 
     * @param dpFpxx 发票信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDpFpxx(DpFpxx dpFpxx) {
        // 设置更新时间
        dpFpxx.setUpdateTime(DateUtils.getNowDate());
        
        // 验证发票数据
        String validateResult = validateInvoiceData(dpFpxx);
        if (StringUtils.isNotEmpty(validateResult)) {
            throw new RuntimeException("发票数据验证失败：" + validateResult);
        }
        
        // 检查发票是否已存在（排除自身）
        if (checkInvoiceExists(dpFpxx.getFpDm(), dpFpxx.getFpHm(), dpFpxx.getId())) {
            throw new RuntimeException("发票已存在：" + dpFpxx.getFpDm() + "-" + dpFpxx.getFpHm());
        }
        
        // 删除原有明细
        dpFpxxMapper.deleteDpFpxxMxByFpid(dpFpxx.getId());
        
        // 插入新明细
        insertDpFpxxMx(dpFpxx);
        
        // 更新主表
        int result = dpFpxxMapper.updateDpFpxx(dpFpxx);
        
        log.info("修改发票信息成功，发票代码：{}，发票号码：{}", dpFpxx.getFpDm(), dpFpxx.getFpHm());
        return result;
    }

    /**
     * 更新发票状态
     * 
     * @param id 发票ID
     * @param fpzt 发票状态
     * @param updateBy 更新人
     * @return 结果
     */
    @Override
    public int updateInvoiceStatus(String id, Integer fpzt, String updateBy) {
        log.info("更新发票状态，发票ID：{}，新状态：{}，操作人：{}", id, fpzt, updateBy);
        return dpFpxxMapper.updateInvoiceStatus(id, fpzt, updateBy);
    }

    /**
     * 批量删除发票信息
     * 
     * @param ids 需要删除的发票信息主键集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDpFpxxByIds(String[] ids) {
        // 删除明细数据
        for (String id : ids) {
            dpFpxxMapper.deleteDpFpxxMxByFpid(id);
        }
        
        // 删除主表数据
        int result = dpFpxxMapper.deleteDpFpxxByIds(ids);
        
        log.info("批量删除发票信息成功，删除数量：{}", ids.length);
        return result;
    }

    /**
     * 删除发票信息
     * 
     * @param id 发票信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDpFpxxById(String id) {
        // 删除明细数据
        dpFpxxMapper.deleteDpFpxxMxByFpid(id);
        
        // 删除主表数据
        int result = dpFpxxMapper.deleteDpFpxxById(id);
        
        log.info("删除发票信息成功，发票ID：{}", id);
        return result;
    }

    /**
     * 查询发票明细信息
     * 
     * @param fpid 发票ID
     * @return 发票明细集合
     */
    @Override
    public List<DpFpxxMx> selectDpFpxxMxByFpid(String fpid) {
        return dpFpxxMapper.selectDpFpxxMxByFpid(fpid);
    }

    /**
     * 根据商品名称模糊查询发票明细
     * 
     * @param spmc 商品名称
     * @param qyId 企业ID
     * @return 发票明细集合
     */
    @Override
    public List<DpFpxxMx> selectDpFpxxMxBySpmc(String spmc, String qyId) {
        return dpFpxxMapper.selectDpFpxxMxBySpmc(spmc, qyId);
    }

    /**
     * 根据商品编码查询发票明细
     * 
     * @param spbm 商品编码
     * @param qyId 企业ID
     * @return 发票明细集合
     */
    @Override
    public List<DpFpxxMx> selectDpFpxxMxBySpbm(String spbm, String qyId) {
        return dpFpxxMapper.selectDpFpxxMxBySpbm(spbm, qyId);
    }

    /**
     * 统计发票明细金额汇总
     * 
     * @param fpid 发票ID
     * @return 金额汇总信息
     */
    @Override
    public Map<String, Object> sumInvoiceDetailAmount(String fpid) {
        return dpFpxxMapper.sumInvoiceDetailAmount(fpid);
    }

    /**
     * 查询发票明细统计信息（按商品分组）
     * 
     * @param qyId 企业ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 商品统计信息
     */
    @Override
    public List<Map<String, Object>> selectProductStatistics(String qyId, String startDate, String endDate) {
        return dpFpxxMapper.selectProductStatistics(qyId, startDate, endDate);
    }

    /**
     * 验证发票信息的完整性和合法性
     * 
     * @param dpFpxx 发票信息
     * @return 验证结果消息，null表示验证通过
     */
    @Override
    public String validateInvoiceData(DpFpxx dpFpxx) {
        if (dpFpxx == null) {
            return "发票信息不能为空";
        }
        
        if (StringUtils.isEmpty(dpFpxx.getFpDm())) {
            return "发票代码不能为空";
        }
        
        if (StringUtils.isEmpty(dpFpxx.getFpHm())) {
            return "发票号码不能为空";
        }
        
        if (dpFpxx.getFpzt() == null) {
            return "发票状态不能为空";
        }
        
        // 验证明细数据
        List<DpFpxxMx> mxList = dpFpxx.getDpFpxxMxList();
        if (mxList == null || mxList.isEmpty()) {
            return "发票明细不能为空";
        }
        
        for (int i = 0; i < mxList.size(); i++) {
            DpFpxxMx mx = mxList.get(i);
            if (!mx.isDataComplete()) {
                return "第" + (i + 1) + "行明细数据不完整";
            }
        }
        
        return null;
    }

    /**
     * 检查发票是否已存在
     * 
     * @param fpDm 发票代码
     * @param fpHm 发票号码
     * @param excludeId 排除的发票ID
     * @return true-已存在，false-不存在
     */
    @Override
    public boolean checkInvoiceExists(String fpDm, String fpHm, String excludeId) {
        DpFpxx existingInvoice = dpFpxxMapper.selectDpFpxxByFpDmAndHm(fpDm, fpHm);
        if (existingInvoice == null) {
            return false;
        }
        
        // 如果是修改操作，排除自身
        if (StringUtils.isNotEmpty(excludeId) && excludeId.equals(existingInvoice.getId())) {
            return false;
        }
        
        return true;
    }

    /**
     * 批量导入发票信息
     *
     * @param invoiceList 发票信息列表
     * @param updateSupport 是否支持更新已存在的发票
     * @return 导入结果统计
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchImportInvoices(List<DpFpxx> invoiceList, boolean updateSupport) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        int updateCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (DpFpxx invoice : invoiceList) {
            try {
                // 检查发票是否已存在
                boolean exists = checkInvoiceExists(invoice.getFpDm(), invoice.getFpHm(), null);

                if (exists && !updateSupport) {
                    failCount++;
                    errorMessages.add("发票已存在：" + invoice.getFpDm() + "-" + invoice.getFpHm());
                    continue;
                }

                if (exists && updateSupport) {
                    // 查询已存在的发票ID
                    DpFpxx existingInvoice = selectDpFpxxByFpDmAndHm(invoice.getFpDm(), invoice.getFpHm());
                    invoice.setId(existingInvoice.getId());
                    updateDpFpxx(invoice);
                    updateCount++;
                } else {
                    insertDpFpxx(invoice);
                    successCount++;
                }
            } catch (Exception e) {
                failCount++;
                errorMessages.add("处理发票失败：" + invoice.getFpDm() + "-" + invoice.getFpHm() + "，错误：" + e.getMessage());
                log.error("批量导入发票失败", e);
            }
        }

        result.put("total", invoiceList.size());
        result.put("successCount", successCount);
        result.put("updateCount", updateCount);
        result.put("failCount", failCount);
        result.put("errorMessages", errorMessages);

        log.info("批量导入发票完成，总数：{}，成功：{}，更新：{}，失败：{}",
                invoiceList.size(), successCount, updateCount, failCount);

        return result;
    }

    /**
     * 导出发票信息
     *
     * @param dpFpxx 查询条件
     * @return 发票信息列表
     */
    @Override
    public List<DpFpxx> exportInvoiceList(DpFpxx dpFpxx) {
        List<DpFpxx> invoiceList = selectDpFpxxList(dpFpxx);

        // 为每个发票加载明细信息
        for (DpFpxx invoice : invoiceList) {
            invoice.setDpFpxxMxList(selectDpFpxxMxByFpid(invoice.getId()));
        }

        return invoiceList;
    }

    /**
     * 获取发票的红冲匹配信息
     *
     * @param dpFpxx 查询条件
     * @return 匹配的发票信息
     */
    @Override
    public List<DpFpxx> getRedFlushMatchingInvoices(DpFpxx dpFpxx) {
        // 设置查询条件：只查询正常状态的发票
        dpFpxx.setFpzt(0);
        return dpFpxxMapper.selectRedFlushableInvoiceList(dpFpxx);
    }

    /**
     * 执行发票红冲操作
     *
     * @param originalInvoiceId 原发票ID
     * @param redFlushInvoice 红冲发票信息
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeRedFlush(String originalInvoiceId, DpFpxx redFlushInvoice) {
        try {
            // 查询原发票信息
            DpFpxx originalInvoice = selectDpFpxxById(originalInvoiceId);
            if (originalInvoice == null) {
                throw new RuntimeException("原发票不存在");
            }

            if (!originalInvoice.canRedFlush()) {
                throw new RuntimeException("原发票状态不允许红冲");
            }

            // 设置红冲发票的原发票信息
            redFlushInvoice.setYfpdm(originalInvoice.getFpDm());
            redFlushInvoice.setYfphm(originalInvoice.getFpHm());
            redFlushInvoice.setFpzt(2); // 设置为红冲状态

            // 插入红冲发票
            insertDpFpxx(redFlushInvoice);

            // 更新原发票状态为已红冲
            updateInvoiceStatus(originalInvoiceId, 2, redFlushInvoice.getCreateBy());

            log.info("执行发票红冲成功，原发票：{}-{}，红冲发票：{}-{}",
                    originalInvoice.getFpDm(), originalInvoice.getFpHm(),
                    redFlushInvoice.getFpDm(), redFlushInvoice.getFpHm());

            return true;
        } catch (Exception e) {
            log.error("执行发票红冲失败", e);
            throw new RuntimeException("红冲操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取发票匹配信息
     *
     * @param invoiceId 发票ID
     * @return 匹配信息
     */
    @Override
    public Map<String, Object> getInvoiceMatchInfo(String invoiceId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取发票明细匹配状态信息
            List<Map<String, Object>> matchDetails = dpFpxxMapper.selectInvoiceMatchInfo(invoiceId);

            // 统计信息
            int totalDetails = matchDetails.size();
            int matchedDetails = 0;
            int partialMatchedDetails = 0;
            int unmatchedDetails = 0;

            for (Map<String, Object> detail : matchDetails) {
                String matchStatus = (String) detail.get("matchStatus");
                if ("2".equals(matchStatus)) {
                    matchedDetails++;
                } else if ("1".equals(matchStatus)) {
                    partialMatchedDetails++;
                } else {
                    unmatchedDetails++;
                }
            }

            result.put("totalDetails", totalDetails);
            result.put("matchedDetails", matchedDetails);
            result.put("partialMatchedDetails", partialMatchedDetails);
            result.put("unmatchedDetails", unmatchedDetails);
            result.put("details", matchDetails);

        } catch (Exception e) {
            log.error("获取发票匹配信息失败，发票ID：{}", invoiceId, e);
            result.put("totalDetails", 0);
            result.put("matchedDetails", 0);
            result.put("partialMatchedDetails", 0);
            result.put("unmatchedDetails", 0);
            result.put("details", new ArrayList<>());
        }

        return result;
    }

    /**
     * 获取明细匹配详情
     *
     * @param detailId 明细ID
     * @return 明细匹配详情
     */
    @Override
    public Map<String, Object> getDetailMatchInfo(String detailId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取明细基本信息和匹配状态
            Map<String, Object> detailInfo = dpFpxxMapper.selectDetailMatchInfo(detailId);
            if (detailInfo != null) {
                result.putAll(detailInfo);

                // 获取匹配历史记录
                List<Map<String, Object>> matchHistory = dpFpxxMapper.selectDetailMatchHistory(detailId);
                result.put("matchHistory", matchHistory);
            }

        } catch (Exception e) {
            log.error("获取明细匹配详情失败，明细ID：{}", detailId, e);
        }

        return result;
    }

    /**
     * 插入发票明细信息
     *
     * @param dpFpxx 发票信息
     */
    private void insertDpFpxxMx(DpFpxx dpFpxx) {
        List<DpFpxxMx> dpFpxxMxList = dpFpxx.getDpFpxxMxList();
        if (StringUtils.isNotNull(dpFpxxMxList)) {
            List<DpFpxxMx> list = new ArrayList<>();
            for (DpFpxxMx dpFpxxMx : dpFpxxMxList) {
                // 设置明细ID和关联的发票ID
                if (StringUtils.isEmpty(dpFpxxMx.getId())) {
                    dpFpxxMx.setId(IdUtils.fastSimpleUUID());
                }
                dpFpxxMx.setFpid(dpFpxx.getId());
                dpFpxxMx.setQyId(dpFpxx.getQyId());
                dpFpxxMx.setCreateTime(DateUtils.getNowDate());
                list.add(dpFpxxMx);
            }
            if (list.size() > 0) {
                dpFpxxMapper.batchDpFpxxMx(list);
            }
        }
    }
}
