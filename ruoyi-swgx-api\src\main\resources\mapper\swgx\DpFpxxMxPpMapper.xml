<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.swgx.mapper.DpFpxxMxPpMapper">
    
    <resultMap type="DpFpxxMxPp" id="DpFpxxMxPpResult">
        <result property="id"    column="id"    />
        <result property="fpmxId"    column="fpmx_id"    />
        <result property="sySl"    column="sy_sl"    />
        <result property="syJe"    column="sy_je"    />
        <result property="sySe"    column="sy_se"    />
        <result property="ppCs"    column="pp_cs"    />
        <result property="ppZt"    column="pp_zt"    />
        <result property="lastPpTime"    column="last_pp_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDpFpxxMxPpVo">
        select id, fpmx_id, sy_sl, sy_je, sy_se, pp_cs, pp_zt, last_pp_time, create_time, update_time from swgx_dp_fpxx_mx_pp
    </sql>

    <select id="selectDpFpxxMxPpList" parameterType="DpFpxxMxPp" resultMap="DpFpxxMxPpResult">
        <include refid="selectDpFpxxMxPpVo"/>
        <where>  
            <if test="fpmxId != null  and fpmxId != ''"> and fpmx_id = #{fpmxId}</if>
            <if test="ppZt != null  and ppZt != ''"> and pp_zt = #{ppZt}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDpFpxxMxPpById" parameterType="String" resultMap="DpFpxxMxPpResult">
        <include refid="selectDpFpxxMxPpVo"/>
        where id = #{id}
    </select>

    <select id="selectByFpmxId" parameterType="String" resultMap="DpFpxxMxPpResult">
        <include refid="selectDpFpxxMxPpVo"/>
        where fpmx_id = #{fpmxId}
    </select>
        
    <insert id="insertDpFpxxMxPp" parameterType="DpFpxxMxPp">
        insert into swgx_dp_fpxx_mx_pp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fpmxId != null and fpmxId != ''">fpmx_id,</if>
            <if test="sySl != null">sy_sl,</if>
            <if test="syJe != null">sy_je,</if>
            <if test="sySe != null">sy_se,</if>
            <if test="ppCs != null">pp_cs,</if>
            <if test="ppZt != null">pp_zt,</if>
            <if test="lastPpTime != null">last_pp_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fpmxId != null and fpmxId != ''">#{fpmxId},</if>
            <if test="sySl != null">#{sySl},</if>
            <if test="syJe != null">#{syJe},</if>
            <if test="sySe != null">#{sySe},</if>
            <if test="ppCs != null">#{ppCs},</if>
            <if test="ppZt != null">#{ppZt},</if>
            <if test="lastPpTime != null">#{lastPpTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDpFpxxMxPp" parameterType="DpFpxxMxPp">
        update swgx_dp_fpxx_mx_pp
        <trim prefix="SET" suffixOverrides=",">
            <if test="fpmxId != null and fpmxId != ''">fpmx_id = #{fpmxId},</if>
            <if test="sySl != null">sy_sl = #{sySl},</if>
            <if test="syJe != null">sy_je = #{syJe},</if>
            <if test="sySe != null">sy_se = #{sySe},</if>
            <if test="ppCs != null">pp_cs = #{ppCs},</if>
            <if test="ppZt != null">pp_zt = #{ppZt},</if>
            <if test="lastPpTime != null">last_pp_time = #{lastPpTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDpFpxxMxPpById" parameterType="String">
        delete from swgx_dp_fpxx_mx_pp where id = #{id}
    </delete>

    <delete id="deleteDpFpxxMxPpByIds" parameterType="String">
        delete from swgx_dp_fpxx_mx_pp where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 统计已初始化的电票明细数量 -->
    <select id="countInitializedDetails" resultType="int">
        select count(*) from swgx_dp_fpxx_mx_pp
    </select>

</mapper>
