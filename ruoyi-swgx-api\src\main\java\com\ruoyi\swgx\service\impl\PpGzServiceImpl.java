package com.ruoyi.swgx.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.swgx.mapper.PpGzMapper;
import com.ruoyi.swgx.domain.PpGz;
import com.ruoyi.swgx.service.IPpGzService;
import com.ruoyi.common.core.text.Convert;

/**
 * 匹配规则配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class PpGzServiceImpl implements IPpGzService 
{
    @Autowired
    private PpGzMapper ppGzMapper;

    /**
     * 查询匹配规则配置
     * 
     * @param id 匹配规则配置主键
     * @return 匹配规则配置
     */
    @Override
    public PpGz selectPpGzById(String id)
    {
        return ppGzMapper.selectPpGzById(id);
    }

    /**
     * 查询匹配规则配置列表
     * 
     * @param ppGz 匹配规则配置
     * @return 匹配规则配置
     */
    @Override
    public List<PpGz> selectPpGzList(PpGz ppGz)
    {
        return ppGzMapper.selectPpGzList(ppGz);
    }

    /**
     * 新增匹配规则配置
     * 
     * @param ppGz 匹配规则配置
     * @return 结果
     */
    @Override
    public int insertPpGz(PpGz ppGz)
    {
        ppGz.setCreateTime(DateUtils.getNowDate());
        return ppGzMapper.insertPpGz(ppGz);
    }

    /**
     * 修改匹配规则配置
     * 
     * @param ppGz 匹配规则配置
     * @return 结果
     */
    @Override
    public int updatePpGz(PpGz ppGz)
    {
        ppGz.setUpdateTime(DateUtils.getNowDate());
        return ppGzMapper.updatePpGz(ppGz);
    }

    /**
     * 批量删除匹配规则配置
     * 
     * @param ids 需要删除的匹配规则配置主键
     * @return 结果
     */
    @Override
    public int deletePpGzByIds(String ids)
    {
        return ppGzMapper.deletePpGzByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除匹配规则配置信息
     *
     * @param id 匹配规则配置主键
     * @return 结果
     */
    @Override
    public int deletePpGzById(String id)
    {
        return ppGzMapper.deletePpGzById(id);
    }

    /**
     * 批量更新规则状态
     *
     * @param ids 规则ID集合
     * @param status 状态值
     * @return 结果
     */
    @Override
    public int batchUpdateStatus(String ids, String status)
    {
        String[] idArray = Convert.toStrArray(ids);
        int result = 0;
        for (String id : idArray) {
            PpGz ppGz = ppGzMapper.selectPpGzById(id);
            if (ppGz != null) {
                ppGz.setGzZt(status);
                ppGz.setUpdateTime(DateUtils.getNowDate());
                result += ppGzMapper.updatePpGz(ppGz);
            }
        }
        return result;
    }
}
