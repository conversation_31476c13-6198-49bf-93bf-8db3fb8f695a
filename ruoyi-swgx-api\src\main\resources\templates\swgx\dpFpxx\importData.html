<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('导入发票数据')" />
</head>
<body class="gray-bg">
    <div class="main-content">
        <form id="form-dpFpxx-import" enctype="multipart/form-data" class="form-horizontal">
            <h4 class="form-header h4">导入发票数据</h4>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">选择文件：</label>
                        <div class="col-sm-9">
                            <input type="file" id="file" name="file" accept=".xls,.xlsx" class="form-control" required>
                            <span class="help-block m-b-none">
                                <i class="fa fa-info-circle"></i> 请选择Excel文件(.xls或.xlsx格式)
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">导入选项：</label>
                        <div class="col-sm-9">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" id="updateSupport" name="updateSupport" value="true">
                                    如果发票已经存在，更新这条数据
                                </label>
                            </div>
                            <span class="help-block m-b-none">
                                <i class="fa fa-warning text-warning"></i> 勾选此项将覆盖已存在的发票数据
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <div class="col-sm-offset-3 col-sm-9">
                            <button type="button" class="btn btn-info" onclick="downloadTemplate()">
                                <i class="fa fa-download"></i> 下载导入模板
                            </button>
                            <span class="help-block m-b-none">
                                <i class="fa fa-info-circle"></i> 请先下载模板，按照模板格式填写数据后再导入
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="alert alert-info">
                        <h4><i class="fa fa-info-circle"></i> 导入说明：</h4>
                        <ul>
                            <li>仅支持Excel格式文件(.xls或.xlsx)</li>
                            <li>请严格按照模板格式填写数据</li>
                            <li>发票代码和发票号码为必填项</li>
                            <li>日期格式请使用：yyyyMMddHHmmss</li>
                            <li>金额字段请使用数字格式</li>
                            <li>如果勾选"更新已存在数据"，系统将覆盖重复的发票信息</li>
                        </ul>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "swgx/dpFpxx";
        
        $("#form-dpFpxx-import").validate({
            focusCleanup: true,
            rules: {
                file: {
                    required: true,
                    extension: "xls|xlsx"
                }
            },
            messages: {
                file: {
                    required: "请选择要导入的文件",
                    extension: "请选择Excel文件(.xls或.xlsx格式)"
                }
            }
        });
        
        function submitHandler() {
            if ($.validate.form()) {
                var formData = new FormData();
                var fileInput = document.getElementById('file');
                var updateSupport = document.getElementById('updateSupport').checked;
                
                if (fileInput.files.length === 0) {
                    $.modal.alertWarning("请选择要导入的文件");
                    return;
                }
                
                formData.append('file', fileInput.files[0]);
                formData.append('updateSupport', updateSupport);
                
                $.modal.loading("正在导入数据，请稍候...");
                
                $.ajax({
                    url: prefix + "/importData",
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.alertSuccess(result.msg);
                            var parent = getParent();
                            if (parent) {
                                parent.$.table.refresh();
                            }
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    },
                    error: function(xhr, status, error) {
                        $.modal.closeLoading();
                        $.modal.alertError("导入失败：" + error);
                    }
                });
            }
        }
        
        function downloadTemplate() {
            $.modal.loading("正在生成模板，请稍候...");
            window.location.href = prefix + "/importTemplate";
            setTimeout(function() {
                $.modal.closeLoading();
            }, 2000);
        }
    </script>
</body>
</html>
