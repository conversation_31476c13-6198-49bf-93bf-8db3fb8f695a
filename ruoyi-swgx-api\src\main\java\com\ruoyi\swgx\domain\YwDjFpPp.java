package com.ruoyi.swgx.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 单据发票匹配关系对象 swgx_yw_dj_fp_pp
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class YwDjFpPp extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 匹配ID */
    private String id;

    /** 单据ID */
    @Excel(name = "单据ID")
    private String djId;

    /** 单据明细ID */
    @Excel(name = "单据明细ID")
    private String djmxId;

    /** 发票ID */
    @Excel(name = "发票ID")
    private String fpId;

    /** 发票明细ID */
    @Excel(name = "发票明细ID")
    private String fpmxId;

    /** 匹配数量 */
    @Excel(name = "匹配数量")
    private BigDecimal ppSl;

    /** 匹配金额 */
    @Excel(name = "匹配金额")
    private BigDecimal ppJe;

    /** 匹配税额 */
    @Excel(name = "匹配税额")
    private BigDecimal ppSe;

    /** 匹配得分(0-1) */
    @Excel(name = "匹配得分")
    private BigDecimal ppDf;

    /** 匹配类型：1-精确匹配，2-模糊匹配，3-手工匹配 */
    @Excel(name = "匹配类型", readConverterExp = "1=精确匹配,2=模糊匹配,3=手工匹配")
    private String ppLx;

    /** 匹配状态：1-有效，0-无效 */
    @Excel(name = "匹配状态", readConverterExp = "1=有效,0=无效")
    private String ppZt;

    /** 匹配日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "匹配日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ppRq;

    /** 匹配人员 */
    @Excel(name = "匹配人员")
    private String ppRy;

    /** 红冲状态：0-未红冲，1-红冲中，2-已红冲，3-红冲失败 */
    @Excel(name = "红冲状态", readConverterExp = "0=未红冲,1=红冲中,2=已红冲,3=红冲失败")
    private String hcZt;

    /** 红冲日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "红冲日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date hcRq;
}
