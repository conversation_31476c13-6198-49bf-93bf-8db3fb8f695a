<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发票详情')" />
    <style type="text/css">
        .detail-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .detail-info .form-group {
            margin-bottom: 10px;
        }
        .detail-info label {
            font-weight: bold;
            color: #333;
        }
        .detail-table {
            margin-top: 20px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        .status-normal { background-color: #d4edda; color: #155724; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        .status-red-flushed { background-color: #fff3cd; color: #856404; }

        .statistic-box {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 4px solid #1ab394;
        }
        .statistic-box h4 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
            color: #1ab394;
        }
        .statistic-box p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 14px;
        }

        .match-info-panel {
            margin-top: 20px;
        }

        .match-detail-table {
            margin-top: 15px;
        }

        .match-detail-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="main-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>发票基本信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="detail-info">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>发票代码：</label>
                                        <span id="fpDm"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>发票号码：</label>
                                        <span id="fpHm"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>发票状态：</label>
                                        <span id="fpzt"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>开票日期：</label>
                                        <span id="kprq"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>销售方名称：</label>
                                        <span id="xsfMc"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>销售方纳税人识别号：</label>
                                        <span id="xsfNsrsbh"></span>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>购买方名称：</label>
                                        <span id="gmfMc"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>购买方纳税人识别号：</label>
                                        <span id="gmfNsrsbh"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>合计金额：</label>
                                        <span id="hjje"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>合计税额：</label>
                                        <span id="hjse"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>价税合计：</label>
                                        <span id="jshj"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>开票人：</label>
                                        <span id="kpr"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label>备注：</label>
                                        <span id="bz"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>发票明细信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="detail-table">
                            <table id="bootstrap-table" class="table table-striped"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 匹配信息面板 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>匹配状态信息</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-xs" onclick="refreshMatchInfo()">
                                <i class="fa fa-refresh"></i> 刷新匹配信息
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div id="matchInfoContainer">
                            <div class="text-center">
                                <i class="fa fa-spinner fa-spin"></i> 加载匹配信息中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏的发票ID -->
    <input type="hidden" id="hiddenInvoiceId" th:value="${id}" />

    <th:block th:include="include :: footer" />
    <script th:src="@{/js/swgx-common.js}"></script>
    <script th:inline="javascript">
        var invoiceId = $("#hiddenInvoiceId").val();
        var prefix = ctx + "swgx/dpFpxx";

        $(function() {
            // 重新获取发票ID，确保DOM已加载
            invoiceId = $("#hiddenInvoiceId").val();


            if (!invoiceId) {
                $.modal.alertError("发票ID为空，无法加载详情");
                return;
            }

            // 通过API加载发票信息（包含明细）
            loadInvoiceDetail();

            // 加载匹配信息
            loadMatchInfo();
        });

        function displayInvoiceDetail(data) {
            if (!data) {
                $.modal.alertError("发票数据为空");
                return;
            }

            $("#fpDm").text(data.fpDm || '-');
            $("#fpHm").text(data.fpHm || '-');
            $("#fpzt").html(getStatusBadge(data.fpzt));
            $("#kprq").text(formatDate(data.kprq) || '-');
            $("#xsfMc").text(data.xsfMc || '-');
            $("#xsfNsrsbh").text(data.xsfNsrsbh || '-');
            $("#gmfMc").text(data.gmfMc || '-');
            $("#gmfNsrsbh").text(data.gmfNsrsbh || '-');
            $("#hjje").text(data.hjje ? '¥' + data.hjje : '-');
            $("#hjse").text(data.hjse ? '¥' + data.hjse : '-');
            $("#jshj").text(data.jshj ? '¥' + data.jshj : '-');
            $("#kpr").text(data.kpr || '-');
            $("#bz").text(data.bz || '-');
        }

        function loadInvoiceDetail() {
            $.get(prefix + "/getInfo/" + invoiceId, function(result) {
                if (result.code == web_status.SUCCESS && result.data) {
                    displayInvoiceDetail(result.data);

                    // 如果API返回的数据中包含明细信息，直接使用
                    if (result.data.dpFpxxMxList && result.data.dpFpxxMxList.length > 0) {
                        displayDetailTable(result.data.dpFpxxMxList);
                    } else {
                        // 如果没有明细数据，通过明细API获取
                        loadDetailData();
                    }
                } else {
                    var errorMsg = result.msg || "未知错误";
                    if (!result.data) {
                        errorMsg = "发票数据为空，发票ID：" + invoiceId;
                    }
                    $.modal.alertError("加载发票信息失败：" + errorMsg);
                }
            }).fail(function(xhr, status, error) {
                $.modal.alertError("请求失败：" + error);
            });
        }

        function displayDetailTable(detailData) {
            var options = {
                data: detailData,
                pagination: false,
                search: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                columns: [{
                    field: 'serialNumber',
                    title: '序号',
                    align: 'center',
                    width: 60
                }, {
                    field: 'spmc',
                    title: '商品名称',
                    align: 'left'
                }, {
                    field: 'ggxh',
                    title: '规格型号',
                    align: 'center'
                }, {
                    field: 'dw',
                    title: '单位',
                    align: 'center',
                    width: 80
                }, {
                    field: 'spsl',
                    title: '数量',
                    align: 'right',
                    width: 120,
                    formatter: function(value) {
                        // 使用统一的数量格式化器，显示12位小数
                        return value ? SwgxCommon.formatQuantity(value) : '-';
                    }
                }, {
                    field: 'dj',
                    title: '单价',
                    align: 'right',
                    width: 120,
                    formatter: function(value) {
                        // 使用统一的单价格式化器，显示12位小数
                        return value ? SwgxCommon.formatPrice(value, true) : '-';
                    }
                }, {
                    field: 'je',
                    title: '金额',
                    align: 'right',
                    width: 120,
                    formatter: function(value) {
                        return value ? '¥' + parseFloat(value).toFixed(2) : '-';
                    }
                }, {
                    field: 'sl',
                    title: '税率',
                    align: 'right',
                    width: 100,
                    formatter: function(value) {
                        // 使用统一的税率格式化器，显示为百分比
                        return value ? SwgxCommon.formatTaxRate(value, true) : '-';
                    }
                }, {
                    field: 'se',
                    title: '税额',
                    align: 'right',
                    width: 120,
                    formatter: function(value) {
                        // 使用统一的税额格式化器
                        return value ? SwgxCommon.formatTaxAmount(value, true) : '-';
                    }
                }, {
                    field: 'matchStatus',
                    title: '匹配状态',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        return getMatchStatusBadge(row.matchStatus, row.remainingQuantity);
                    }
                }, {
                    title: '操作',
                    align: 'center',
                    width: 180,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showDetailMatchInfo(\'' + row.id + '\')"><i class="fa fa-eye"></i>匹配详情</a>');

                        return '<div class="btn-group-xs" style="white-space: nowrap;">' + actions.join(' ') + '</div>';
                    }
                }]
            };
            $("#bootstrap-table").bootstrapTable('destroy').bootstrapTable(options);
        }

        function loadDetailData() {
            $.get(prefix + "/detailList/" + invoiceId, function(result) {
                if (result.code == web_status.SUCCESS && result.data) {
                    displayDetailTable(result.data);
                }
            });
        }


        
        function getStatusBadge(status) {
            var statusMap = {
                0: '<span class="status-badge status-normal">正常</span>',
                1: '<span class="status-badge status-cancelled">作废</span>',
                2: '<span class="status-badge status-red-flushed">红冲</span>'
            };
            return statusMap[status] || '<span class="status-badge">未知</span>';
        }
        
        function formatDate(dateStr) {
            if (!dateStr || dateStr.length < 8) return dateStr;
            // yyyyMMddHHmmss -> yyyy-MM-dd HH:mm:ss
            var year = dateStr.substring(0, 4);
            var month = dateStr.substring(4, 6);
            var day = dateStr.substring(6, 8);
            var hour = dateStr.substring(8, 10) || '00';
            var minute = dateStr.substring(10, 12) || '00';
            var second = dateStr.substring(12, 14) || '00';
            return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
        }

        // 加载匹配信息
        function loadMatchInfo() {
            $.get(prefix + "/getMatchInfo/" + invoiceId, function(result) {
                if (result.code == web_status.SUCCESS) {
                    displayMatchInfo(result.data);
                } else {
                    $("#matchInfoContainer").html('<div class="alert alert-warning">暂无匹配信息</div>');
                }
            }).fail(function() {
                $("#matchInfoContainer").html('<div class="alert alert-danger">加载匹配信息失败</div>');
            });
        }

        // 显示匹配信息
        function displayMatchInfo(matchData) {
            if (!matchData || !matchData.details || matchData.details.length === 0) {
                $("#matchInfoContainer").html('<div class="alert alert-info">该发票暂无匹配记录</div>');
                return;
            }

            var html = '<div class="row">';

            // 匹配统计信息
            html += '<div class="col-sm-12">';
            html += '<div class="row">';
            html += '<div class="col-sm-3"><div class="statistic-box"><h4>' + (matchData.totalDetails || 0) + '</h4><p>明细总数</p></div></div>';
            html += '<div class="col-sm-3"><div class="statistic-box"><h4>' + (matchData.matchedDetails || 0) + '</h4><p>已匹配明细</p></div></div>';
            html += '<div class="col-sm-3"><div class="statistic-box"><h4>' + (matchData.partialMatchedDetails || 0) + '</h4><p>部分匹配明细</p></div></div>';
            html += '<div class="col-sm-3"><div class="statistic-box"><h4>' + (matchData.unmatchedDetails || 0) + '</h4><p>未匹配明细</p></div></div>';
            html += '</div></div>';

            // 匹配详情表格
            html += '<div class="col-sm-12" style="margin-top: 20px;">';
            html += '<table class="table table-bordered table-striped">';
            html += '<thead><tr><th>序号</th><th>商品名称</th><th>原始数量</th><th>剩余数量</th><th>匹配状态</th><th>匹配次数</th><th>最后匹配时间</th><th>操作</th></tr></thead>';
            html += '<tbody>';

            matchData.details.forEach(function(detail, index) {
                html += '<tr>';
                html += '<td>' + (index + 1) + '</td>';
                html += '<td>' + (detail.spmc || '-') + '</td>';
                html += '<td>' + (detail.originalQuantity ? SwgxCommon.formatQuantity(detail.originalQuantity) : '-') + '</td>';
                html += '<td>' + (detail.remainingQuantity ? SwgxCommon.formatQuantity(detail.remainingQuantity) : '-') + '</td>';
                html += '<td>' + getMatchStatusBadge(detail.matchStatus, detail.remainingQuantity) + '</td>';
                html += '<td>' + (detail.matchCount || 0) + '</td>';
                html += '<td>' + (detail.lastMatchTime ? formatDateTime(detail.lastMatchTime) : '-') + '</td>';
                html += '<td><a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showDetailMatchInfo(\'' + detail.id + '\')">查看详情</a></td>';
                html += '</tr>';
            });

            html += '</tbody></table>';
            html += '</div></div>';

            $("#matchInfoContainer").html(html);
        }

        // 获取匹配状态标签
        function getMatchStatusBadge(status, remainingQuantity) {
            if (!status) return '<span class="label label-default">未知</span>';

            switch(status) {
                case '0':
                    return '<span class="label label-warning">未匹配</span>';
                case '1':
                    return '<span class="label label-info">部分匹配</span>';
                case '2':
                    return '<span class="label label-success">完全匹配</span>';
                default:
                    return '<span class="label label-default">未知</span>';
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTime) {
            if (!dateTime) return '-';
            var date = new Date(dateTime);
            return date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0') + ' ' +
                   String(date.getHours()).padStart(2, '0') + ':' +
                   String(date.getMinutes()).padStart(2, '0') + ':' +
                   String(date.getSeconds()).padStart(2, '0');
        }

        // 刷新匹配信息
        function refreshMatchInfo() {
            $("#matchInfoContainer").html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> 加载匹配信息中...</div>');
            loadMatchInfo();
        }

        // 显示明细匹配详情
        function showDetailMatchInfo(detailId) {
            $.get(prefix + "/getDetailMatchInfo/" + detailId, function(result) {
                if (result.code == web_status.SUCCESS) {
                    displayDetailMatchDialog(result.data);
                } else {
                    $.modal.msgError(result.msg || "获取匹配详情失败");
                }
            }).fail(function() {
                $.modal.msgError("获取匹配详情失败");
            });
        }

        // 显示明细匹配详情对话框
        function displayDetailMatchDialog(matchData) {
            var content = '<div class="container-fluid">';

            // 基本信息
            content += '<div class="panel panel-default">';
            content += '<div class="panel-heading">';
            content += '<h4 class="panel-title"><i class="fa fa-info-circle"></i> 明细基本信息</h4>';
            content += '</div>';
            content += '<div class="panel-body">';
            content += '<div class="row">';
            content += '<div class="col-sm-12">';
            content += '<div class="row">';
            content += '<div class="col-sm-6">';
            content += '<div class="form-group">';
            content += '<label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">商品名称：</label>';
            content += '<span class="form-control-static" style="display: inline-block; font-weight: bold; word-break: break-all;">' + (matchData.spmc || '-') + '</span>';
            content += '</div>';
            content += '</div>';
            content += '<div class="col-sm-6">';
            content += '<div class="form-group">';
            content += '<label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">规格型号：</label>';
            content += '<span class="form-control-static" style="display: inline-block;">' + (matchData.ggxh || '-') + '</span>';
            content += '</div>';
            content += '</div>';
            content += '</div>';
            content += '<div class="row">';
            content += '<div class="col-sm-4">';
            content += '<div class="form-group">';
            content += '<label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">原始数量：</label>';
            content += '<span class="form-control-static" style="display: inline-block;">' + (matchData.originalQuantity ? SwgxCommon.formatQuantity(matchData.originalQuantity) : '-') + '</span>';
            content += '</div>';
            content += '</div>';
            content += '<div class="col-sm-4">';
            content += '<div class="form-group">';
            content += '<label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">剩余数量：</label>';
            content += '<span class="form-control-static" style="display: inline-block;">' + (matchData.remainingQuantity ? SwgxCommon.formatQuantity(matchData.remainingQuantity) : '-') + '</span>';
            content += '</div>';
            content += '</div>';
            content += '<div class="col-sm-4">';
            content += '<div class="form-group">';
            content += '<label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">匹配状态：</label>';
            content += '<span class="form-control-static" style="display: inline-block;">' + getMatchStatusBadge(matchData.matchStatus) + '</span>';
            content += '</div>';
            content += '</div>';
            content += '</div>';
            content += '</div>';
            content += '</div>';
            content += '</div>';
            content += '</div>';

            // 匹配历史
            if (matchData.matchHistory && matchData.matchHistory.length > 0) {
                content += '<div class="col-sm-12"><hr><h4>匹配历史</h4>';
                content += '<table class="table table-bordered table-striped">';
                content += '<thead><tr><th>匹配时间</th><th>单据编号</th><th>匹配数量</th><th>匹配金额</th><th>匹配得分</th><th>匹配类型</th></tr></thead>';
                content += '<tbody>';

                matchData.matchHistory.forEach(function(history) {
                    content += '<tr>';
                    content += '<td>' + formatDateTime(history.matchTime) + '</td>';
                    content += '<td>' + (history.documentNo || '-') + '</td>';
                    content += '<td>' + (history.matchQuantity ? SwgxCommon.formatQuantity(history.matchQuantity) : '-') + '</td>';
                    content += '<td>' + (history.matchAmount ? '¥' + history.matchAmount.toFixed(2) : '-') + '</td>';
                    content += '<td>' + (history.matchScore ? (history.matchScore * 100).toFixed(1) + '%' : '-') + '</td>';
                    content += '<td>' + getMatchTypeLabel(history.matchType) + '</td>';
                    content += '</tr>';
                });

                content += '</tbody></table></div>';
            } else {
                content += '<div class="col-sm-12"><hr><div class="alert alert-info">暂无匹配历史记录</div></div>';
            }

            content += '</div>';

            layer.open({
                title: "明细匹配详情",
                type: 1,
                area: ['900px', '600px'],
                content: content,
                btn: ['关闭'],
                btn1: function(index) {
                    layer.close(index);
                }
            });
        }

        // 获取匹配类型标签
        function getMatchTypeLabel(matchType) {
            if (!matchType) return '<span class="label label-default">未知</span>';

            // 处理数字类型和字符串类型
            switch (matchType.toString()) {
                case '1':
                case 'EXACT':
                    return '<span class="label label-success">精确匹配</span>';
                case '2':
                case 'FUZZY':
                    return '<span class="label label-info">模糊匹配</span>';
                case '3':
                case 'PARTIAL':
                case 'MANUAL':
                    return '<span class="label label-warning">手工匹配</span>';
                default:
                    return '<span class="label label-default">未知</span>';
            }
        }


    </script>
</body>
</html>
