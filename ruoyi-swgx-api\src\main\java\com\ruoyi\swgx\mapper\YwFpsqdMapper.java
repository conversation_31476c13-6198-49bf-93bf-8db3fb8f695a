package com.ruoyi.swgx.mapper;

import java.util.List;
import com.ruoyi.swgx.domain.YwFpsqd;
import com.ruoyi.swgx.domain.YwFpsqdmx;

/**
 * 发票申请单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface YwFpsqdMapper 
{
    /**
     * 查询发票申请单信息
     * 
     * @param swguid 发票申请单信息主键
     * @return 发票申请单信息
     */
    public YwFpsqd selectYwFpsqdBySwguid(String swguid);

    /**
     * 查询发票申请单信息列表
     * 
     * @param ywFpsqd 发票申请单信息
     * @return 发票申请单信息集合
     */
    public List<YwFpsqd> selectYwFpsqdList(YwFpsqd ywFpsqd);

    /**
     * 新增发票申请单信息
     * 
     * @param ywFpsqd 发票申请单信息
     * @return 结果
     */
    public int insertYwFpsqd(YwFpsqd ywFpsqd);

    /**
     * 修改发票申请单信息
     * 
     * @param ywFpsqd 发票申请单信息
     * @return 结果
     */
    public int updateYwFpsqd(YwFpsqd ywFpsqd);

    /**
     * 删除发票申请单信息
     * 
     * @param swguid 发票申请单信息主键
     * @return 结果
     */
    public int deleteYwFpsqdBySwguid(String swguid);

    /**
     * 批量删除发票申请单信息
     * 
     * @param swguids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYwFpsqdBySwguids(String[] swguids);

    /**
     * 批量删除发票申请单明细
     * 
     * @param swguids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYwFpsqdmxBySWMAINGUIDs(String[] swguids);
    
    /**
     * 批量新增发票申请单明细
     * 
     * @param ywFpsqdmxList 发票申请单明细列表
     * @return 结果
     */
    public int batchYwFpsqdmx(List<YwFpsqdmx> ywFpsqdmxList);
    

    /**
     * 通过发票申请单信息主键删除发票申请单明细信息
     * 
     * @param swguid 发票申请单信息ID
     * @return 结果
     */
    public int deleteYwFpsqdmxBySWMAINGUID(String swguid);
}
