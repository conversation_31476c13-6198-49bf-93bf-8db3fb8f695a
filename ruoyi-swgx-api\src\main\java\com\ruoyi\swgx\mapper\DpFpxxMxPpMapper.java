package com.ruoyi.swgx.mapper;

import com.ruoyi.swgx.domain.DpFpxxMxPp;
import java.util.List;

/**
 * 电票明细匹配状态Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface DpFpxxMxPpMapper {
    
    /**
     * 查询电票明细匹配状态
     * 
     * @param id 电票明细匹配状态主键
     * @return 电票明细匹配状态
     */
    public DpFpxxMxPp selectDpFpxxMxPpById(String id);

    /**
     * 根据发票明细ID查询匹配状态
     * 
     * @param fpmxId 发票明细ID
     * @return 电票明细匹配状态
     */
    public DpFpxxMxPp selectByFpmxId(String fpmxId);

    /**
     * 查询电票明细匹配状态列表
     * 
     * @param dpFpxxMxPp 电票明细匹配状态
     * @return 电票明细匹配状态集合
     */
    public List<DpFpxxMxPp> selectDpFpxxMxPpList(DpFpxxMxPp dpFpxxMxPp);

    /**
     * 新增电票明细匹配状态
     * 
     * @param dpFpxxMxPp 电票明细匹配状态
     * @return 结果
     */
    public int insertDpFpxxMxPp(DpFpxxMxPp dpFpxxMxPp);

    /**
     * 修改电票明细匹配状态
     * 
     * @param dpFpxxMxPp 电票明细匹配状态
     * @return 结果
     */
    public int updateDpFpxxMxPp(DpFpxxMxPp dpFpxxMxPp);

    /**
     * 删除电票明细匹配状态
     * 
     * @param id 电票明细匹配状态主键
     * @return 结果
     */
    public int deleteDpFpxxMxPpById(String id);

    /**
     * 批量删除电票明细匹配状态
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDpFpxxMxPpByIds(String[] ids);

    /**
     * 统计已初始化的电票明细数量
     *
     * @return 已初始化数量
     */
    public int countInitializedDetails();
}
