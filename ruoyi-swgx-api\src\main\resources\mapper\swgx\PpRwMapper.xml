<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.swgx.mapper.PpRwMapper">
    
    <resultMap type="PpRw" id="PpRwResult">
        <result property="id"    column="id"    />
        <result property="rwMc"    column="rw_mc"    />
        <result property="rwLx"    column="rw_lx"    />
        <result property="rwZt"    column="rw_zt"    />
        <result property="djIds"    column="dj_ids"    />
        <result property="ppGzIds"    column="pp_gz_ids"    />
        <result property="ppCs"    column="pp_cs"    />
        <result property="cgCs"    column="cg_cs"    />
        <result property="sbCs"    column="sb_cs"    />
        <result property="ksSj"    column="ks_sj"    />
        <result property="jsSj"    column="js_sj"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPpRwVo">
        select id, rw_mc, rw_lx, rw_zt, dj_ids, pp_gz_ids, pp_cs, cg_cs, sb_cs, ks_sj, js_sj, error_msg, create_by, create_time, update_by, update_time from swgx_pp_rw
    </sql>

    <select id="selectPpRwList" parameterType="PpRw" resultMap="PpRwResult">
        <include refid="selectPpRwVo"/>
        <where>  
            <if test="rwMc != null  and rwMc != ''"> and rw_mc like concat('%', #{rwMc}, '%')</if>
            <if test="rwLx != null  and rwLx != ''"> and rw_lx = #{rwLx}</if>
            <if test="rwZt != null  and rwZt != ''"> and rw_zt = #{rwZt}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPpRwById" parameterType="String" resultMap="PpRwResult">
        <include refid="selectPpRwVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPpRw" parameterType="PpRw">
        insert into swgx_pp_rw
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="rwMc != null and rwMc != ''">rw_mc,</if>
            <if test="rwLx != null and rwLx != ''">rw_lx,</if>
            <if test="rwZt != null">rw_zt,</if>
            <if test="djIds != null">dj_ids,</if>
            <if test="ppGzIds != null">pp_gz_ids,</if>
            <if test="ppCs != null">pp_cs,</if>
            <if test="cgCs != null">cg_cs,</if>
            <if test="sbCs != null">sb_cs,</if>
            <if test="ksSj != null">ks_sj,</if>
            <if test="jsSj != null">js_sj,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="rwMc != null and rwMc != ''">#{rwMc},</if>
            <if test="rwLx != null and rwLx != ''">#{rwLx},</if>
            <if test="rwZt != null">#{rwZt},</if>
            <if test="djIds != null">#{djIds},</if>
            <if test="ppGzIds != null">#{ppGzIds},</if>
            <if test="ppCs != null">#{ppCs},</if>
            <if test="cgCs != null">#{cgCs},</if>
            <if test="sbCs != null">#{sbCs},</if>
            <if test="ksSj != null">#{ksSj},</if>
            <if test="jsSj != null">#{jsSj},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePpRw" parameterType="PpRw">
        update swgx_pp_rw
        <trim prefix="SET" suffixOverrides=",">
            <if test="rwMc != null and rwMc != ''">rw_mc = #{rwMc},</if>
            <if test="rwLx != null and rwLx != ''">rw_lx = #{rwLx},</if>
            <if test="rwZt != null">rw_zt = #{rwZt},</if>
            <if test="djIds != null">dj_ids = #{djIds},</if>
            <if test="ppGzIds != null">pp_gz_ids = #{ppGzIds},</if>
            <if test="ppCs != null">pp_cs = #{ppCs},</if>
            <if test="cgCs != null">cg_cs = #{cgCs},</if>
            <if test="sbCs != null">sb_cs = #{sbCs},</if>
            <if test="ksSj != null">ks_sj = #{ksSj},</if>
            <if test="jsSj != null">js_sj = #{jsSj},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePpRwById" parameterType="String">
        delete from swgx_pp_rw where id = #{id}
    </delete>

    <delete id="deletePpRwByIds" parameterType="String">
        delete from swgx_pp_rw where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
