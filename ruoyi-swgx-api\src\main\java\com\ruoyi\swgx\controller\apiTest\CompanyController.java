package com.ruoyi.swgx.controller.apiTest;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.swgx.common.dto.company.QueryUniqueSignRequest;
import com.ruoyi.swgx.common.dto.company.QueryUniqueSignResponse;
import com.ruoyi.swgx.service.apiTest.CompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 企业相关接口控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/swgx/company")
@Validated
public class CompanyController extends BaseController {

    @Autowired
    private CompanyService companyService;

    /**
     * 查询企业唯一标识
     */
    @PostMapping("/queryUniqueSign")
    public AjaxResult queryUniqueSign(@Valid @RequestBody QueryUniqueSignRequest request) {
        try {
            log.info("查询企业唯一标识请求: {}", request);
            
            QueryUniqueSignResponse response = companyService.queryUniqueSign(
                request.getNsrmc(), 
                request.getNsrsbh()
            );
            
            return AjaxResult.success("查询成功", response);
            
        } catch (Exception e) {
            log.error("查询企业唯一标识失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据纳税人识别号获取企业唯一标识
     */
    @GetMapping("/uniqueId/{nsrsbh}")
    public AjaxResult getUniqueId(
            @PathVariable @NotBlank(message = "纳税人识别号不能为空") String nsrsbh) {
        try {
            log.info("获取企业唯一标识: nsrsbh={}", nsrsbh);
            
            String uniqueId = companyService.getCompanyUniqueId(nsrsbh);
            
            return AjaxResult.success("获取成功", uniqueId);
            
        } catch (Exception e) {
            log.error("获取企业唯一标识失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }
}
