package com.ruoyi.swgx.service.impl;

import com.ruoyi.swgx.service.IMatchService;
import com.ruoyi.swgx.domain.dto.MatchRequestDto;
import com.ruoyi.swgx.domain.dto.MatchProgressDto;

import com.ruoyi.swgx.domain.PpRw;
import com.ruoyi.swgx.domain.YwDjxx;
import com.ruoyi.swgx.domain.YwDjmx;
import com.ruoyi.swgx.domain.DpFpxx;
import com.ruoyi.swgx.domain.DpFpxxMx;
import com.ruoyi.swgx.domain.DpFpxxMxPp;
import com.ruoyi.swgx.domain.YwDjFpPp;
import com.ruoyi.swgx.mapper.PpRwMapper;
import com.ruoyi.swgx.mapper.YwDjxxMapper;
import com.ruoyi.swgx.mapper.DpFpxxMapper;
import com.ruoyi.swgx.mapper.DpFpxxMxPpMapper;
import com.ruoyi.swgx.mapper.YwDjFpPpMapper;
import com.ruoyi.swgx.service.IYwFpsqdService;
import com.ruoyi.swgx.service.IMatchStatusService;
import com.ruoyi.swgx.domain.YwFpsqd;
import com.ruoyi.swgx.domain.YwFpsqdmx;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ShiroUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.scheduling.annotation.Async;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 匹配服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
public class MatchServiceImpl implements IMatchService {
    
    @Autowired
    private PpRwMapper ppRwMapper;
    
    @Autowired
    private YwDjxxMapper ywDjxxMapper;
    
    @Autowired
    private DpFpxxMapper dpFpxxMapper;
    
    @Autowired
    private DpFpxxMxPpMapper dpFpxxMxPpMapper;
    
    @Autowired
    private YwDjFpPpMapper ywDjFpPpMapper;

    @Autowired
    private IYwFpsqdService ywFpsqdService;

    @Autowired
    private IMatchStatusService matchStatusService;

    /** 任务进度缓存 */
    private final Map<String, MatchProgressDto> taskProgressCache = new ConcurrentHashMap<>();
    
    @Override
    @Transactional
    public String startMatchTask(MatchRequestDto request) {
        log.info("启动匹配任务，请求参数：{}", request);

        // 创建匹配任务
        PpRw task = new PpRw();
        task.setId(IdUtils.fastSimpleUUID());
        task.setRwMc("匹配任务-" + DateUtils.dateTimeNow());
        task.setRwLx(request.getMatchType());
        task.setRwZt("PENDING");
        task.setDjIds(String.join(",", request.getDjIds()));
        task.setPpGzIds(String.join(",", request.getMatchRules()));
        task.setPpCs(0);
        task.setCgCs(0);
        task.setSbCs(0);
        task.setCreateBy(ShiroUtils.getLoginName());
        task.setCreateTime(DateUtils.getNowDate());
        
        // 保存任务
        ppRwMapper.insertPpRw(task);
        
        // 初始化进度缓存
        MatchProgressDto progress = new MatchProgressDto();
        progress.setTaskId(task.getId());
        progress.setStatus("PENDING");
        progress.setTotalCount(request.getDjIds().size());
        progress.setSuccessCount(0);
        progress.setFailedCount(0);
        progress.setProgress(0);
        progress.setStatusDesc("任务已创建，等待执行");
        taskProgressCache.put(task.getId(), progress);
        
        // 异步执行匹配任务
        executeMatchTaskAsync(task, request);
        
        return task.getId();
    }
    
    @Override
    public MatchProgressDto getMatchProgress(String taskId) {
        MatchProgressDto progress = taskProgressCache.get(taskId);
        if (progress == null) {
            // 从数据库查询任务状态
            PpRw task = ppRwMapper.selectPpRwById(taskId);
            if (task != null) {
                progress = new MatchProgressDto();
                progress.setTaskId(taskId);
                progress.setStatus(task.getRwZt());
                progress.setTotalCount(task.getPpCs());
                progress.setSuccessCount(task.getCgCs());
                progress.setFailedCount(task.getSbCs());
                progress.setProgress(calculateProgress(task.getCgCs(), task.getSbCs(), task.getPpCs()));
                progress.setStatusDesc(getStatusDescription(task.getRwZt()));
                progress.setErrorMsg(task.getErrorMsg());
            }
        }
        return progress;
    }
    
    @Override
    @Transactional
    public boolean cancelMatchTask(String taskId) {
        try {
            PpRw task = ppRwMapper.selectPpRwById(taskId);
            if (task != null && "RUNNING".equals(task.getRwZt())) {
                task.setRwZt("CANCELLED");
                task.setJsSj(DateUtils.getNowDate());
                task.setUpdateBy(ShiroUtils.getLoginName());
                task.setUpdateTime(DateUtils.getNowDate());
                ppRwMapper.updatePpRw(task);
                
                // 更新进度缓存
                MatchProgressDto progress = taskProgressCache.get(taskId);
                if (progress != null) {
                    progress.setStatus("CANCELLED");
                    progress.setStatusDesc("任务已取消");
                }
                
                return true;
            }
        } catch (Exception e) {
            log.error("取消匹配任务失败，任务ID：{}", taskId, e);
        }
        return false;
    }
    
    @Async
    public void executeMatchTaskAsync(PpRw task, MatchRequestDto request) {
        try {
            executeMatchTask(task);
        } catch (Exception e) {
            log.error("异步执行匹配任务失败，任务ID：{}", task.getId(), e);
            updateTaskStatus(task.getId(), "FAILED", e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public void executeMatchTask(PpRw task) {
        log.info("开始执行匹配任务，任务ID：{}", task.getId());
        
        try {
            // 更新任务状态为执行中
            updateTaskStatus(task.getId(), "RUNNING", null);
            
            // 获取单据列表
            String[] djIdArray = task.getDjIds().split(",");
            List<String> djIds = Arrays.asList(djIdArray);
            
            // 初始化电票明细匹配状态
            initInvoiceDetailMatchStatus();
            
            int totalCount = 0;
            int successCount = 0;
            int failedCount = 0;
            
            for (String djId : djIds) {
                try {
                    // 执行单个单据的匹配
                    boolean result = matchSingleDocument(djId, task);
                    if (result) {
                        successCount++;
                    } else {
                        failedCount++;
                    }
                    totalCount++;
                    
                    // 更新进度
                    updateProgress(task.getId(), totalCount, successCount, failedCount, djIds.size());
                    
                } catch (Exception e) {
                    log.error("匹配单据失败，单据ID：{}，任务ID：{}", djId, task.getId(), e);
                    failedCount++;
                    totalCount++;
                    updateProgress(task.getId(), totalCount, successCount, failedCount, djIds.size());
                }
            }
            
            // 更新任务完成状态
            task.setPpCs(totalCount);
            task.setCgCs(successCount);
            task.setSbCs(failedCount);
            task.setJsSj(DateUtils.getNowDate());
            task.setUpdateBy(ShiroUtils.getLoginName());
            task.setUpdateTime(DateUtils.getNowDate());
            
            if (failedCount == 0) {
                updateTaskStatus(task.getId(), "SUCCESS", null);
            } else if (successCount == 0) {
                updateTaskStatus(task.getId(), "FAILED", "所有匹配都失败");
            } else {
                updateTaskStatus(task.getId(), "SUCCESS", "部分匹配成功");
            }
            
            log.info("匹配任务执行完成，任务ID：{}，总数：{}，成功：{}，失败：{}", 
                    task.getId(), totalCount, successCount, failedCount);
                    
        } catch (Exception e) {
            log.error("执行匹配任务失败，任务ID：{}", task.getId(), e);
            updateTaskStatus(task.getId(), "FAILED", e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public void initInvoiceDetailMatchStatus() {
        log.info("开始初始化电票明细匹配状态");

        try {
            // 统计总的电票明细数量
            int totalDetails = dpFpxxMapper.countAllDetails();
            log.info("电票明细总数：{}", totalDetails);

            // 统计已初始化的数量
            int initializedCount = dpFpxxMxPpMapper.countInitializedDetails();
            log.info("已初始化的电票明细数量：{}", initializedCount);

            // 查询所有未初始化的电票明细
            List<DpFpxxMx> uninitializedDetails = dpFpxxMapper.selectUninitializedDetails();
            log.info("待初始化的电票明细数量：{}", uninitializedDetails.size());

            if (uninitializedDetails.isEmpty()) {
                log.info("所有电票明细已初始化，无需处理");
                return;
            }

            int processedCount = 0;
            int skippedCount = 0;

            for (DpFpxxMx detail : uninitializedDetails) {
                try {
                    DpFpxxMxPp matchStatus = new DpFpxxMxPp();
                    matchStatus.setId(IdUtils.fastSimpleUUID());
                    matchStatus.setFpmxId(detail.getId());
                    matchStatus.setSySl(detail.getSpsl()); // 剩余数量等于原始数量
                    matchStatus.setSyJe(detail.getJe());   // 剩余金额等于原始金额
                    matchStatus.setSySe(detail.getSe());   // 剩余税额等于原始税额
                    matchStatus.setPpCs(0);
                    matchStatus.setPpZt("0"); // 未匹配
                    matchStatus.setCreateTime(DateUtils.getNowDate());
                    matchStatus.setUpdateTime(DateUtils.getNowDate());

                    dpFpxxMxPpMapper.insertDpFpxxMxPp(matchStatus);
                    processedCount++;

                } catch (Exception e) {
                    // 如果是唯一约束冲突，说明已经存在，跳过即可
                    if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                        skippedCount++;
                        log.debug("电票明细已存在匹配状态，跳过 - 明细ID: {}", detail.getId());
                    } else {
                        // 其他异常需要记录并重新抛出
                        log.error("初始化电票明细匹配状态失败 - 明细ID: {}", detail.getId(), e);
                        throw e;
                    }
                }

                // 每处理100条记录输出一次进度日志
                if ((processedCount + skippedCount) % 100 == 0) {
                    log.info("已处理 {} / {} 条电票明细（成功: {}, 跳过: {})",
                            processedCount + skippedCount, uninitializedDetails.size(),
                            processedCount, skippedCount);
                }
            }

            log.info("电票明细匹配状态初始化完成，总计: {} 条，成功: {} 条，跳过: {} 条",
                    uninitializedDetails.size(), processedCount, skippedCount);

        } catch (Exception e) {
            log.error("初始化电票明细匹配状态失败", e);
            throw new RuntimeException("初始化电票明细匹配状态失败", e);
        }
    }
    
    /**
     * 匹配单个单据
     */
    private boolean matchSingleDocument(String djId, PpRw task) {
        log.info("开始匹配单据，单据ID：{}", djId);
        
        try {
            // 查询单据及其明细
            YwDjxx document = ywDjxxMapper.selectYwDjxxById(djId);
            if (document == null) {
                log.warn("单据不存在，单据ID：{}", djId);
                return false;
            }
            
            List<YwDjmx> documentDetails = ywDjxxMapper.selectYwDjmxByDjId(djId);
            if (documentDetails.isEmpty()) {
                log.warn("单据明细为空，单据ID：{}", djId);
                return false;
            }
            
            // 查询可匹配的电票明细
            List<DpFpxxMx> availableInvoiceDetails = dpFpxxMapper.selectAvailableDetailsForMatch(
                document.getGmfNsrsbh());

            if (availableInvoiceDetails.isEmpty()) {
                // 检查是否有电票明细但未初始化
                List<DpFpxxMx> uninitializedDetails = dpFpxxMapper.selectUninitializedDetails();
                if (!uninitializedDetails.isEmpty()) {
                    log.warn("存在未初始化的电票明细，请先执行初始化操作，单据ID：{}", djId);
                    throw new RuntimeException("存在未初始化的电票明细，请先在电票管理页面点击'初始化匹配状态'按钮");
                } else {
                    log.warn("没有可匹配的电票明细，单据ID：{}", djId);
                    return false;
                }
            }
            
            // 执行匹配算法
            List<MatchResult> matchResults = performMatching(documentDetails, availableInvoiceDetails);
            
            // 保存匹配结果
            saveMatchResults(djId, matchResults);

            // 更新单据匹配状态
            updateDocumentMatchStatus(djId);

            // 生成发票申请单
            if (!matchResults.isEmpty()) {
                generateInvoiceApplication(djId, document, matchResults);
            }

            return true;
            
        } catch (Exception e) {
            log.error("匹配单据失败，单据ID：{}", djId, e);
            return false;
        }
    }
    
    /**
     * 执行匹配算法
     */
    private List<MatchResult> performMatching(List<YwDjmx> documentDetails, List<DpFpxxMx> invoiceDetails) {
        List<MatchResult> results = new ArrayList<>();

        // 为每个单据明细寻找最佳匹配
        for (YwDjmx docDetail : documentDetails) {
            List<MatchCandidate> candidates = findMatchCandidates(docDetail, invoiceDetails);

            if (!candidates.isEmpty()) {
                // 选择最佳匹配候选
                MatchCandidate bestMatch = selectBestMatch(candidates);

                // 计算匹配数量
                BigDecimal matchQuantity = calculateMatchQuantity(docDetail, bestMatch.getInvoiceDetail());

                if (matchQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    MatchResult result = new MatchResult();
                    result.setDocumentDetail(docDetail);
                    result.setInvoiceDetail(bestMatch.getInvoiceDetail());
                    result.setMatchQuantity(matchQuantity);

                    // 修复：精确匹配时，如果找到匹配项，得分应该是100%
                    // 检查是否为精确匹配（所有关键字段完全相等）
                    if (isExactMatch(docDetail, bestMatch.getInvoiceDetail())) {
                        result.setMatchScore(1.0); // 精确匹配得分100%
                        result.setMatchType("1"); // 精确匹配
                        log.info("精确匹配成功，单据明细ID：{}，电票明细ID：{}，得分：100%",
                                docDetail.getId(), bestMatch.getInvoiceDetail().getId());
                    } else {
                        result.setMatchScore(bestMatch.getScore());
                        result.setMatchType("2"); // 模糊匹配
                        log.info("模糊匹配，单据明细ID：{}，电票明细ID：{}，得分：{}%",
                                docDetail.getId(), bestMatch.getInvoiceDetail().getId(),
                                (bestMatch.getScore() * 100));
                    }

                    results.add(result);
                }
            }
        }

        return results;
    }
    
    /**
     * 寻找匹配候选
     */
    private List<MatchCandidate> findMatchCandidates(YwDjmx docDetail, List<DpFpxxMx> invoiceDetails) {
        List<MatchCandidate> candidates = new ArrayList<>();
        List<MatchCandidate> exactMatches = new ArrayList<>();

        for (DpFpxxMx invDetail : invoiceDetails) {
            // 首先检查是否为精确匹配
            if (isExactMatch(docDetail, invDetail)) {
                MatchCandidate exactCandidate = new MatchCandidate();
                exactCandidate.setInvoiceDetail(invDetail);
                exactCandidate.setScore(1.0); // 精确匹配得分100%
                exactMatches.add(exactCandidate);
                log.debug("找到精确匹配候选，单据明细ID：{}，电票明细ID：{}",
                         docDetail.getId(), invDetail.getId());
            } else {
                // 计算模糊匹配得分
                double score = calculateMatchScore(docDetail, invDetail);
                if (score > 0.6) { // 匹配度阈值
                    MatchCandidate candidate = new MatchCandidate();
                    candidate.setInvoiceDetail(invDetail);
                    candidate.setScore(score);
                    candidates.add(candidate);
                }
            }
        }

        // 如果有精确匹配，优先返回精确匹配
        if (!exactMatches.isEmpty()) {
            log.info("找到{}个精确匹配候选，单据明细ID：{}", exactMatches.size(), docDetail.getId());
            return exactMatches;
        }

        // 否则返回模糊匹配，按匹配度排序
        candidates.sort((a, b) -> Double.compare(b.getScore(), a.getScore()));
        log.info("找到{}个模糊匹配候选，单据明细ID：{}", candidates.size(), docDetail.getId());

        return candidates;
    }
    
    /**
     * 计算匹配得分
     */
    private double calculateMatchScore(YwDjmx docDetail, DpFpxxMx invDetail) {
        double score = 0.0;

        // 商品名称匹配（权重40%）
        if (StringUtils.isNotEmpty(docDetail.getSpMc()) && StringUtils.isNotEmpty(invDetail.getSpmc())) {
            if (docDetail.getSpMc().equals(invDetail.getSpmc())) {
                score += 0.4;
            } else if (docDetail.getSpMc().contains(invDetail.getSpmc()) ||
                      invDetail.getSpmc().contains(docDetail.getSpMc())) {
                score += 0.2;
            }
        }

        // 单价匹配（权重30%）
        if (docDetail.getDj() != null && invDetail.getDj() != null) {
            if (docDetail.getDj().compareTo(invDetail.getDj()) == 0) {
                score += 0.3;
            } else {
                // 允许一定的价格差异（5%以内）
                BigDecimal diff = docDetail.getDj().subtract(invDetail.getDj()).abs();
                BigDecimal tolerance = docDetail.getDj().multiply(new BigDecimal("0.05"));
                if (diff.compareTo(tolerance) <= 0) {
                    score += 0.15;
                }
            }
        }

        // 税率匹配（权重20%）
        if (docDetail.getSlv() != null && invDetail.getSl() != null) {
            if (docDetail.getSlv().compareTo(invDetail.getSl()) == 0) {
                score += 0.2;
            }
        }

        // 商品编码匹配（权重10%）
        if (StringUtils.isNotEmpty(docDetail.getSpBm()) && StringUtils.isNotEmpty(invDetail.getSpbm())) {
            if (docDetail.getSpBm().equals(invDetail.getSpbm())) {
                score += 0.1;
            }
        }

        return score;
    }

    /**
     * 判断是否为精确匹配
     * 精确匹配要求所有关键字段完全相等
     *
     * @param docDetail 单据明细
     * @param invDetail 电票明细
     * @return 是否为精确匹配
     */
    private boolean isExactMatch(YwDjmx docDetail, DpFpxxMx invDetail) {
        // 商品名称必须完全相等
        if (!StringUtils.isNotEmpty(docDetail.getSpMc()) || !StringUtils.isNotEmpty(invDetail.getSpmc()) ||
            !docDetail.getSpMc().equals(invDetail.getSpmc())) {
            return false;
        }

        // 单价必须完全相等
        if (docDetail.getDj() == null || invDetail.getDj() == null ||
            docDetail.getDj().compareTo(invDetail.getDj()) != 0) {
            return false;
        }

        // 税率必须完全相等
        if (docDetail.getSlv() == null || invDetail.getSl() == null ||
            docDetail.getSlv().compareTo(invDetail.getSl()) != 0) {
            return false;
        }

        // 商品编码如果都存在，必须完全相等
        if (StringUtils.isNotEmpty(docDetail.getSpBm()) && StringUtils.isNotEmpty(invDetail.getSpbm()) &&
            !docDetail.getSpBm().equals(invDetail.getSpbm())) {
            return false;
        }

        return true;
    }
    
    /**
     * 选择最佳匹配
     */
    private MatchCandidate selectBestMatch(List<MatchCandidate> candidates) {
        return candidates.get(0); // 已按得分排序，取第一个
    }
    
    /**
     * 计算匹配数量
     */
    private BigDecimal calculateMatchQuantity(YwDjmx docDetail, DpFpxxMx invDetail) {
        // 查询电票明细的剩余可匹配数量
        DpFpxxMxPp matchStatus = dpFpxxMxPpMapper.selectByFpmxId(invDetail.getId());

        if (matchStatus == null || matchStatus.getSySl().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 取单据明细剩余数量和电票明细剩余数量的最小值
        BigDecimal docRemaining = docDetail.getSySl();
        BigDecimal invRemaining = matchStatus.getSySl();

        // 如果单据明细剩余数量为null，使用原始数量
        if (docRemaining == null) {
            docRemaining = docDetail.getSl();
        }

        // 如果仍然为null，返回0
        if (docRemaining == null) {
            return BigDecimal.ZERO;
        }

        return docRemaining.min(invRemaining);
    }
    
    // 其他辅助方法...
    
    /**
     * 匹配候选类
     */
    private static class MatchCandidate {
        private DpFpxxMx invoiceDetail;
        private double score;
        
        // getters and setters
        public DpFpxxMx getInvoiceDetail() { return invoiceDetail; }
        public void setInvoiceDetail(DpFpxxMx invoiceDetail) { this.invoiceDetail = invoiceDetail; }
        public double getScore() { return score; }
        public void setScore(double score) { this.score = score; }
    }
    
    /**
     * 匹配结果类
     */
    private static class MatchResult {
        private YwDjmx documentDetail;
        private DpFpxxMx invoiceDetail;
        private BigDecimal matchQuantity;
        private double matchScore;
        private String matchType;
        
        // getters and setters
        public YwDjmx getDocumentDetail() { return documentDetail; }
        public void setDocumentDetail(YwDjmx documentDetail) { this.documentDetail = documentDetail; }
        public DpFpxxMx getInvoiceDetail() { return invoiceDetail; }
        public void setInvoiceDetail(DpFpxxMx invoiceDetail) { this.invoiceDetail = invoiceDetail; }
        public BigDecimal getMatchQuantity() { return matchQuantity; }
        public void setMatchQuantity(BigDecimal matchQuantity) { this.matchQuantity = matchQuantity; }
        public double getMatchScore() { return matchScore; }
        public void setMatchScore(double matchScore) { this.matchScore = matchScore; }
        public String getMatchType() { return matchType; }
        public void setMatchType(String matchType) { this.matchType = matchType; }
    }
    
    // 辅助方法
    private void updateTaskStatus(String taskId, String status, String errorMsg) {
        PpRw task = new PpRw();
        task.setId(taskId);
        task.setRwZt(status);
        task.setErrorMsg(errorMsg);
        task.setUpdateBy(ShiroUtils.getLoginName());
        task.setUpdateTime(DateUtils.getNowDate());
        ppRwMapper.updatePpRw(task);
        
        // 更新缓存
        MatchProgressDto progress = taskProgressCache.get(taskId);
        if (progress != null) {
            progress.setStatus(status);
            progress.setStatusDesc(getStatusDescription(status));
            progress.setErrorMsg(errorMsg);
        }
    }
    
    private void updateProgress(String taskId, int processed, int success, int failed, int total) {
        MatchProgressDto progress = taskProgressCache.get(taskId);
        if (progress != null) {
            progress.setSuccessCount(success);
            progress.setFailedCount(failed);
            progress.setProgress(calculateProgress(success, failed, total));
            progress.setStatusDesc(String.format("已处理 %d/%d", processed, total));
        }
    }
    
    private int calculateProgress(int success, int failed, int total) {
        if (total == 0) return 0;
        return Math.round((float)(success + failed) / total * 100);
    }
    
    private String getStatusDescription(String status) {
        switch (status) {
            case "PENDING": return "等待执行";
            case "RUNNING": return "执行中";
            case "SUCCESS": return "执行成功";
            case "FAILED": return "执行失败";
            case "CANCELLED": return "已取消";
            default: return "未知状态";
        }
    }
    
    /**
     * 保存匹配结果
     */
    @Transactional(rollbackFor = Exception.class)
    private void saveMatchResults(String djId, List<MatchResult> matchResults) {
        try {
            for (MatchResult result : matchResults) {
                String djmxId = result.getDocumentDetail().getId();
                String fpmxId = result.getInvoiceDetail().getId();

                // 检查是否已存在相同的匹配记录
                YwDjFpPp existingMatch = ywDjFpPpMapper.selectByDjmxAndFpmx(djmxId, fpmxId);
                if (existingMatch != null) {
                    log.warn("匹配记录已存在，跳过保存 - 单据明细ID: {}, 发票明细ID: {}", djmxId, fpmxId);
                    continue;
                }

                // 创建匹配关系记录
                YwDjFpPp matchRelation = new YwDjFpPp();
                matchRelation.setId(IdUtils.fastSimpleUUID());
                matchRelation.setDjId(djId);
                matchRelation.setDjmxId(djmxId);
                matchRelation.setFpId(result.getInvoiceDetail().getFpid());
                matchRelation.setFpmxId(fpmxId);
                matchRelation.setPpSl(result.getMatchQuantity());

                // 计算匹配金额和税额
                BigDecimal matchAmount = result.getMatchQuantity().multiply(result.getInvoiceDetail().getDj());
                BigDecimal matchTax = matchAmount.multiply(result.getInvoiceDetail().getSl());
                matchRelation.setPpJe(matchAmount);
                matchRelation.setPpSe(matchTax);

                matchRelation.setPpDf(new BigDecimal(result.getMatchScore()));
                matchRelation.setPpLx(result.getMatchType());
                matchRelation.setPpZt("1"); // 有效
                matchRelation.setPpRq(DateUtils.getNowDate());
                matchRelation.setPpRy(ShiroUtils.getLoginName());
                matchRelation.setHcZt("0"); // 未红冲
                matchRelation.setCreateBy(ShiroUtils.getLoginName());
                matchRelation.setCreateTime(DateUtils.getNowDate());

                // 记录匹配结果日志
                log.info("保存匹配关系 - 单据明细ID：{}，电票明细ID：{}，匹配类型：{}，匹配得分：{}%，匹配数量：{}，匹配金额：{}",
                        djmxId, fpmxId, result.getMatchType(),
                        (result.getMatchScore() * 100), result.getMatchQuantity(), matchAmount);

                // 保存匹配关系
                ywDjFpPpMapper.insertYwDjFpPp(matchRelation);

                // 更新单据明细剩余数量
                updateDocumentDetailRemaining(result.getDocumentDetail(), result.getMatchQuantity());

                // 更新电票明细剩余数量
                updateInvoiceDetailRemaining(result.getInvoiceDetail(), result.getMatchQuantity());
            }

        } catch (Exception e) {
            log.error("保存匹配结果失败，单据ID：{}", djId, e);
            throw new RuntimeException("保存匹配结果失败", e);
        }
    }

    /**
     * 更新单据匹配状态
     */
    private void updateDocumentMatchStatus(String djId) {
        try {
            // 使用状态管理服务更新单据匹配状态
            matchStatusService.updateDocumentMatchStatus(djId);
            log.debug("单据匹配状态更新完成，单据ID：{}", djId);
        } catch (Exception e) {
            log.error("更新单据匹配状态失败，单据ID：{}", djId, e);
            throw new RuntimeException("更新单据匹配状态失败", e);
        }
    }

    /**
     * 更新发票匹配状态
     */
    private void updateInvoiceMatchStatus(String fpId) {
        try {
            // 使用状态管理服务更新发票匹配状态
            matchStatusService.updateInvoiceMatchStatus(fpId);
            log.debug("发票匹配状态更新完成，发票ID：{}", fpId);
        } catch (Exception e) {
            log.error("更新发票匹配状态失败，发票ID：{}", fpId, e);
            throw new RuntimeException("更新发票匹配状态失败", e);
        }
    }

    /**
     * 生成发票申请单
     */
    private void generateInvoiceApplication(String djId, YwDjxx document, List<MatchResult> matchResults) {
        try {
            // 创建发票申请单主表
            YwFpsqd fpsqd = new YwFpsqd();
            fpsqd.setSwguid(IdUtils.fastSimpleUUID());
            fpsqd.setSwfpdate(DateUtils.getDate()); // 申请日期
            fpsqd.setSwcsflag(0L); // 重试标志
            fpsqd.setQyid(document.getGmfNsrsbh()); // 企业ID（购买方纳税人识别号）
            fpsqd.setYwdjdm(document.getDjlx()); // 业务单据代码
            fpsqd.setSfdj(1L); // 是否单据
            fpsqd.setDjbh(document.getDjbh()); // 单据编号
            fpsqd.setFplxdm("01"); // 发票类型代码（增值税专用发票）
            fpsqd.setKplx(1L); // 开票类型
            fpsqd.setKpzt(0L); // 开票状态（待开票）
            fpsqd.setCreate_by(ShiroUtils.getLoginName());
            fpsqd.setCreate_time(DateUtils.getNowDate());

            // 创建发票申请单明细列表
            List<YwFpsqdmx> fpsqdmxList = new ArrayList<>();

            for (MatchResult result : matchResults) {
                YwFpsqdmx fpsqdmx = new YwFpsqdmx();
                fpsqdmx.setSwguid(IdUtils.fastSimpleUUID());
                fpsqdmx.setSwmainguid(fpsqd.getSwguid()); // 主表ID

                // 从单据明细获取商品信息
                YwDjmx docDetail = result.getDocumentDetail();
                fpsqdmx.setSpmc(docDetail.getSpMc()); // 商品名称
                fpsqdmx.setSpbm(docDetail.getSpBm()); // 商品编码
                fpsqdmx.setGgxh(docDetail.getGgxh()); // 规格型号
                fpsqdmx.setDw(docDetail.getDw()); // 单位
                fpsqdmx.setSl(result.getMatchQuantity().longValue()); // 数量（匹配数量）
                fpsqdmx.setDj(docDetail.getDj()); // 单价

                // 计算金额和税额
                BigDecimal amount = docDetail.getDj().multiply(result.getMatchQuantity());
                BigDecimal taxRate = docDetail.getSlv();
                BigDecimal tax = amount.multiply(taxRate);

                fpsqdmx.setJe(amount); // 金额
                fpsqdmx.setSlv(taxRate); // 税率
                fpsqdmx.setSe(tax); // 税额

                fpsqdmx.setCreate_by(ShiroUtils.getLoginName());
                fpsqdmx.setCreate_time(DateUtils.getNowDate());

                fpsqdmxList.add(fpsqdmx);
            }

            // 设置明细列表
            fpsqd.setYwFpsqdmxList(fpsqdmxList);

            // 保存发票申请单
            ywFpsqdService.insertYwFpsqd(fpsqd);

        } catch (Exception e) {
            log.error("生成发票申请单失败，单据ID：{}", djId, e);
            throw new RuntimeException("生成发票申请单失败", e);
        }
    }

    /**
     * 更新单据明细剩余数量
     */
    private void updateDocumentDetailRemaining(YwDjmx documentDetail, BigDecimal matchQuantity) {
        // 获取当前剩余数量，如果为null则使用原始数量
        BigDecimal currentRemaining = documentDetail.getSySl();
        if (currentRemaining == null) {
            currentRemaining = documentDetail.getSl();
        }

        // 如果仍然为null，跳过更新
        if (currentRemaining == null) {
            log.warn("单据明细数量为null，跳过剩余数量更新 - 明细ID: {}", documentDetail.getId());
            return;
        }

        BigDecimal newRemaining = currentRemaining.subtract(matchQuantity);
        BigDecimal unitPrice = documentDetail.getDj();
        BigDecimal taxRate = documentDetail.getSlv();

        // 计算剩余金额和税额
        BigDecimal remainingAmount = newRemaining.multiply(unitPrice);
        BigDecimal remainingTax = remainingAmount.multiply(taxRate);

        YwDjmx updateDetail = new YwDjmx();
        updateDetail.setId(documentDetail.getId());
        updateDetail.setSySl(newRemaining);
        updateDetail.setSyJe(remainingAmount);
        updateDetail.setSySe(remainingTax);
        // 处理匹配次数，如果为null则从0开始
        Long currentPpCs = documentDetail.getPpCs();
        updateDetail.setPpCs(currentPpCs == null ? 1L : currentPpCs + 1L);
        updateDetail.setLastPpTime(DateUtils.getNowDate());

        // 更新匹配状态
        if (newRemaining.compareTo(BigDecimal.ZERO) == 0) {
            updateDetail.setPpZt("2"); // 完全匹配
        } else {
            updateDetail.setPpZt("1"); // 部分匹配
        }

        updateDetail.setUpdateBy(ShiroUtils.getLoginName());
        updateDetail.setUpdateTime(DateUtils.getNowDate());

        ywDjxxMapper.updateYwDjmx(updateDetail);
    }

    /**
     * 更新电票明细剩余数量
     */
    private void updateInvoiceDetailRemaining(DpFpxxMx invoiceDetail, BigDecimal matchQuantity) {
        DpFpxxMxPp matchStatus = dpFpxxMxPpMapper.selectByFpmxId(invoiceDetail.getId());
        if (matchStatus != null) {
            BigDecimal currentRemaining = matchStatus.getSySl();

            // 如果剩余数量为null，跳过更新
            if (currentRemaining == null) {
                log.warn("电票明细剩余数量为null，跳过剩余数量更新 - 明细ID: {}", invoiceDetail.getId());
                return;
            }

            BigDecimal newRemaining = currentRemaining.subtract(matchQuantity);
            BigDecimal unitPrice = invoiceDetail.getDj();
            BigDecimal taxRate = invoiceDetail.getSl();

            // 计算剩余金额和税额
            BigDecimal remainingAmount = newRemaining.multiply(unitPrice);
            BigDecimal remainingTax = remainingAmount.multiply(taxRate);

            matchStatus.setSySl(newRemaining);
            matchStatus.setSyJe(remainingAmount);
            matchStatus.setSySe(remainingTax);
            // 处理匹配次数，如果为null则从0开始
            Integer currentPpCs = matchStatus.getPpCs();
            matchStatus.setPpCs(currentPpCs == null ? 1 : currentPpCs + 1);
            matchStatus.setLastPpTime(DateUtils.getNowDate());

            // 更新匹配状态
            if (newRemaining.compareTo(BigDecimal.ZERO) == 0) {
                matchStatus.setPpZt("2"); // 完全匹配
            } else {
                matchStatus.setPpZt("1"); // 部分匹配
            }

            matchStatus.setUpdateTime(DateUtils.getNowDate());

            dpFpxxMxPpMapper.updateDpFpxxMxPp(matchStatus);
        }
    }

}

