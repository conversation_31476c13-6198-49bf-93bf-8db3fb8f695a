<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改发票申请单信息')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-fpsqd-edit" th:object="${ywFpsqd}">
            <h4 class="form-header h4">发票申请单信息信息</h4>
            <input name="swguid" th:field="*{swguid}" type="hidden">
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">申请日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="swfpdate" th:field="*{swfpdate}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">重试标志：</label>
                    <div class="col-sm-8">
                        <input name="swcsflag" th:field="*{swcsflag}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">企业ID：</label>
                    <div class="col-sm-8">
                        <input name="qyid" th:field="*{qyid}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">业务单据代码：</label>
                    <div class="col-sm-8">
                        <input name="ywdjdm" th:field="*{ywdjdm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">是否单据：</label>
                    <div class="col-sm-8">
                        <input name="sfdj" th:field="*{sfdj}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">单据编号：</label>
                    <div class="col-sm-8">
                        <input name="djbh" th:field="*{djbh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">发票类型代码：</label>
                    <div class="col-sm-8">
                        <input name="fplxdm" th:field="*{fplxdm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">开票类型：</label>
                    <div class="col-sm-8">
                        <select name="kplx" class="form-control" th:with="type=${@dict.getType('swgx_kplx')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{kplx}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">开票状态：</label>
                    <div class="col-sm-8">
                        <select name="kpzt" class="form-control" th:with="type=${@dict.getType('swgx_fpzt')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{kpzt}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">发票代码：</label>
                    <div class="col-sm-8">
                        <input name="fpdm" th:field="*{fpdm}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">发票号码：</label>
                    <div class="col-sm-8">
                        <input name="fphm" th:field="*{fphm}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">开票日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="kprq" th:field="*{kprq}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">特殊票种：</label>
                    <div class="col-sm-8">
                        <input name="tspz" th:field="*{tspz}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">征收方式：</label>
                    <div class="col-sm-8">
                        <input name="zsfs" th:field="*{zsfs}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">清单标志：</label>
                    <div class="col-sm-8">
                        <input name="qdbz" th:field="*{qdbz}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">销方名称：</label>
                    <div class="col-sm-8">
                        <input name="xsfmc" th:field="*{xsfmc}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">销方税号：</label>
                    <div class="col-sm-8">
                        <input name="xsfnsrsbh" th:field="*{xsfnsrsbh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">销方地址：</label>
                    <div class="col-sm-8">
                        <input name="xsfdz" th:field="*{xsfdz}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">销方电话：</label>
                    <div class="col-sm-8">
                        <input name="xsfdh" th:field="*{xsfdh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">销方银行：</label>
                    <div class="col-sm-8">
                        <input name="xsfyh" th:field="*{xsfyh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">销方账号：</label>
                    <div class="col-sm-8">
                        <input name="xsfzh" th:field="*{xsfzh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购方名称：</label>
                    <div class="col-sm-8">
                        <input name="gmfmc" th:field="*{gmfmc}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购方税号：</label>
                    <div class="col-sm-8">
                        <input name="gmfnsrsbh" th:field="*{gmfnsrsbh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购方地址：</label>
                    <div class="col-sm-8">
                        <input name="gmfdz" th:field="*{gmfdz}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购方电话：</label>
                    <div class="col-sm-8">
                        <input name="gmfdh" th:field="*{gmfdh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购方银行：</label>
                    <div class="col-sm-8">
                        <input name="gmfyh" th:field="*{gmfyh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购方账号：</label>
                    <div class="col-sm-8">
                        <input name="gmfzh" th:field="*{gmfzh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购方手机：</label>
                    <div class="col-sm-8">
                        <input name="gmfmobile" th:field="*{gmfmobile}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购方邮箱：</label>
                    <div class="col-sm-8">
                        <input name="gmfemail" th:field="*{gmfemail}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">开票人：</label>
                    <div class="col-sm-8">
                        <input name="kpr" th:field="*{kpr}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">收款人：</label>
                    <div class="col-sm-8">
                        <input name="skr" th:field="*{skr}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">复核人：</label>
                    <div class="col-sm-8">
                        <input name="fhr" th:field="*{fhr}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">申请人：</label>
                    <div class="col-sm-8">
                        <input name="sqr" th:field="*{sqr}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">价税合计：</label>
                    <div class="col-sm-8">
                        <input name="jshj" th:field="*{jshj}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">合计金额：</label>
                    <div class="col-sm-8">
                        <input name="hjje" th:field="*{hjje}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">合计税额：</label>
                    <div class="col-sm-8">
                        <input name="hjse" th:field="*{hjse}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="bz" class="form-control">[[*{bz}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">附加要素：</label>
                    <div class="col-sm-8">
                        <input name="fjys" th:field="*{fjys}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">原发票代码：</label>
                    <div class="col-sm-8">
                        <input name="yfpdm" th:field="*{yfpdm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">原发票号码：</label>
                    <div class="col-sm-8">
                        <input name="yfphm" th:field="*{yfphm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">库存额：</label>
                    <div class="col-sm-8">
                        <input name="kce" th:field="*{kce}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">税收来源：</label>
                    <div class="col-sm-8">
                        <input name="sslkjly" th:field="*{sslkjly}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">预开票日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="ykprq" th:field="*{ykprq}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">原发票类型代码：</label>
                    <div class="col-sm-8">
                        <input name="yfplxdm" th:field="*{yfplxdm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">冲红原因代码：</label>
                    <div class="col-sm-8">
                        <input name="chyydm" th:field="*{chyydm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">审核标志：</label>
                    <div class="col-sm-8">
                        <input name="checkaudit" th:field="*{checkaudit}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">代理账号：</label>
                    <div class="col-sm-8">
                        <input name="dlzh" th:field="*{dlzh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">证件号码：</label>
                    <div class="col-sm-8">
                        <input name="zjhm" th:field="*{zjhm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">是否为增值税发票：</label>
                    <div class="col-sm-8">
                        <input name="sfwzzfp" th:field="*{sfwzzfp}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">纸票发票凭证代码：</label>
                    <div class="col-sm-8">
                        <input name="zpfppzdm" th:field="*{zpfppzdm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">购买方自然人标识：</label>
                    <div class="col-sm-8">
                        <input name="gmfzrrbs" th:field="*{gmfzrrbs}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">商品分类确认：</label>
                    <div class="col-sm-8">
                        <input name="spflxconfirm" th:field="*{spflxconfirm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">是否展示购买方银行账号：</label>
                    <div class="col-sm-8">
                        <input name="sfzsgmfyhzh" th:field="*{sfzsgmfyhzh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">是否展示销售方银行账号：</label>
                    <div class="col-sm-8">
                        <input name="sfzsxsfyhzh" th:field="*{sfzsxsfyhzh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">是否展示购买方地址电话：</label>
                    <div class="col-sm-8">
                        <input name="sfzsgmfdzdh" th:field="*{sfzsgmfdzdh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">是否展示销售方地址电话：</label>
                    <div class="col-sm-8">
                        <input name="sfzsxsfdzdh" th:field="*{sfzsxsfdzdh}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">农产品收购增值税类型：</label>
                    <div class="col-sm-8">
                        <input name="ncpsgzjlx" th:field="*{ncpsgzjlx}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">出口退税类型代码：</label>
                    <div class="col-sm-8">
                        <input name="cktslxdm" th:field="*{cktslxdm}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">云端数据：</label>
                    <div class="col-sm-8">
                        <textarea name="clouddata" class="form-control">[[*{clouddata}]]</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">扩展字段1：</label>
                    <div class="col-sm-8">
                        <input name="kz" th:field="*{kz}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">扩展字段2：</label>
                    <div class="col-sm-8">
                        <input name="kz1" th:field="*{kz1}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">错误信息：</label>
                    <div class="col-sm-8">
                        <textarea name="errmsg" class="form-control">[[*{errmsg}]]</textarea>
                    </div>
                </div>
            </div>
            <h4 class="form-header h4">发票申请单明细信息</h4>
            <div class="row">
                <div class="col-sm-12">
                    <button type="button" class="btn btn-white btn-sm" onclick="addRow()"><i class="fa fa-plus"> 增加</i></button>
                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delRow()"><i class="fa fa-minus"> 删除</i></button>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "swgx/fpsqd";
        $("#form-fpsqd-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-fpsqd-edit').serialize());
            }
        }

        $("input[name='swfpdate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='kprq']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='ykprq']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $(function() {
            var options = {
                data: [[${ywFpsqd.ywFpsqdmxList}]],
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                    	var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                    	return columnIndex + $.table.serialNumber(index);
                    }
                },


                {
                    field: 'swoutdetailno',
                    align: 'center',
                    title: '外部明细编号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].swoutdetailno' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'swcsflag',
                    align: 'center',
                    title: '重试标志',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].swcsflag' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'djbh',
                    align: 'center',
                    title: '单据编号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].djbh' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'xh',
                    align: 'center',
                    title: '序号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].xh' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'lzmxxh',
                    align: 'center',
                    title: '绿证明细序号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].lzmxxh' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'fpxzh',
                    align: 'center',
                    title: '发票行性质',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].fpxzh' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'hsbz',
                    align: 'center',
                    title: '含税标志',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].hsbz' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'spmc',
                    align: 'center',
                    title: '商品名称',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].spmc' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'spbm',
                    align: 'center',
                    title: '商品编码',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].spbm' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'ggxh',
                    align: 'center',
                    title: '规格型号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].ggxh' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'dw',
                    align: 'center',
                    title: '单位',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].dw' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'dj',
                    align: 'center',
                    title: '单价',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].dj' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'sl',
                    align: 'center',
                    title: '数量',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].sl' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'je',
                    align: 'center',
                    title: '金额',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].je' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'se',
                    align: 'center',
                    title: '税额',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].se' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'slv',
                    align: 'center',
                    title: '税率',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].slv' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'lslbs',
                    align: 'center',
                    title: '零税率标识',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].lslbs' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'yhzcbs',
                    align: 'center',
                    title: '优惠政策标识',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].yhzcbs' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'zxbm',
                    align: 'center',
                    title: '自行编码',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].zxbm' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'zzstsgl',
                    align: 'center',
                    title: '增值税特殊管理',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].zzstsgl' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'bdcdz',
                    align: 'center',
                    title: '不动产地址',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].bdcdz' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'fulladdress',
                    align: 'center',
                    title: '完整地址',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].fulladdress' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'zlqqz',
                    align: 'center',
                    title: '质量期限值',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].zlqqz' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'kdsbz',
                    align: 'center',
                    title: '扣除税标志',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].kdsbz' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'cqzsh',
                    align: 'center',
                    title: '产权证书号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].cqzsh' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'mjdw',
                    align: 'center',
                    title: '面积单位',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].mjdw' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'wqhtbabh',
                    align: 'center',
                    title: '委托合同备案编号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].wqhtbabh' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'tdzzsxmbh',
                    align: 'center',
                    title: '土地增值税项目编号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].tdzzsxmbh' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'hdjsjg',
                    align: 'center',
                    title: '核定计税价格',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].hdjsjg' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'sjcjhsje',
                    align: 'center',
                    title: '实际成交含税金额',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].sjcjhsje' value='%s'>", index, value);
                        return html;
                    }
                },

                {
                    field: 'cph',
                    align: 'center',
                    title: '车牌号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywFpsqdmxList[%s].cph' value='%s'>", index, value);
                        return html;
                    }
                },



                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                        return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + value + '\')"><i class="fa fa-remove"></i>删除</a>';
                    }
                }]
            };
            $.table.init(options);
        });

        function addRow() {
            var count = $("#" + table.options.id).bootstrapTable('getData').length;
            var row = {
                index: $.table.serialNumber(count),

                swoutdetailno: "",
                swcsflag: "",
                djbh: "",
                xh: "",
                lzmxxh: "",
                fpxzh: "",
                hsbz: "",
                spmc: "",
                spbm: "",
                ggxh: "",
                dw: "",
                dj: "",
                sl: "",
                je: "",
                se: "",
                slv: "",
                lslbs: "",
                yhzcbs: "",
                zxbm: "",
                zzstsgl: "",
                bdcdz: "",
                fulladdress: "",
                zlqqz: "",
                kdsbz: "",
                cqzsh: "",
                mjdw: "",
                wqhtbabh: "",
                tdzzsxmbh: "",
                hdjsjg: "",
                sjcjhsje: "",
                cph: "",

            }
            sub.addRow(row);
        }
    </script>
</body>
</html>