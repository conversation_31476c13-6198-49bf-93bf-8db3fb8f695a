package com.ruoyi.swgx.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * SWGX模块Web配置
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Configuration
public class SwgxWebConfig implements WebMvcConfigurer {

    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置swgx模块的静态资源
        registry.addResourceHandler("/swgx/**")
                .addResourceLocations("classpath:/static/swgx/")
                .setCachePeriod(3600);
    }
}
