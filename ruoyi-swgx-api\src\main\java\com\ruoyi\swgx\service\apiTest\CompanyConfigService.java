package com.ruoyi.swgx.service.apiTest;

import com.ruoyi.swgx.common.config.SwgxApiProperties;
import com.ruoyi.swgx.util.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

/**
 * 企业配置服务
 * 统一管理企业唯一标识，支持配置直接设置或自动获取
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class CompanyConfigService {

    @Autowired
    private SwgxApiProperties properties;

    // 移除了CompanyService依赖和缓存字段

    @PostConstruct
    public void init() {
        if (properties == null) {
            log.error("SwgxApiProperties未正确注入");
            return;
        }

        SwgxApiProperties.CompanyConfig config = properties.getCompany();
        log.info("企业配置初始化: name={}, taxNumber={}, uniqueId={}",
                config.getName(),
                LogUtils.maskSensitiveData(config.getTaxNumber()),
                LogUtils.maskSensitiveData(config.getUniqueId()));
    }

    /**
     * 获取企业唯一标识
     * 直接从配置中获取，不支持缓存和自动获取
     *
     * @return 企业唯一标识
     */
    public String getCompanyUniqueId() {
        SwgxApiProperties.CompanyConfig config = properties.getCompany();

        // 直接使用配置中的企业唯一标识
        if (StringUtils.hasText(config.getUniqueId())) {
            log.debug("使用配置中的企业唯一标识: {}", LogUtils.maskSensitiveData(config.getUniqueId()));
            return config.getUniqueId();
        }

        throw new IllegalStateException("企业唯一标识未配置，请设置 swgx.api.company.unique-id");
    }

    /**
     * 获取企业名称
     * 
     * @return 企业名称
     */
    public String getCompanyName() {
        return properties.getCompany().getName();
    }

    /**
     * 获取纳税人识别号
     * 
     * @return 纳税人识别号
     */
    public String getCompanyTaxNumber() {
        String taxNumber = properties.getCompany().getTaxNumber();
        if (!StringUtils.hasText(taxNumber)) {
            throw new IllegalStateException("纳税人识别号未配置，请设置 swgx.api.company.tax-number");
        }
        return taxNumber;
    }

    /**
     * 获取完整的企业信息
     * 
     * @return 企业信息
     */
    public CompanyInfo getCompanyInfo() {
        SwgxApiProperties.CompanyConfig config = properties.getCompany();
        
        CompanyInfo info = new CompanyInfo();
        info.setName(config.getName());
        info.setTaxNumber(getCompanyTaxNumber());
        info.setUniqueId(getCompanyUniqueId());
        
        return info;
    }

    // 移除了刷新缓存方法

    /**
     * 验证企业配置是否完整
     *
     * @return 验证结果
     */
    public boolean validateCompanyConfig() {
        SwgxApiProperties.CompanyConfig config = properties.getCompany();

        // 检查企业名称
        if (!StringUtils.hasText(config.getName())) {
            log.error("企业名称未配置");
            return false;
        }

        // 检查纳税人识别号
        if (!StringUtils.hasText(config.getTaxNumber())) {
            log.error("纳税人识别号未配置");
            return false;
        }

        // 检查企业唯一标识
        if (!StringUtils.hasText(config.getUniqueId())) {
            log.error("企业唯一标识未配置");
            return false;
        }

        return true;
    }

    // 移除了自动获取企业唯一标识的方法

    /**
     * 企业信息
     */
    public static class CompanyInfo {
        private String name;
        private String taxNumber;
        private String uniqueId;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getTaxNumber() { return taxNumber; }
        public void setTaxNumber(String taxNumber) { this.taxNumber = taxNumber; }
        
        public String getUniqueId() { return uniqueId; }
        public void setUniqueId(String uniqueId) { this.uniqueId = uniqueId; }
        
        @Override
        public String toString() {
            return "CompanyInfo{" +
                    "name='" + name + '\'' +
                    ", taxNumber='" + LogUtils.maskSensitiveData(taxNumber) + '\'' +
                    ", uniqueId='" + LogUtils.maskSensitiveData(uniqueId) + '\'' +
                    '}';
        }
    }
}
