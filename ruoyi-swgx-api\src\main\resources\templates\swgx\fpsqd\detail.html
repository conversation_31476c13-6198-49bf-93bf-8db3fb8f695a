<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发票申请单详情')" />
    
    <style>
        .form-header { margin-top: 20px; margin-bottom: 15px; border-bottom: 1px solid #e7eaec; padding-bottom: 5px; }
        .detail-container { padding: 15px; }
        .price-highlight { font-weight: bold; color: #d9534f; }
        .form-group { margin-bottom: 8px; }
        .row { margin-bottom: 5px; }
        .form-control-static { padding-top: 3px; padding-bottom: 3px; margin-bottom: 0; line-height: 1.3; }
        .compact-section .form-group { margin-bottom: 3px; }
        .compact-section .row { margin-bottom: 0px; }
        .empty-value { color: #999; font-style: italic; }
    </style>
</head>
<body class="white-bg">
    <div class="detail-container">
        <form class="form-horizontal m" id="form-fpsqd-detail">
            <input name="swguid" th:value="${ywFpsqd.swguid}" type="hidden">
            
            <!-- 基本信息 -->
            <h4 class="form-header h4">发票申请单基本信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">申请单ID：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.swguid}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">申请日期：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.swfpdate}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">企业ID：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.qyid}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">单据编号：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.djbh}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 发票信息 -->
            <h4 class="form-header h4">发票信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">发票代码：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.fpdm}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">发票号码：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.fphm}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">开票日期：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.kprq}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">开票类型：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${@dict.getLabel('swgx_kplx', ywFpsqd.kplx)}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 销方信息 -->
            <h4 class="form-header h4">销方信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">销方名称：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${ywFpsqd.xsfmc}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">纳税人识别号：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.xsfnsrsbh}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">联系电话：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.xsfdh != null and ywFpsqd.xsfdh != '' ? ywFpsqd.xsfdh : '未填写'}" th:class="${ywFpsqd.xsfdh == null or ywFpsqd.xsfdh == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 购方信息 -->
            <h4 class="form-header h4">购方信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">购方名称：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${ywFpsqd.gmfmc}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">纳税人识别号：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.gmfnsrsbh}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">联系电话：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywFpsqd.gmfdh != null and ywFpsqd.gmfdh != '' ? ywFpsqd.gmfdh : '未填写'}" th:class="${ywFpsqd.gmfdh == null or ywFpsqd.gmfdh == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 金额信息 -->
            <h4 class="form-header h4">金额信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">合计金额：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right" th:text="${#numbers.formatDecimal(ywFpsqd.hjje, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">合计税额：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right" th:text="${#numbers.formatDecimal(ywFpsqd.hjse, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">价税合计：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right price-highlight" th:text="${#numbers.formatDecimal(ywFpsqd.jshj, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 其他信息 -->
            <h4 class="form-header h4">其他信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${ywFpsqd.bz != null and ywFpsqd.bz != '' ? ywFpsqd.bz : '无'}" th:class="${ywFpsqd.bz == null or ywFpsqd.bz == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 发票申请单明细 -->
            <h4 class="form-header h4">发票申请单明细信息</h4>
            <div class="row">
                <div class="col-sm-12">
                    <div th:if="${ywFpsqd.ywFpsqdmxList != null and !ywFpsqd.ywFpsqdmxList.isEmpty()}">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>商品名称</th>
                                    <th>商品编码</th>
                                    <th>规格型号</th>
                                    <th>单位</th>
                                    <th>数量</th>
                                    <th>单价</th>
                                    <th>金额</th>
                                    <th>税率</th>
                                    <th>税额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="item : ${ywFpsqd.ywFpsqdmxList}">
                                    <td th:text="${item.xh}"></td>
                                    <td th:text="${item.spmc}"></td>
                                    <td th:text="${item.spbm}"></td>
                                    <td th:text="${item.ggxh}"></td>
                                    <td th:text="${item.dw}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.sl, 1, 6)}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.dj, 1, 6)}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.je, 1, 2)}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.slv, 1, 6)}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.se, 1, 2)}"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div th:if="${ywFpsqd.ywFpsqdmxList == null or ywFpsqd.ywFpsqdmxList.isEmpty()}">
                        <p class="text-muted">暂无明细数据</p>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
