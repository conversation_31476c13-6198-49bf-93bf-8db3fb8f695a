<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('业务单据主列表')" />
    <style>
        /* 操作列按钮样式优化 */
        .btn-group-xs .btn {
            margin-right: 5px;
        }
        .btn-group-xs .btn:last-child {
            margin-right: 0;
        }
        /* 确保操作列内容不换行 */
        .bootstrap-table .fixed-table-body .btn-group-xs {
            white-space: nowrap;
        }
    </style>
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>单据编号：</label>
                                <input type="text" name="djbh"/>
                            </li>
                            <li>
                                <label>单据类型：</label>
                                <select name="djlx" th:with="type=${@dict.getType('swgx_yw_dj_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>单据状态：</label>
                                <select name="djzt" th:with="type=${@dict.getType('swgx_yw_dj_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>匹配状态：</label>
                                <select name="ppzt" th:with="type=${@dict.getType('swgx_yw_pp_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <label>单据日期：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginDjrq]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endDjrq]"/>
                            </li>
                            <li>
                                <label>购方名称：</label>
                                <input type="text" name="gmfMc"/>
                            </li>
                            <li>
                                <label>购方税号：</label>
                                <input type="text" name="gmfNsrsbh"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="swgx:djxx:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="swgx:djxx:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="swgx:djxx:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="swgx:djxx:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info single disabled" onclick="matchInvoice()" shiro:hasPermission="swgx:djxx:match">
                    <i class="fa fa-link"></i> 匹配发票
                </a>

            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('swgx:djxx:edit')}]];
        var removeFlag = [[${@permission.hasPermi('swgx:djxx:remove')}]];
        var djlxDatas = [[${@dict.getType('swgx_yw_dj_type')}]];
        var djztDatas = [[${@dict.getType('swgx_yw_dj_status')}]];
        var ppztDatas = [[${@dict.getType('swgx_yw_pp_status')}]];
        var prefix = ctx + "swgx/djxx";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "业务单据主",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '单据ID',
                    visible: false
                },
                {
                    field: 'djbh',
                    title: '单据编号'
                },
                {
                    field: 'djlx',
                    title: '单据类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(djlxDatas, value);
                    }
                },
                {
                    field: 'djzt',
                    title: '单据状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(djztDatas, value);
                    }
                },
                {
                    field: 'ppzt',
                    title: '匹配状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(ppztDatas, value);
                    }
                },
                {
                    field: 'djrq',
                    title: '单据日期'
                },
                {
                    field: 'gmfMc',
                    title: '购方名称'
                },
                {
                    field: 'gmfNsrsbh',
                    title: '购方税号'
                },
                {
                    field: 'hjje',
                    title: '合计金额'
                },
                {
                    field: 'syHjje',
                    title: '剩余合计金额'
                },
                {
                    field: 'ywxtId',
                    title: '业务系统单据ID'
                },
                {
                    field: 'ywxtMc',
                    title: '业务系统名称'
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 320,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a>');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="detail(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a>');
                        // 只有已匹配的单据才显示匹配结果按钮
                        if (row.ppzt && (row.ppzt === '1' || row.ppzt === '2')) {
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="showMatchResults(\'' + row.id + '\')"><i class="fa fa-link"></i>匹配结果</a>');
                        }
                        return '<div class="btn-group-xs" style="white-space: nowrap;">' + actions.join(' ') + '</div>';
                    }
                }]
            };
            $.table.init(options);
        });

        /* 详情-详情 */
        function detail(id) {
            var url = prefix + '/detail/' + id;
            $.modal.open("业务单据详情", url, '1100', '600');
        }

        /**
         * 匹配发票功能
         */
        function matchInvoice() {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            // 显示匹配配置对话框
            showMatchConfigDialog(rows);
        }

        /**
         * 显示匹配配置对话框
         */
        function showMatchConfigDialog(djIds) {
            var content = '<div class="form-horizontal">' +
                '<div class="form-group">' +
                    '<label class="col-sm-3 control-label">匹配类型：</label>' +
                    '<div class="col-sm-8">' +
                        '<select id="matchType" class="form-control">' +
                            '<option value="SINGLE">单个匹配</option>' +
                            '<option value="BATCH">批量匹配</option>' +
                            '<option value="MERGE">合并匹配</option>' +
                        '</select>' +
                    '</div>' +
                '</div>' +
                '<div class="form-group">' +
                    '<label class="col-sm-3 control-label">匹配规则：</label>' +
                    '<div class="col-sm-8">' +
                        '<div class="checkbox">' +
                            '<label><input type="checkbox" name="matchRules" value="EXACT" checked> 精确匹配</label>' +
                        '</div>' +
                        '<div class="checkbox">' +
                            '<label><input type="checkbox" name="matchRules" value="FUZZY"> 模糊匹配</label>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
                '<div class="form-group">' +
                    '<label class="col-sm-3 control-label">匹配字段：</label>' +
                    '<div class="col-sm-8">' +
                        '<div class="checkbox">' +
                            '<label><input type="checkbox" name="matchFields" value="spmc" checked disabled> 商品名称 <span class="text-danger">*</span></label>' +
                        '</div>' +
                        '<div class="checkbox">' +
                            '<label><input type="checkbox" name="matchFields" value="dj" checked disabled> 单价 <span class="text-danger">*</span></label>' +
                        '</div>' +
                        '<div class="checkbox">' +
                            '<label><input type="checkbox" name="matchFields" value="slv" checked disabled> 税率 <span class="text-danger">*</span></label>' +
                        '</div>' +
                        '<div class="checkbox">' +
                            '<label><input type="checkbox" name="matchFields" value="spbm"> 商品编码</label>' +
                        '</div>' +
                        '<div class="checkbox">' +
                            '<label><input type="checkbox" name="matchFields" value="ggxh"> 规格型号</label>' +
                        '</div>' +
                        '<div class="checkbox">' +
                            '<label><input type="checkbox" name="matchFields" value="dw"> 单位</label>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
                '<div class="form-group">' +
                    '<label class="col-sm-3 control-label">优化策略：</label>' +
                    '<div class="col-sm-8">' +
                        '<select id="optimizeStrategy" class="form-control">' +
                            '<option value="MIN_INVOICES">最少发票数量</option>' +
                            '<option value="MAX_MATCH_RATE">最大匹配率</option>' +
                            '<option value="BALANCE">平衡策略</option>' +
                        '</select>' +
                    '</div>' +
                '</div>' +
            '</div>';

            var index = layer.open({
                title: "匹配发票配置",
                type: 1,
                area: ['600px', '450px'],
                content: content,
                btn: ['开始匹配', '取消'],
                btn1: function(index) {
                    startMatching(djIds);
                    layer.close(index);
                },
                btn2: function(index) {
                    layer.close(index);
                }
            });
        }

        /**
         * 开始匹配
         */
        function startMatching(djIds) {
            var matchType = $("#matchType").val();
            var matchRules = [];
            // 只选择匹配规则的checkbox，使用更具体的选择器
            $("input[name='matchRules']:checked").each(function() {
                matchRules.push($(this).val());
            });
            var matchFields = [];
            $("input[name='matchFields']:checked").each(function() {
                matchFields.push($(this).val());
            });
            var optimizeStrategy = $("#optimizeStrategy").val();

            var data = {
                djIds: djIds,
                matchType: matchType,
                matchRules: matchRules,
                matchFields: matchFields,
                optimizeStrategy: optimizeStrategy
            };

            $.modal.loading("正在执行匹配，请稍候...");

            $.ajax({
                url: prefix + "/startMatch",
                type: "post",
                dataType: "json",
                data: JSON.stringify(data),
                contentType: "application/json",
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        var taskId = result.taskId || (result.data && result.data.taskId);
                        $.modal.msgSuccess("匹配任务已启动，任务ID：" + taskId);
                        // 显示匹配进度对话框
                        showMatchProgress(taskId);
                        // 刷新表格
                        $.table.refresh();
                    } else {
                        $.modal.msgError(result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.msgError("匹配请求失败");
                }
            });
        }

        /**
         * 显示匹配进度
         */
        function showMatchProgress(taskId) {
            var content = '<div class="progress-container">' +
                '<div class="progress">' +
                    '<div id="matchProgress" class="progress-bar progress-bar-info" role="progressbar" style="width: 0%">' +
                        '0%' +
                    '</div>' +
                '</div>' +
                '<div id="matchStatus" class="text-center" style="margin-top: 10px;">' +
                    '正在初始化匹配任务...' +
                '</div>' +
                '<div id="matchResult" style="margin-top: 15px; display: none;">' +
                    '<table class="table table-bordered">' +
                        '<tr><td>匹配总数：</td><td id="totalCount">0</td></tr>' +
                        '<tr><td>成功匹配：</td><td id="successCount" class="text-success">0</td></tr>' +
                        '<tr><td>失败匹配：</td><td id="failedCount" class="text-danger">0</td></tr>' +
                    '</table>' +
                '</div>' +
            '</div>';

            var index = layer.open({
                title: "匹配进度",
                type: 1,
                area: ['500px', '300px'],
                content: content,
                btn: ['关闭'],
                btn1: function(index) {
                    layer.close(index);
                }
            });

            // 轮询匹配进度
            pollMatchProgress(taskId);
        }

        /**
         * 轮询匹配进度
         */
        function pollMatchProgress(taskId) {
            var timer = setInterval(function() {
                $.ajax({
                    url: prefix + "/getMatchProgress/" + taskId,
                    type: "get",
                    dataType: "json",
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            var data = result.data;
                            var progress = data.totalCount > 0 ? Math.round((data.successCount + data.failedCount) / data.totalCount * 100) : 0;

                            $("#matchProgress").css("width", progress + "%").text(progress + "%");
                            $("#matchStatus").text(data.status);
                            $("#totalCount").text(data.totalCount);
                            $("#successCount").text(data.successCount);
                            $("#failedCount").text(data.failedCount);

                            if (data.status === 'SUCCESS' || data.status === 'FAILED' || data.status === 'CANCELLED') {
                                clearInterval(timer);
                                $("#matchResult").show();
                                $.table.refresh();
                            }
                        }
                    }
                });
            }, 2000);
        }

        /**
         * 显示匹配结果
         */
        function showMatchResults(id) {
            $.modal.loading("正在加载匹配结果...");

            $.ajax({
                url: prefix + "/getMatchResults/" + id,
                type: "get",
                dataType: "json",
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS && result.data.success) {
                        displayMatchResultsDialog(result.data);
                    } else {
                        $.modal.msgError(result.data.message || "获取匹配结果失败");
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.msgError("获取匹配结果失败，请检查网络连接");
                }
            });
        }

        /**
         * 显示匹配结果对话框
         */
        function displayMatchResultsDialog(data) {
            var document = data.document;
            var matchedInvoices = data.matchedInvoices;
            var totalMatched = data.totalMatched;

            // 打开独立的匹配结果页面
            var url = prefix + "/matchResults/" + document.id;
            $.modal.open("匹配结果详情", url, '1400', '800');
        }

        /**
         * 获取匹配状态文本
         */
        function getMatchStatusText(status) {
            switch (status) {
                case '0': return '<span class="label label-default">未匹配</span>';
                case '1': return '<span class="label label-warning">部分匹配</span>';
                case '2': return '<span class="label label-success">完全匹配</span>';
                default: return '<span class="label label-default">未知</span>';
            }
        }

        /**
         * 获取匹配类型文本
         */
        function getMatchTypeText(type) {
            switch (type) {
                case '1': return '<span class="label label-success">精确匹配</span>';
                case '2': return '<span class="label label-info">模糊匹配</span>';
                case '3': return '<span class="label label-warning">手工匹配</span>';
                default: return '<span class="label label-default">未知</span>';
            }
        }

        /**
         * 格式化日期
         */
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            var date = new Date(dateStr);
            if (isNaN(date.getTime())) return '-';
            return date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0');
        }


    </script>
</body>
</html>