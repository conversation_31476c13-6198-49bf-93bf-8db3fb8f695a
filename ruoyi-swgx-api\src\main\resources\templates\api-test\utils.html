<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API工具测试 - 河北九赋</title>
    <link rel="shortcut icon" th:href="@{/img/logo.jpg}">
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/animate.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/style.css}" rel="stylesheet"/>
    <link th:href="@{/css/swgx-test.css}" rel="stylesheet"/>
</head>
<body class="gray-bg">
    <div class="test-container">
        <a href="/swgx/demo/test" class="back-btn">
            <i class="fa fa-arrow-left"></i> 返回测试中心
        </a>

        <div class="test-card animated fadeInDown">
            <div class="test-card-header">
                <h3><i class="fa fa-wrench"></i> API工具与实用功能测试</h3>
                <p class="header-subtitle">测试各种实用工具和辅助功能</p>
            </div>
            <div class="test-card-body">
                <div class="row">
                    <!-- 订单号生成工具 -->
                    <div class="col-md-4">
                        <div class="tool-item text-center">
                            <div class="tool-icon">
                                <i class="fa fa-calculator"></i>
                            </div>
                            <h4>订单号生成</h4>
                            <p class="text-muted">生成唯一的订单流水号</p>
                            <button type="button" class="test-btn" onclick="generateOrderNumber()">
                                <i class="fa fa-play"></i> 生成订单号
                            </button>
                            <div id="orderResult" class="code-block hidden"></div>
                        </div>
                    </div>

                    <!-- 税额计算工具 -->
                    <div class="col-md-4">
                        <div class="tool-item text-center">
                            <div class="tool-icon">
                                <i class="fa fa-money"></i>
                            </div>
                            <h4>税额计算</h4>
                            <p class="text-muted">计算税额和总金额</p>
                            <div class="form-group">
                                <input type="number" class="form-control" id="amount" placeholder="请输入金额">
                            </div>
                            <div class="form-group">
                                <select class="form-control" id="taxRate">
                                    <option value="0.13">13% (一般纳税人)</option>
                                    <option value="0.09">9% (交通运输)</option>
                                    <option value="0.06">6% (现代服务)</option>
                                    <option value="0.03">3% (小规模纳税人)</option>
                                </select>
                            </div>
                            <button type="button" class="test-btn warning" onclick="calculateTax()">
                                <i class="fa fa-calculator"></i> 计算税额
                            </button>
                            <div id="taxResult" class="code-block hidden"></div>
                        </div>
                    </div>

                    <!-- 工具演示 -->
                    <div class="col-md-4">
                        <div class="tool-item text-center">
                            <div class="tool-icon">
                                <i class="fa fa-cogs"></i>
                            </div>
                            <h4>综合演示</h4>
                            <p class="text-muted">调用API获取所有工具演示</p>
                            <button type="button" class="test-btn success" onclick="loadUtilsDemo()">
                                <i class="fa fa-play"></i> 加载演示
                            </button>
                            <div id="utilsResult" class="code-block hidden"></div>
                        </div>
                    </div>
                </div>

                <!-- 结果显示区域 -->
                <div id="resultContainer" class="hidden">
                    <div class="result-card result-success" id="resultCard">
                        <h4><i class="fa fa-check-circle text-success"></i> 工具测试结果</h4>
                        <div id="resultContent"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="test-card animated fadeInUp">
            <div class="test-card-header" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);">
                <h4><i class="fa fa-book"></i> 工具说明</h4>
            </div>
            <div class="test-card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5><i class="fa fa-calculator"></i> 订单号生成器</h5>
                        <ul>
                            <li>基于时间戳生成唯一订单号</li>
                            <li>支持自定义前缀</li>
                            <li>保证全局唯一性</li>
                            <li>格式：前缀+时间戳+随机数</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fa fa-money"></i> 税额计算器</h5>
                        <ul>
                            <li>支持多种税率计算</li>
                            <li>自动计算税额和总金额</li>
                            <li>精确到分的计算</li>
                            <li>符合财务规范</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fa fa-shield"></i> 安全特性</h5>
                        <ul>
                            <li>HMAC-SHA256签名算法</li>
                            <li>请求参数验证</li>
                            <li>敏感数据脱敏</li>
                            <li>完整的日志记录</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script th:src="@{/js/swgx-test.js}"></script>
    <script>
        function generateOrderNumber() {
            var orderNumber = SwgxTest.generateOrderNumber();
            $('#orderResult').html('生成的订单号：<br><strong>' + orderNumber + '</strong>').removeClass('hidden').show();
            SwgxTest.showSuccess('订单号生成成功');
        }
        
        function calculateTax() {
            var amount = parseFloat($('#amount').val());
            var taxRate = parseFloat($('#taxRate').val());

            var taxResult = SwgxTest.calculateTax(amount, taxRate);
            if (!taxResult) {
                SwgxTest.showError('请输入有效的金额');
                return;
            }

            var result = '金额：' + taxResult.amount + ' 元<br>' +
                        '税率：' + taxResult.taxRate + '<br>' +
                        '税额：' + taxResult.taxAmount + ' 元<br>' +
                        '总额：' + taxResult.totalAmount + ' 元';

            $('#taxResult').html(result).removeClass('hidden').show();
            SwgxTest.showSuccess('税额计算完成');
        }
        
        function loadUtilsDemo() {
            SwgxTest.request({
                url: '/swgx/demo/utilsApi',
                type: 'GET',
                onSuccess: function(data) {
                    var html = '<pre>' + SwgxTest.formatJson(data) + '</pre>';
                    $('#utilsResult').html(html).removeClass('hidden').show();
                    SwgxTest.showResult('resultContainer', data, true);
                    SwgxTest.showSuccess('工具演示加载成功');
                },
                onError: function(message) {
                    $('#utilsResult').html('<div class="text-danger">' + message + '</div>').removeClass('hidden').show();
                    SwgxTest.showResult('resultContainer', message, false);
                }
            });
        }

    </script>
</body>
</html>
