package com.ruoyi.swgx.common.exception;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.swgx.common.config.SwgxApiProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * 百旺金穗云API异常处理器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Order(1)
@RestControllerAdvice(basePackages = "com.ruoyi.swgx")
public class SwgxApiExceptionHandler {

    @Autowired
    private SwgxApiProperties properties;

    /**
     * 处理百旺金穗云API异常
     */
    @ExceptionHandler(SwgxApiException.class)
    public AjaxResult handleSwgxApiException(SwgxApiException e) {
        log.error("SwgxApiExceptionHandler处理异常: code={}, message={}, reason={}",
                e.getCode(), e.getMessage(), e.getReason(), e);

        String detailedMessage = getDetailedErrorMessage(e);
        log.info("生成详细错误信息: {}", detailedMessage);

        return AjaxResult.error(detailedMessage);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public AjaxResult handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("参数验证异常", e);
        
        StringBuilder message = new StringBuilder("参数验证失败: ");
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        
        return AjaxResult.error("PARAM_VALIDATION_ERROR", message.toString());
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e) {
        log.error("绑定异常", e);
        
        StringBuilder message = new StringBuilder("参数绑定失败: ");
        for (FieldError error : e.getFieldErrors()) {
            message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        
        return AjaxResult.error("PARAM_BIND_ERROR", message.toString());
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public AjaxResult handleConstraintViolationException(ConstraintViolationException e) {
        log.error("约束违反异常", e);
        
        StringBuilder message = new StringBuilder("参数约束违反: ");
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            message.append(violation.getPropertyPath()).append(" ").append(violation.getMessage()).append("; ");
        }
        
        return AjaxResult.error("CONSTRAINT_VIOLATION_ERROR", message.toString());
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public AjaxResult handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("非法参数异常", e);
        return AjaxResult.error("ILLEGAL_ARGUMENT_ERROR", "参数错误: " + e.getMessage());
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e) {
        log.error("系统异常", e);
        return AjaxResult.error("系统异常: " + e.getMessage());
    }

    /**
     * 获取详细的错误信息
     */
    private String getDetailedErrorMessage(SwgxApiException e) {
        String message = e.getMessage();
        String reason = e.getReason();

        // 检查是否是企业信息相关的错误
        if ((message != null && message.contains("企业信息不存在")) ||
            (reason != null && reason.contains("企业信息不存在"))) {

            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append("企业配置错误：");

            // 检查具体的配置问题
            try {
                // 检查API密钥配置
                if ("app-id".equals(properties.getAppId()) || "app-secret".equals(properties.getAppSecret())) {
                    errorMsg.append("\n• API密钥未正确配置，请设置有效的 app-id 和 app-secret");
                }

                // 检查企业信息配置
                SwgxApiProperties.CompanyConfig company = properties.getCompany();
                if ("123".equals(company.getTaxNumber()) || StringUtils.isEmpty(company.getTaxNumber())) {
                    errorMsg.append("\n• 纳税人识别号未正确配置，请设置有效的税号");
                }

                if ("unique".equals(company.getUniqueId()) || StringUtils.isEmpty(company.getUniqueId())) {
                    errorMsg.append("\n• 企业唯一标识未配置，请设置有效的企业唯一标识");
                }

                errorMsg.append("\n\n请在 application.yml 中正确配置 swgx.api 相关参数");

            } catch (Exception configEx) {
                log.warn("检查配置时出错", configEx);
            }

            return errorMsg.toString();
        }

        // 检查是否是网络连接错误
        if ((message != null && (message.contains("Connection") || message.contains("timeout"))) ||
            (reason != null && (reason.contains("Connection") || reason.contains("timeout")))) {
            return "网络连接失败，请检查网络连接或API服务是否可用";
        }

        // 检查是否是认证错误
        if ((message != null && (message.contains("认证") || message.contains("授权") || message.contains("签名"))) ||
            (reason != null && (reason.contains("认证") || reason.contains("授权") || reason.contains("签名")))) {
            return "API认证失败，请检查 app-id 和 app-secret 配置是否正确";
        }

        // 返回原始错误信息，包含reason
        String result = message != null ? message : "未知错误";
        if (reason != null && !reason.trim().isEmpty() && !reason.equals(message)) {
            result += " (" + reason + ")";
        }

        return result;
    }
}
