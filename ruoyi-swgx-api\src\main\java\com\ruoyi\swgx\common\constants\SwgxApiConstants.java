package com.ruoyi.swgx.common.constants;

/**
 * 百旺金穗云API常量
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class SwgxApiConstants {

    /**
     * API路径常量
     */
    public static class ApiPaths {
        /**
         * 查询企业唯一标识
         */
        public static final String QUERY_UNIQUE_SIGN = "/cloud/queryUniqueSign";

        /**
         * 发票开具申请(V2)
         */
        public static final String APPLY_INVOICE_V2 = "/cloud/v2/applyInvoice";

        /**
         * 红冲发票开具申请
         */
        public static final String APPLY_RED_FLUSH_INVOICE = "/cloud/v2/applyRedFlushInvoice";
    }

    /**
     * HTTP头常量
     */
    public static class Headers {
        public static final String CONTENT_TYPE = "Content-Type";
        public static final String DATE = "Date";
        public static final String HOST = "host";
        public static final String NONCE = "x-htjs-nonce";
        public static final String UA = "x-htjs-ua";
        public static final String AUTHORIZATION = "Authorization";
        
        public static final String APPLICATION_JSON = "application/json";
    }

    /**
     * 响应状态码
     */
    public static class ResponseCode {
        /**
         * 成功
         */
        public static final String SUCCESS = "0";

        /**
         * 需要登录
         */
        public static final String NEED_LOGIN = "1004";

        /**
         * 需要认证
         */
        public static final String NEED_AUTH = "1003";
    }

    /**
     * 发票状态
     */
    public static class InvoiceStatus {
        /**
         * 开具成功
         */
        public static final String SUCCESS = "1";

        /**
         * 开具失败
         */
        public static final String FAILED = "2";

        /**
         * 已开具未生成PDF（税控电子票）
         */
        public static final String PDF_PENDING = "3";

        /**
         * 需要登录
         */
        public static final String NEED_LOGIN = "1004";

        /**
         * 需要认证
         */
        public static final String NEED_AUTH = "1003";
    }

    /**
     * 发票类型代码
     */
    public static class InvoiceType {
        // 税控发票类型
        public static final String TAX_CONTROL_ELECTRONIC = "026"; // 电子发票
        public static final String TAX_CONTROL_NORMAL = "007"; // 普通发票
        public static final String TAX_CONTROL_SPECIAL = "004"; // 专用发票
        public static final String TAX_CONTROL_ROLL = "025"; // 卷票
        public static final String TAX_CONTROL_ELECTRONIC_SPECIAL = "028"; // 电子专用发票

        // 数电发票类型
        public static final String DIGITAL_SPECIAL = "01"; // 数电专票
        public static final String DIGITAL_NORMAL = "02"; // 数电普票
        public static final String DIGITAL_VEHICLE = "03"; // 机动车纸票
        public static final String DIGITAL_USED_VEHICLE = "04"; // 二手车纸票
    }

    /**
     * 开票类型
     */
    public static class InvoiceKind {
        /**
         * 蓝字发票
         */
        public static final String BLUE = "0";

        /**
         * 红字发票
         */
        public static final String RED = "1";
    }

    /**
     * 业务类型代码
     */
    public static class BusinessType {
        /**
         * 发票开具
         */
        public static final String INVOICE_ISSUE = "fpkj";

        /**
         * 全电版式文件
         */
        public static final String DIGITAL_FORMAT = "qdbswj";
    }
}
