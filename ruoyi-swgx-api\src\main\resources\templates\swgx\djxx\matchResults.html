<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('匹配结果详情')" />
    <style>
        .match-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .match-header h3 {
            margin: 0;
            color: #495057;
            font-size: 18px;
        }
        
        .match-header .subtitle {
            margin: 5px 0 0 0;
            color: #6c757d;
            font-size: 14px;
        }
        
        .panel {
            margin-bottom: 20px;
        }

        .panel-title {
            font-size: 16px;
            margin: 0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .control-label {
            font-weight: 600;
            color: #495057;
        }

        .form-control-static {
            color: #212529;
            margin: 0;
            padding: 0;
        }
        
        .status-badge {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: normal;
        }
        
        .status-success { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        .status-default { background: #e2e3e5; color: #383d41; }
        
        .stats-row {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 500;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <!-- 页面头部 -->
        <div class="match-header">
            <h3><i class="fa fa-link"></i> 匹配结果详情</h3>
            <div class="subtitle">查看单据与电票的匹配关系和详细信息</div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-row">
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-item">
                        <div class="stat-number" id="matchedCount">-</div>
                        <div class="stat-label">匹配电票数</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-item">
                        <div class="stat-number" id="totalAmount">¥0.00</div>
                        <div class="stat-label">匹配总金额</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-item">
                        <div class="stat-number" id="avgScore">-</div>
                        <div class="stat-label">平均匹配度</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 单据信息卡片 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title"><i class="fa fa-file-text-o"></i> 单据基本信息</h4>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">单据编号:</label>
                            <span class="form-control-static" style="display: inline-block; font-weight: bold; color: #007bff;" th:text="${ywDjxx.djbh}">-</span>
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">单据日期:</label>
                            <span class="form-control-static" style="display: inline-block;" th:text="${ywDjxx.djrq != null ? #dates.format(ywDjxx.djrq, 'yyyy-MM-dd') : '-'}">-</span>
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">购方名称:</label>
                            <span class="form-control-static" style="display: inline-block;" th:text="${ywDjxx.gmfMc}">-</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">单据类型:</label>
                            <span class="form-control-static" style="display: inline-block;" id="documentType">-</span>
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">匹配状态:</label>
                            <span class="form-control-static" style="display: inline-block;" id="matchStatus">-</span>
                        </div>
                        <div class="form-group">
                            <label class="control-label" style="display: inline-block; width: 80px; margin-right: 10px;">合计金额:</label>
                            <span class="form-control-static" style="display: inline-block; font-weight: bold; color: #007bff;">¥<span th:text="${ywDjxx.jshj != null ? #numbers.formatDecimal(ywDjxx.jshj, 0, 2) : '0.00'}">0.00</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 匹配结果表格 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title"><i class="fa fa-table"></i> 匹配的电票明细</h4>
            </div>
            <div class="panel-body">
                <div id="matchResultsContainer">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> 加载匹配结果中...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var documentId = /*[[${ywDjxx.id}]]*/ '';
        var documentType = /*[[${ywDjxx.djlx}]]*/ '';
        var matchStatus = /*[[${ywDjxx.ppzt}]]*/ '';
        
        // 字典数据
        var djlxDatas = /*[[${@dict.getType('swgx_yw_dj_type')}]]*/ [];
        var ppztDatas = /*[[${@dict.getType('swgx_yw_pp_status')}]]*/ [];
        
        $(document).ready(function() {
            initPage();
            loadMatchResults();
        });
        
        function initPage() {
            // 设置单据类型显示
            $('#documentType').text(getDictLabel(djlxDatas, documentType));
            
            // 设置匹配状态显示
            $('#matchStatus').html(getMatchStatusBadge(matchStatus));
        }
        
        function loadMatchResults() {
            $.ajax({
                url: ctx + 'swgx/djxx/getMatchResults/' + documentId,
                type: 'GET',
                dataType: 'json',
                success: function(result) {
                    if (result.code == web_status.SUCCESS && result.data.success) {
                        var matchedInvoices = result.data.matchedInvoices || [];

                        // 验证数据一致性
                        validateMatchTypeConsistency(matchedInvoices);

                        displayMatchResults(matchedInvoices);
                    } else {
                        $('#matchResultsContainer').html('<div class="alert alert-warning text-center"><i class="fa fa-exclamation-triangle"></i> ' + (result.data.message || '获取匹配结果失败') + '</div>');
                        updateStats(0, 0, 0);
                    }
                },
                error: function() {
                    $('#matchResultsContainer').html('<div class="alert alert-danger text-center"><i class="fa fa-times-circle"></i> 获取匹配结果失败，请检查网络连接</div>');
                    updateStats(0, 0, 0);
                }
            });
        }

        // 验证匹配类型数据一致性
        function validateMatchTypeConsistency(matchedInvoices) {
            if (!matchedInvoices || matchedInvoices.length === 0) return;

            var firstMatchType = matchedInvoices[0].ppLx;
            var hasInconsistency = false;

            for (var i = 1; i < matchedInvoices.length; i++) {
                if (matchedInvoices[i].ppLx !== firstMatchType) {
                    hasInconsistency = true;
                    console.warn('匹配类型数据不一致：第' + (i+1) + '条记录的匹配类型(' + matchedInvoices[i].ppLx + ')与第1条记录(' + firstMatchType + ')不一致');
                }
            }

            if (hasInconsistency) {
                console.warn('检测到匹配类型数据不一致，请检查后端数据或匹配逻辑');
            }
        }
        
        function displayMatchResults(matchedInvoices) {
            if (!matchedInvoices || matchedInvoices.length === 0) {
                $('#matchResultsContainer').html('<div class="alert alert-info text-center"><i class="fa fa-info-circle"></i> 该单据暂无匹配的电票信息</div>');
                updateStats(0, 0, 0);
                return;
            }
            
            // 计算统计信息
            var totalAmount = 0;
            var totalScore = 0;
            for (var i = 0; i < matchedInvoices.length; i++) {
                totalAmount += parseFloat(matchedInvoices[i].ppJe || 0);
                totalScore += parseFloat(matchedInvoices[i].ppDf || 0);
            }
            var avgScore = totalScore / matchedInvoices.length;
            updateStats(matchedInvoices.length, totalAmount, avgScore);
            
            var html = '<div class="table-responsive">';
            html += '<table class="table table-striped table-bordered">';
            html += '<thead>';
            html += '<tr>';
            html += '<th>发票代码</th>';
            html += '<th>发票号码</th>';
            html += '<th>开票日期</th>';
            html += '<th>销售方</th>';
            html += '<th>商品名称</th>';
            html += '<th>匹配数量</th>';
            html += '<th>匹配金额</th>';
            html += '<th>匹配类型</th>';
            html += '<th>匹配得分</th>';
            html += '<th>操作</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';
            
            for (var i = 0; i < matchedInvoices.length; i++) {
                var invoice = matchedInvoices[i];
                html += '<tr>';
                html += '<td>' + (invoice.fpDm || '-') + '</td>';
                html += '<td>' + (invoice.fpHm || '-') + '</td>';
                html += '<td>' + formatInvoiceDate(invoice.kprq) + '</td>';
                html += '<td>' + (invoice.xsfMc || '-') + '</td>';
                html += '<td>' + (invoice.spmc || '-') + '</td>';
                html += '<td class="text-right">' + (invoice.ppSl || '-') + '</td>';
                html += '<td class="text-right">¥' + (invoice.ppJe ? parseFloat(invoice.ppJe).toFixed(2) : '0.00') + '</td>';
                html += '<td>' + getMatchTypeBadge(invoice.ppLx) + '</td>';
                html += '<td class="text-center">' + (invoice.ppDf ? (parseFloat(invoice.ppDf) * 100).toFixed(1) + '%' : '-') + '</td>';
                html += '<td class="text-center">';
                html += '-'; // 移除取消匹配按钮
                html += '</td>';
                html += '</tr>';
            }
            
            html += '</tbody>';
            html += '</table>';
            html += '</div>';
            
            $('#matchResultsContainer').html(html);
        }
        
        function updateStats(count, amount, avgScore) {
            $('#matchedCount').text(count);
            $('#totalAmount').text('¥' + amount.toFixed(2));
            $('#avgScore').text(avgScore > 0 ? (avgScore * 100).toFixed(1) + '%' : '-');
        }
        
        // 工具函数
        function getDictLabel(dictData, value) {
            if (!dictData || !value) return value || '-';
            for (var i = 0; i < dictData.length; i++) {
                if (dictData[i].dictValue === value) {
                    return dictData[i].dictLabel;
                }
            }
            return value;
        }
        
        function getMatchStatusBadge(status) {
            switch (status) {
                case '0': return '<span class="status-badge status-default">未匹配</span>';
                case '1': return '<span class="status-badge status-warning">部分匹配</span>';
                case '2': return '<span class="status-badge status-success">完全匹配</span>';
                default: return '<span class="status-badge status-default">未知</span>';
            }
        }
        
        function getMatchTypeBadge(type) {
            switch (type) {
                case '1': return '<span class="status-badge status-success">精确匹配</span>';
                case '2': return '<span class="status-badge status-info">模糊匹配</span>';
                case '3': return '<span class="status-badge status-warning">手工匹配</span>';
                default: return '<span class="status-badge status-default">未知</span>';
            }
        }
        
        function formatInvoiceDate(dateStr) {
            if (!dateStr) return '-';
            
            // 如果是数字格式的日期（如：20250101）
            if (/^\d{8}$/.test(dateStr)) {
                var year = dateStr.substring(0, 4);
                var month = dateStr.substring(4, 6);
                var day = dateStr.substring(6, 8);
                return year + '-' + month + '-' + day;
            }
            
            return dateStr;
        }


    </script>
</body>
</html>
