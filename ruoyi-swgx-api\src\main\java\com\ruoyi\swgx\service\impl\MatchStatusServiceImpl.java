package com.ruoyi.swgx.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.swgx.domain.DpFpxx;
import com.ruoyi.swgx.domain.DpFpxxMxPp;
import com.ruoyi.swgx.domain.YwDjxx;
import com.ruoyi.swgx.domain.YwDjmx;
import com.ruoyi.swgx.mapper.DpFpxxMapper;
import com.ruoyi.swgx.mapper.DpFpxxMxPpMapper;
import com.ruoyi.swgx.mapper.YwDjxxMapper;
import com.ruoyi.swgx.service.IMatchStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 匹配状态管理服务实现
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
public class MatchStatusServiceImpl implements IMatchStatusService {
    
    @Autowired
    private YwDjxxMapper ywDjxxMapper;
    
    @Autowired
    private DpFpxxMapper dpFpxxMapper;
    
    @Autowired
    private DpFpxxMxPpMapper dpFpxxMxPpMapper;
    
    /**
     * 更新单据匹配状态
     */
    @Override
    @Transactional
    public String updateDocumentMatchStatus(String djId) {
        log.debug("开始更新单据匹配状态，单据ID: {}", djId);
        
        try {
            // 查询单据所有明细
            List<YwDjmx> djmxList = ywDjxxMapper.selectYwDjmxByDjId(djId);
            if (djmxList == null || djmxList.isEmpty()) {
                log.warn("单据{}没有明细数据", djId);
                return "0"; // 无明细，未匹配
            }
            
            // 统计匹配状态
            int totalCount = djmxList.size();
            int matchedCount = 0; // 完全匹配
            int partialCount = 0; // 部分匹配
            
            for (YwDjmx djmx : djmxList) {
                String ppZt = djmx.getPpZt();
                if ("2".equals(ppZt)) {
                    matchedCount++;
                } else if ("1".equals(ppZt)) {
                    partialCount++;
                }
            }
            
            // 计算整体匹配状态
            String matchStatus;
            if (matchedCount == totalCount) {
                matchStatus = "2"; // 完全匹配
            } else if (matchedCount > 0 || partialCount > 0) {
                matchStatus = "1"; // 部分匹配
            } else {
                matchStatus = "0"; // 未匹配
            }
            
            // 更新单据主表状态
            YwDjxx djxx = new YwDjxx();
            djxx.setId(djId);
            djxx.setPpzt(matchStatus);
            djxx.setUpdateTime(DateUtils.getNowDate());
            ywDjxxMapper.updateYwDjxx(djxx);
            
            log.debug("单据{}匹配状态更新完成: {} -> {}", djId, 
                String.format("总计%d,完全匹配%d,部分匹配%d", totalCount, matchedCount, partialCount), 
                matchStatus);
            
            return matchStatus;
            
        } catch (Exception e) {
            log.error("更新单据匹配状态失败，单据ID: {}", djId, e);
            throw new RuntimeException("更新单据匹配状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新发票匹配状态
     */
    @Override
    @Transactional
    public String updateInvoiceMatchStatus(String fpId) {
        log.debug("开始更新发票匹配状态，发票ID: {}", fpId);
        
        try {
            // 查询发票所有明细的匹配状态
            List<DpFpxxMxPp> mxPpList = dpFpxxMxPpMapper.selectByFpId(fpId);
            if (mxPpList == null || mxPpList.isEmpty()) {
                log.warn("发票{}没有明细匹配数据", fpId);
                return "0"; // 无明细，未匹配
            }
            
            // 统计匹配状态
            int totalCount = mxPpList.size();
            int matchedCount = 0; // 完全匹配
            int partialCount = 0; // 部分匹配
            
            for (DpFpxxMxPp mxPp : mxPpList) {
                String ppZt = mxPp.getPpZt();
                if ("2".equals(ppZt)) {
                    matchedCount++;
                } else if ("1".equals(ppZt)) {
                    partialCount++;
                }
            }
            
            // 计算整体匹配状态
            String matchStatus;
            if (matchedCount == totalCount) {
                matchStatus = "2"; // 完全匹配
            } else if (matchedCount > 0 || partialCount > 0) {
                matchStatus = "1"; // 部分匹配
            } else {
                matchStatus = "0"; // 未匹配
            }
            
            // 更新发票主表状态
            DpFpxx fpxx = new DpFpxx();
            fpxx.setId(fpId);
            fpxx.setPpzt(matchStatus);
            fpxx.setUpdateTime(DateUtils.getNowDate());
            dpFpxxMapper.updateDpFpxx(fpxx);
            
            log.debug("发票{}匹配状态更新完成: {} -> {}", fpId, 
                String.format("总计%d,完全匹配%d,部分匹配%d", totalCount, matchedCount, partialCount), 
                matchStatus);
            
            return matchStatus;
            
        } catch (Exception e) {
            log.error("更新发票匹配状态失败，发票ID: {}", fpId, e);
            throw new RuntimeException("更新发票匹配状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新单据匹配状态
     */
    @Override
    @Transactional
    public void batchUpdateDocumentMatchStatus(String[] djIds) {
        if (djIds == null || djIds.length == 0) {
            return;
        }
        
        log.debug("开始批量更新单据匹配状态，数量: {}", djIds.length);
        
        for (String djId : djIds) {
            try {
                updateDocumentMatchStatus(djId);
            } catch (Exception e) {
                log.error("批量更新单据匹配状态失败，单据ID: {}", djId, e);
                // 继续处理其他单据，不中断整个批量操作
            }
        }
        
        log.debug("批量更新单据匹配状态完成");
    }
    
    /**
     * 批量更新发票匹配状态
     */
    @Override
    @Transactional
    public void batchUpdateInvoiceMatchStatus(String[] fpIds) {
        if (fpIds == null || fpIds.length == 0) {
            return;
        }
        
        log.debug("开始批量更新发票匹配状态，数量: {}", fpIds.length);
        
        for (String fpId : fpIds) {
            try {
                updateInvoiceMatchStatus(fpId);
            } catch (Exception e) {
                log.error("批量更新发票匹配状态失败，发票ID: {}", fpId, e);
                // 继续处理其他发票，不中断整个批量操作
            }
        }
        
        log.debug("批量更新发票匹配状态完成");
    }
    
    /**
     * 检查数据一致性
     */
    @Override
    public int checkDataConsistency() {
        // TODO: 实现数据一致性检查逻辑
        // 比较主表和明细表的匹配状态是否一致
        log.info("数据一致性检查功能待实现");
        return 0;
    }
    
    /**
     * 修复数据一致性
     */
    @Override
    @Transactional
    public int fixDataConsistency() {
        // TODO: 实现数据一致性修复逻辑
        // 重新计算所有主表的匹配状态
        log.info("数据一致性修复功能待实现");
        return 0;
    }
}
