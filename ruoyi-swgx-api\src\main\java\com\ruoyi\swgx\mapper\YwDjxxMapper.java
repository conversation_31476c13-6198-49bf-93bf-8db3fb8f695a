package com.ruoyi.swgx.mapper;

import java.util.List;
import com.ruoyi.swgx.domain.YwDjxx;
import com.ruoyi.swgx.domain.YwDjmx;

/**
 * 业务单据主Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface YwDjxxMapper 
{
    /**
     * 查询业务单据主
     * 
     * @param id 业务单据主主键
     * @return 业务单据主
     */
    public YwDjxx selectYwDjxxById(String id);

    /**
     * 查询业务单据主列表
     * 
     * @param ywDjxx 业务单据主
     * @return 业务单据主集合
     */
    public List<YwDjxx> selectYwDjxxList(YwDjxx ywDjxx);

    /**
     * 新增业务单据主
     * 
     * @param ywDjxx 业务单据主
     * @return 结果
     */
    public int insertYwDjxx(YwDjxx ywDjxx);

    /**
     * 修改业务单据主
     * 
     * @param ywDjxx 业务单据主
     * @return 结果
     */
    public int updateYwDjxx(YwDjxx ywDjxx);

    /**
     * 删除业务单据主
     * 
     * @param id 业务单据主主键
     * @return 结果
     */
    public int deleteYwDjxxById(String id);

    /**
     * 批量删除业务单据主
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYwDjxxByIds(String[] ids);

    /**
     * 批量删除业务单据明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYwDjmxByDjIds(String[] ids);
    
    /**
     * 批量新增业务单据明细
     * 
     * @param ywDjmxList 业务单据明细列表
     * @return 结果
     */
    public int batchYwDjmx(List<YwDjmx> ywDjmxList);
    

    /**
     * 通过业务单据主主键删除业务单据明细信息
     *
     * @param id 业务单据主ID
     * @return 结果
     */
    public int deleteYwDjmxByDjId(String id);

    /**
     * 查询业务单据明细列表（用于匹配）
     *
     * @param djId 单据ID
     * @return 业务单据明细集合
     */
    public List<YwDjmx> selectYwDjmxByDjId(String djId);

    /**
     * 修改业务单据明细
     *
     * @param ywDjmx 业务单据明细
     * @return 结果
     */
    public int updateYwDjmx(YwDjmx ywDjmx);

    /**
     * 根据ID查询业务单据明细
     *
     * @param id 业务单据明细ID
     * @return 业务单据明细
     */
    public YwDjmx selectYwDjmxById(String id);
}
