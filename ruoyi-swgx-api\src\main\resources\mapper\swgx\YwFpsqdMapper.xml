<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.swgx.mapper.YwFpsqdMapper">
    
    <resultMap type="YwFpsqd" id="YwFpsqdResult">
        <result property="swguid"    column="SWGUID"    />
        <result property="swfpdate"    column="SWFPDATE"    />
        <result property="swcsflag"    column="SWCSFLAG"    />
        <result property="qyid"    column="QYID"    />
        <result property="ywdjdm"    column="YWDJDM"    />
        <result property="sfdj"    column="SFDJ"    />
        <result property="djbh"    column="DJBH"    />
        <result property="fplxdm"    column="FPLXDM"    />
        <result property="kplx"    column="KPLX"    />
        <result property="kpzt"    column="KPZT"    />
        <result property="fpdm"    column="FPDM"    />
        <result property="fphm"    column="FPHM"    />
        <result property="kprq"    column="KPRQ"    />
        <result property="tspz"    column="TSPZ"    />
        <result property="zsfs"    column="ZSFS"    />
        <result property="qdbz"    column="QDBZ"    />
        <result property="xsfmc"    column="XSFMC"    />
        <result property="xsfnsrsbh"    column="XSFNSRSBH"    />
        <result property="xsfdz"    column="XSFDZ"    />
        <result property="xsfdh"    column="XSFDH"    />
        <result property="xsfyh"    column="XSFYH"    />
        <result property="xsfzh"    column="XSFZH"    />
        <result property="gmfmc"    column="GMFMC"    />
        <result property="gmfnsrsbh"    column="GMFNSRSBH"    />
        <result property="gmfdz"    column="GMFDZ"    />
        <result property="gmfdh"    column="GMFDH"    />
        <result property="gmfyh"    column="GMFYH"    />
        <result property="gmfzh"    column="GMFZH"    />
        <result property="gmfmobile"    column="GMFMOBILE"    />
        <result property="gmfemail"    column="GMFEMAIL"    />
        <result property="kpr"    column="KPR"    />
        <result property="skr"    column="SKR"    />
        <result property="fhr"    column="FHR"    />
        <result property="sqr"    column="SQR"    />
        <result property="jshj"    column="JSHJ"    />
        <result property="hjje"    column="HJJE"    />
        <result property="hjse"    column="HJSE"    />
        <result property="bz"    column="BZ"    />
        <result property="fjys"    column="FJYS"    />
        <result property="yfpdm"    column="YFPDM"    />
        <result property="yfphm"    column="YFPHM"    />
        <result property="kce"    column="KCE"    />
        <result property="sslkjly"    column="SSLKJLY"    />
        <result property="ykprq"    column="YKPRQ"    />
        <result property="yfplxdm"    column="YFPLXDM"    />
        <result property="chyydm"    column="CHYYDM"    />
        <result property="checkaudit"    column="CHECKAUDIT"    />
        <result property="dlzh"    column="DLZH"    />
        <result property="zjhm"    column="ZJHM"    />
        <result property="sfwzzfp"    column="SFWZZFP"    />
        <result property="zpfppzdm"    column="ZPFPPZDM"    />
        <result property="gmfzrrbs"    column="GMFZRRBS"    />
        <result property="spflxconfirm"    column="SPFLXCONFIRM"    />
        <result property="sfzsgmfyhzh"    column="SFZSGMFYHZH"    />
        <result property="sfzsxsfyhzh"    column="SFZSXSFYHZH"    />
        <result property="sfzsgmfdzdh"    column="SFZSGMFDZDH"    />
        <result property="sfzsxsfdzdh"    column="SFZSXSFDZDH"    />
        <result property="ncpsgzjlx"    column="NCPSGZJLX"    />
        <result property="cktslxdm"    column="CKTSLXDM"    />
        <result property="clouddata"    column="CLOUDDATA"    />
        <result property="kz"    column="KZ"    />
        <result property="kz1"    column="KZ1"    />
        <result property="errmsg"    column="ERRMSG"    />
        <result property="create_by"    column="create_by"    />
        <result property="create_time"    column="create_time"    />
        <result property="update_by"    column="update_by"    />
        <result property="update_time"    column="update_time"    />
    </resultMap>

    <resultMap id="YwFpsqdYwFpsqdmxResult" type="YwFpsqd" extends="YwFpsqdResult">
        <collection property="ywFpsqdmxList" ofType="YwFpsqdmx" column="SWGUID" select="selectYwFpsqdmxList" />
    </resultMap>

    <resultMap type="YwFpsqdmx" id="YwFpsqdmxResult">
        <result property="swguid"    column="SWGUID"    />
        <result property="swmainguid"    column="SWMAINGUID"    />
        <result property="swoutdetailno"    column="SWOUTDETAILNO"    />
        <result property="swcsflag"    column="SWCSFLAG"    />
        <result property="djbh"    column="DJBH"    />
        <result property="xh"    column="XH"    />
        <result property="lzmxxh"    column="LZMXXH"    />
        <result property="fpxzh"    column="FPXZH"    />
        <result property="hsbz"    column="HSBZ"    />
        <result property="spmc"    column="SPMC"    />
        <result property="spbm"    column="SPBM"    />
        <result property="ggxh"    column="GGXH"    />
        <result property="dw"    column="DW"    />
        <result property="dj"    column="DJ"    />
        <result property="sl"    column="SL"    />
        <result property="je"    column="JE"    />
        <result property="se"    column="SE"    />
        <result property="slv"    column="SLV"    />
        <result property="lslbs"    column="LSLBS"    />
        <result property="yhzcbs"    column="YHZCBS"    />
        <result property="zxbm"    column="ZXBM"    />
        <result property="zzstsgl"    column="ZZSTSGL"    />
        <result property="bdcdz"    column="BDCDZ"    />
        <result property="fulladdress"    column="FULLADDRESS"    />
        <result property="zlqqz"    column="ZLQQZ"    />
        <result property="kdsbz"    column="KDSBZ"    />
        <result property="cqzsh"    column="CQZSH"    />
        <result property="mjdw"    column="MJDW"    />
        <result property="wqhtbabh"    column="WQHTBABH"    />
        <result property="tdzzsxmbh"    column="TDZZSXMBH"    />
        <result property="hdjsjg"    column="HDJSJG"    />
        <result property="sjcjhsje"    column="SJCJHSJE"    />
        <result property="cph"    column="CPH"    />
        <result property="create_by"    column="create_by"    />
        <result property="create_time"    column="create_time"    />
        <result property="update_by"    column="update_by"    />
        <result property="update_time"    column="update_time"    />
    </resultMap>

    <sql id="selectYwFpsqdVo">
        select SWGUID, SWFPDATE, SWCSFLAG, QYID, YWDJDM, SFDJ, DJBH, FPLXDM, KPLX, KPZT, FPDM, FPHM, KPRQ, TSPZ, ZSFS, QDBZ, XSFMC, XSFNSRSBH, XSFDZ, XSFDH, XSFYH, XSFZH, GMFMC, GMFNSRSBH, GMFDZ, GMFDH, GMFYH, GMFZH, GMFMOBILE, GMFEMAIL, KPR, SKR, FHR, SQR, JSHJ, HJJE, HJSE, BZ, FJYS, YFPDM, YFPHM, KCE, SSLKJLY, YKPRQ, YFPLXDM, CHYYDM, CHECKAUDIT, DLZH, ZJHM, SFWZZFP, ZPFPPZDM, GMFZRRBS, SPFLXCONFIRM, SFZSGMFYHZH, SFZSXSFYHZH, SFZSGMFDZDH, SFZSXSFDZDH, NCPSGZJLX, CKTSLXDM, CLOUDDATA, KZ, KZ1, ERRMSG, create_by, create_time, update_by, update_time from swgx_yw_fpsqd
    </sql>

    <select id="selectYwFpsqdList" parameterType="YwFpsqd" resultMap="YwFpsqdResult">
        <include refid="selectYwFpsqdVo"/>
        <where>  
            <if test="qyid != null  and qyid != ''"> and QYID = #{qyid}</if>
            <if test="djbh != null  and djbh != ''"> and DJBH = #{djbh}</if>
            <if test="fpdm != null  and fpdm != ''"> and FPDM = #{fpdm}</if>
            <if test="fphm != null  and fphm != ''"> and FPHM = #{fphm}</if>
            <if test="kprq != null  and kprq != ''"> and KPRQ = #{kprq}</if>
            <if test="xsfmc != null  and xsfmc != ''"> and XSFMC = #{xsfmc}</if>
            <if test="xsfnsrsbh != null  and xsfnsrsbh != ''"> and XSFNSRSBH = #{xsfnsrsbh}</if>
            <if test="gmfmc != null  and gmfmc != ''"> and GMFMC = #{gmfmc}</if>
            <if test="gmfnsrsbh != null  and gmfnsrsbh != ''"> and GMFNSRSBH = #{gmfnsrsbh}</if>
        </where>
    </select>
    
    <select id="selectYwFpsqdBySwguid" parameterType="String" resultMap="YwFpsqdYwFpsqdmxResult">
        select SWGUID, SWFPDATE, SWCSFLAG, QYID, YWDJDM, SFDJ, DJBH, FPLXDM, KPLX, KPZT, FPDM, FPHM, KPRQ, TSPZ, ZSFS, QDBZ, XSFMC, XSFNSRSBH, XSFDZ, XSFDH, XSFYH, XSFZH, GMFMC, GMFNSRSBH, GMFDZ, GMFDH, GMFYH, GMFZH, GMFMOBILE, GMFEMAIL, KPR, SKR, FHR, SQR, JSHJ, HJJE, HJSE, BZ, FJYS, YFPDM, YFPHM, KCE, SSLKJLY, YKPRQ, YFPLXDM, CHYYDM, CHECKAUDIT, DLZH, ZJHM, SFWZZFP, ZPFPPZDM, GMFZRRBS, SPFLXCONFIRM, SFZSGMFYHZH, SFZSXSFYHZH, SFZSGMFDZDH, SFZSXSFDZDH, NCPSGZJLX, CKTSLXDM, CLOUDDATA, KZ, KZ1, ERRMSG, create_by, create_time, update_by, update_time
        from swgx_yw_fpsqd
        where SWGUID = #{swguid}
    </select>

    <select id="selectYwFpsqdmxList" resultMap="YwFpsqdmxResult">
        select SWGUID, SWMAINGUID, SWOUTDETAILNO, SWCSFLAG, DJBH, XH, LZMXXH, FPXZH, HSBZ, SPMC, SPBM, GGXH, DW, DJ, SL, JE, SE, SLV, LSLBS, YHZCBS, ZXBM, ZZSTSGL, BDCDZ, FULLADDRESS, ZLQQZ, KDSBZ, CQZSH, MJDW, WQHTBABH, TDZZSXMBH, HDJSJG, SJCJHSJE, CPH, create_by, create_time, update_by, update_time
        from swgx_yw_fpsqdmx
        where SWMAINGUID = #{SWMAINGUID}
    </select>

    <insert id="insertYwFpsqd" parameterType="YwFpsqd">
        insert into swgx_yw_fpsqd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="swguid != null">SWGUID,</if>
            <if test="swfpdate != null">SWFPDATE,</if>
            <if test="swcsflag != null">SWCSFLAG,</if>
            <if test="qyid != null">QYID,</if>
            <if test="ywdjdm != null">YWDJDM,</if>
            <if test="sfdj != null">SFDJ,</if>
            <if test="djbh != null">DJBH,</if>
            <if test="fplxdm != null">FPLXDM,</if>
            <if test="kplx != null">KPLX,</if>
            <if test="kpzt != null">KPZT,</if>
            <if test="fpdm != null and fpdm != ''">FPDM,</if>
            <if test="fphm != null and fphm != ''">FPHM,</if>
            <if test="kprq != null">KPRQ,</if>
            <if test="tspz != null">TSPZ,</if>
            <if test="zsfs != null">ZSFS,</if>
            <if test="qdbz != null">QDBZ,</if>
            <if test="xsfmc != null">XSFMC,</if>
            <if test="xsfnsrsbh != null">XSFNSRSBH,</if>
            <if test="xsfdz != null">XSFDZ,</if>
            <if test="xsfdh != null">XSFDH,</if>
            <if test="xsfyh != null">XSFYH,</if>
            <if test="xsfzh != null">XSFZH,</if>
            <if test="gmfmc != null">GMFMC,</if>
            <if test="gmfnsrsbh != null">GMFNSRSBH,</if>
            <if test="gmfdz != null">GMFDZ,</if>
            <if test="gmfdh != null">GMFDH,</if>
            <if test="gmfyh != null">GMFYH,</if>
            <if test="gmfzh != null">GMFZH,</if>
            <if test="gmfmobile != null">GMFMOBILE,</if>
            <if test="gmfemail != null">GMFEMAIL,</if>
            <if test="kpr != null">KPR,</if>
            <if test="skr != null">SKR,</if>
            <if test="fhr != null">FHR,</if>
            <if test="sqr != null">SQR,</if>
            <if test="jshj != null">JSHJ,</if>
            <if test="hjje != null">HJJE,</if>
            <if test="hjse != null">HJSE,</if>
            <if test="bz != null">BZ,</if>
            <if test="fjys != null">FJYS,</if>
            <if test="yfpdm != null">YFPDM,</if>
            <if test="yfphm != null">YFPHM,</if>
            <if test="kce != null">KCE,</if>
            <if test="sslkjly != null">SSLKJLY,</if>
            <if test="ykprq != null">YKPRQ,</if>
            <if test="yfplxdm != null">YFPLXDM,</if>
            <if test="chyydm != null">CHYYDM,</if>
            <if test="checkaudit != null">CHECKAUDIT,</if>
            <if test="dlzh != null">DLZH,</if>
            <if test="zjhm != null">ZJHM,</if>
            <if test="sfwzzfp != null">SFWZZFP,</if>
            <if test="zpfppzdm != null">ZPFPPZDM,</if>
            <if test="gmfzrrbs != null">GMFZRRBS,</if>
            <if test="spflxconfirm != null">SPFLXCONFIRM,</if>
            <if test="sfzsgmfyhzh != null">SFZSGMFYHZH,</if>
            <if test="sfzsxsfyhzh != null">SFZSXSFYHZH,</if>
            <if test="sfzsgmfdzdh != null">SFZSGMFDZDH,</if>
            <if test="sfzsxsfdzdh != null">SFZSXSFDZDH,</if>
            <if test="ncpsgzjlx != null">NCPSGZJLX,</if>
            <if test="cktslxdm != null">CKTSLXDM,</if>
            <if test="clouddata != null">CLOUDDATA,</if>
            <if test="kz != null">KZ,</if>
            <if test="kz1 != null">KZ1,</if>
            <if test="errmsg != null">ERRMSG,</if>
            <if test="create_by != null">create_by,</if>
            <if test="create_time != null">create_time,</if>
            <if test="update_by != null">update_by,</if>
            <if test="update_time != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="swguid != null">#{swguid},</if>
            <if test="swfpdate != null">#{swfpdate},</if>
            <if test="swcsflag != null">#{swcsflag},</if>
            <if test="qyid != null">#{qyid},</if>
            <if test="ywdjdm != null">#{ywdjdm},</if>
            <if test="sfdj != null">#{sfdj},</if>
            <if test="djbh != null">#{djbh},</if>
            <if test="fplxdm != null">#{fplxdm},</if>
            <if test="kplx != null">#{kplx},</if>
            <if test="kpzt != null">#{kpzt},</if>
            <if test="fpdm != null and fpdm != ''">#{fpdm},</if>
            <if test="fphm != null and fphm != ''">#{fphm},</if>
            <if test="kprq != null">#{kprq},</if>
            <if test="tspz != null">#{tspz},</if>
            <if test="zsfs != null">#{zsfs},</if>
            <if test="qdbz != null">#{qdbz},</if>
            <if test="xsfmc != null">#{xsfmc},</if>
            <if test="xsfnsrsbh != null">#{xsfnsrsbh},</if>
            <if test="xsfdz != null">#{xsfdz},</if>
            <if test="xsfdh != null">#{xsfdh},</if>
            <if test="xsfyh != null">#{xsfyh},</if>
            <if test="xsfzh != null">#{xsfzh},</if>
            <if test="gmfmc != null">#{gmfmc},</if>
            <if test="gmfnsrsbh != null">#{gmfnsrsbh},</if>
            <if test="gmfdz != null">#{gmfdz},</if>
            <if test="gmfdh != null">#{gmfdh},</if>
            <if test="gmfyh != null">#{gmfyh},</if>
            <if test="gmfzh != null">#{gmfzh},</if>
            <if test="gmfmobile != null">#{gmfmobile},</if>
            <if test="gmfemail != null">#{gmfemail},</if>
            <if test="kpr != null">#{kpr},</if>
            <if test="skr != null">#{skr},</if>
            <if test="fhr != null">#{fhr},</if>
            <if test="sqr != null">#{sqr},</if>
            <if test="jshj != null">#{jshj},</if>
            <if test="hjje != null">#{hjje},</if>
            <if test="hjse != null">#{hjse},</if>
            <if test="bz != null">#{bz},</if>
            <if test="fjys != null">#{fjys},</if>
            <if test="yfpdm != null">#{yfpdm},</if>
            <if test="yfphm != null">#{yfphm},</if>
            <if test="kce != null">#{kce},</if>
            <if test="sslkjly != null">#{sslkjly},</if>
            <if test="ykprq != null">#{ykprq},</if>
            <if test="yfplxdm != null">#{yfplxdm},</if>
            <if test="chyydm != null">#{chyydm},</if>
            <if test="checkaudit != null">#{checkaudit},</if>
            <if test="dlzh != null">#{dlzh},</if>
            <if test="zjhm != null">#{zjhm},</if>
            <if test="sfwzzfp != null">#{sfwzzfp},</if>
            <if test="zpfppzdm != null">#{zpfppzdm},</if>
            <if test="gmfzrrbs != null">#{gmfzrrbs},</if>
            <if test="spflxconfirm != null">#{spflxconfirm},</if>
            <if test="sfzsgmfyhzh != null">#{sfzsgmfyhzh},</if>
            <if test="sfzsxsfyhzh != null">#{sfzsxsfyhzh},</if>
            <if test="sfzsgmfdzdh != null">#{sfzsgmfdzdh},</if>
            <if test="sfzsxsfdzdh != null">#{sfzsxsfdzdh},</if>
            <if test="ncpsgzjlx != null">#{ncpsgzjlx},</if>
            <if test="cktslxdm != null">#{cktslxdm},</if>
            <if test="clouddata != null">#{clouddata},</if>
            <if test="kz != null">#{kz},</if>
            <if test="kz1 != null">#{kz1},</if>
            <if test="errmsg != null">#{errmsg},</if>
            <if test="create_by != null">#{create_by},</if>
            <if test="create_time != null">#{create_time},</if>
            <if test="update_by != null">#{update_by},</if>
            <if test="update_time != null">#{update_time},</if>
         </trim>
    </insert>

    <update id="updateYwFpsqd" parameterType="YwFpsqd">
        update swgx_yw_fpsqd
        <trim prefix="SET" suffixOverrides=",">
            <if test="swfpdate != null">SWFPDATE = #{swfpdate},</if>
            <if test="swcsflag != null">SWCSFLAG = #{swcsflag},</if>
            <if test="qyid != null">QYID = #{qyid},</if>
            <if test="ywdjdm != null">YWDJDM = #{ywdjdm},</if>
            <if test="sfdj != null">SFDJ = #{sfdj},</if>
            <if test="djbh != null">DJBH = #{djbh},</if>
            <if test="fplxdm != null">FPLXDM = #{fplxdm},</if>
            <if test="kplx != null">KPLX = #{kplx},</if>
            <if test="kpzt != null">KPZT = #{kpzt},</if>
            <if test="fpdm != null and fpdm != ''">FPDM = #{fpdm},</if>
            <if test="fphm != null and fphm != ''">FPHM = #{fphm},</if>
            <if test="kprq != null">KPRQ = #{kprq},</if>
            <if test="tspz != null">TSPZ = #{tspz},</if>
            <if test="zsfs != null">ZSFS = #{zsfs},</if>
            <if test="qdbz != null">QDBZ = #{qdbz},</if>
            <if test="xsfmc != null">XSFMC = #{xsfmc},</if>
            <if test="xsfnsrsbh != null">XSFNSRSBH = #{xsfnsrsbh},</if>
            <if test="xsfdz != null">XSFDZ = #{xsfdz},</if>
            <if test="xsfdh != null">XSFDH = #{xsfdh},</if>
            <if test="xsfyh != null">XSFYH = #{xsfyh},</if>
            <if test="xsfzh != null">XSFZH = #{xsfzh},</if>
            <if test="gmfmc != null">GMFMC = #{gmfmc},</if>
            <if test="gmfnsrsbh != null">GMFNSRSBH = #{gmfnsrsbh},</if>
            <if test="gmfdz != null">GMFDZ = #{gmfdz},</if>
            <if test="gmfdh != null">GMFDH = #{gmfdh},</if>
            <if test="gmfyh != null">GMFYH = #{gmfyh},</if>
            <if test="gmfzh != null">GMFZH = #{gmfzh},</if>
            <if test="gmfmobile != null">GMFMOBILE = #{gmfmobile},</if>
            <if test="gmfemail != null">GMFEMAIL = #{gmfemail},</if>
            <if test="kpr != null">KPR = #{kpr},</if>
            <if test="skr != null">SKR = #{skr},</if>
            <if test="fhr != null">FHR = #{fhr},</if>
            <if test="sqr != null">SQR = #{sqr},</if>
            <if test="jshj != null">JSHJ = #{jshj},</if>
            <if test="hjje != null">HJJE = #{hjje},</if>
            <if test="hjse != null">HJSE = #{hjse},</if>
            <if test="bz != null">BZ = #{bz},</if>
            <if test="fjys != null">FJYS = #{fjys},</if>
            <if test="yfpdm != null">YFPDM = #{yfpdm},</if>
            <if test="yfphm != null">YFPHM = #{yfphm},</if>
            <if test="kce != null">KCE = #{kce},</if>
            <if test="sslkjly != null">SSLKJLY = #{sslkjly},</if>
            <if test="ykprq != null">YKPRQ = #{ykprq},</if>
            <if test="yfplxdm != null">YFPLXDM = #{yfplxdm},</if>
            <if test="chyydm != null">CHYYDM = #{chyydm},</if>
            <if test="checkaudit != null">CHECKAUDIT = #{checkaudit},</if>
            <if test="dlzh != null">DLZH = #{dlzh},</if>
            <if test="zjhm != null">ZJHM = #{zjhm},</if>
            <if test="sfwzzfp != null">SFWZZFP = #{sfwzzfp},</if>
            <if test="zpfppzdm != null">ZPFPPZDM = #{zpfppzdm},</if>
            <if test="gmfzrrbs != null">GMFZRRBS = #{gmfzrrbs},</if>
            <if test="spflxconfirm != null">SPFLXCONFIRM = #{spflxconfirm},</if>
            <if test="sfzsgmfyhzh != null">SFZSGMFYHZH = #{sfzsgmfyhzh},</if>
            <if test="sfzsxsfyhzh != null">SFZSXSFYHZH = #{sfzsxsfyhzh},</if>
            <if test="sfzsgmfdzdh != null">SFZSGMFDZDH = #{sfzsgmfdzdh},</if>
            <if test="sfzsxsfdzdh != null">SFZSXSFDZDH = #{sfzsxsfdzdh},</if>
            <if test="ncpsgzjlx != null">NCPSGZJLX = #{ncpsgzjlx},</if>
            <if test="cktslxdm != null">CKTSLXDM = #{cktslxdm},</if>
            <if test="clouddata != null">CLOUDDATA = #{clouddata},</if>
            <if test="kz != null">KZ = #{kz},</if>
            <if test="kz1 != null">KZ1 = #{kz1},</if>
            <if test="errmsg != null">ERRMSG = #{errmsg},</if>
            <if test="create_by != null">create_by = #{create_by},</if>
            <if test="create_time != null">create_time = #{create_time},</if>
            <if test="update_by != null">update_by = #{update_by},</if>
            <if test="update_time != null">update_time = #{update_time},</if>
        </trim>
        where SWGUID = #{swguid}
    </update>

    <delete id="deleteYwFpsqdBySwguid" parameterType="String">
        delete from swgx_yw_fpsqd where SWGUID = #{swguid}
    </delete>

    <delete id="deleteYwFpsqdBySwguids" parameterType="String">
        delete from swgx_yw_fpsqd where SWGUID in 
        <foreach item="swguid" collection="array" open="(" separator="," close=")">
            #{swguid}
        </foreach>
    </delete>
    
    <delete id="deleteYwFpsqdmxBySWMAINGUIDs" parameterType="String">
        delete from swgx_yw_fpsqdmx where SWMAINGUID in 
        <foreach item="sWMAINGUID" collection="array" open="(" separator="," close=")">
            #{sWMAINGUID}
        </foreach>
    </delete>

    <delete id="deleteYwFpsqdmxBySWMAINGUID" parameterType="String">
        delete from swgx_yw_fpsqdmx where SWMAINGUID = #{sWMAINGUID}
    </delete>

    <insert id="batchYwFpsqdmx">
        insert into swgx_yw_fpsqdmx( SWGUID, SWMAINGUID, SWOUTDETAILNO, SWCSFLAG, DJBH, XH, LZMXXH, FPXZH, HSBZ, SPMC, SPBM, GGXH, DW, DJ, SL, JE, SE, SLV, LSLBS, YHZCBS, ZXBM, ZZSTSGL, BDCDZ, FULLADDRESS, ZLQQZ, KDSBZ, CQZSH, MJDW, WQHTBABH, TDZZSXMBH, HDJSJG, SJCJHSJE, CPH, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.swguid}, #{item.swmainguid}, #{item.swoutdetailno}, #{item.swcsflag}, #{item.djbh}, #{item.xh}, #{item.lzmxxh}, #{item.fpxzh}, #{item.hsbz}, #{item.spmc}, #{item.spbm}, #{item.ggxh}, #{item.dw}, #{item.dj}, #{item.sl}, #{item.je}, #{item.se}, #{item.slv}, #{item.lslbs}, #{item.yhzcbs}, #{item.zxbm}, #{item.zzstsgl}, #{item.bdcdz}, #{item.fulladdress}, #{item.zlqqz}, #{item.kdsbz}, #{item.cqzsh}, #{item.mjdw}, #{item.wqhtbabh}, #{item.tdzzsxmbh}, #{item.hdjsjg}, #{item.sjcjhsje}, #{item.cph}, #{item.create_by}, #{item.create_time}, #{item.update_by}, #{item.update_time})
        </foreach>
    </insert>

</mapper>