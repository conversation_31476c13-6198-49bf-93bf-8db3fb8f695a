package com.ruoyi.swgx.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.swgx.domain.YwDjmx;
import com.ruoyi.swgx.domain.YwDjFpPp;
import com.ruoyi.swgx.domain.DpFpxx;
import com.ruoyi.swgx.domain.DpFpxxMx;
import com.ruoyi.swgx.mapper.YwDjxxMapper;
import com.ruoyi.swgx.mapper.YwDjFpPpMapper;
import com.ruoyi.swgx.mapper.DpFpxxMapper;
import com.ruoyi.swgx.domain.YwDjxx;
import com.ruoyi.swgx.service.IYwDjxxService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.uuid.IdUtils;

/**
 * 业务单据主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class YwDjxxServiceImpl implements IYwDjxxService
{
    @Autowired
    private YwDjxxMapper ywDjxxMapper;

    @Autowired
    private YwDjFpPpMapper ywDjFpPpMapper;

    @Autowired
    private DpFpxxMapper dpFpxxMapper;

    /**
     * 查询业务单据主
     * 
     * @param id 业务单据主主键
     * @return 业务单据主
     */
    @Override
    public YwDjxx selectYwDjxxById(String id)
    {
        return ywDjxxMapper.selectYwDjxxById(id);
    }

    /**
     * 查询业务单据主列表
     * 
     * @param ywDjxx 业务单据主
     * @return 业务单据主
     */
    @Override
    public List<YwDjxx> selectYwDjxxList(YwDjxx ywDjxx)
    {
        return ywDjxxMapper.selectYwDjxxList(ywDjxx);
    }

    /**
     * 新增业务单据主
     * 
     * @param ywDjxx 业务单据主
     * @return 结果
     */
    @Transactional
    @Override
    public int insertYwDjxx(YwDjxx ywDjxx)
    {
        // 为主表记录生成UUID
        ywDjxx.setId(IdUtils.fastSimpleUUID());
        ywDjxx.setCreateTime(DateUtils.getNowDate());
        int rows = ywDjxxMapper.insertYwDjxx(ywDjxx);
        insertYwDjmx(ywDjxx);
        return rows;
    }

    /**
     * 修改业务单据主
     * 
     * @param ywDjxx 业务单据主
     * @return 结果
     */
    @Transactional
    @Override
    public int updateYwDjxx(YwDjxx ywDjxx)
    {
        ywDjxx.setUpdateTime(DateUtils.getNowDate());
        ywDjxxMapper.deleteYwDjmxByDjId(ywDjxx.getId());
        insertYwDjmx(ywDjxx);
        return ywDjxxMapper.updateYwDjxx(ywDjxx);
    }

    /**
     * 批量删除业务单据主
     * 
     * @param ids 需要删除的业务单据主主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteYwDjxxByIds(String ids)
    {
        ywDjxxMapper.deleteYwDjmxByDjIds(Convert.toStrArray(ids));
        return ywDjxxMapper.deleteYwDjxxByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除业务单据主信息
     * 
     * @param id 业务单据主主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteYwDjxxById(String id)
    {
        ywDjxxMapper.deleteYwDjmxByDjId(id);
        return ywDjxxMapper.deleteYwDjxxById(id);
    }

    /**
     * 新增业务单据明细信息
     *
     * @param ywDjxx 业务单据主对象
     */
    public void insertYwDjmx(YwDjxx ywDjxx)
    {
        List<YwDjmx> ywDjmxList = ywDjxx.getYwDjmxList();
        String id = ywDjxx.getId();
        if (StringUtils.isNotNull(ywDjmxList))
        {
            List<YwDjmx> list = new ArrayList<YwDjmx>();
            for (YwDjmx ywDjmx : ywDjmxList)
            {
                // 为明细记录生成UUID
                ywDjmx.setId(IdUtils.fastSimpleUUID());
                ywDjmx.setDjId(id);
                ywDjmx.setCreateTime(DateUtils.getNowDate());
                list.add(ywDjmx);
            }
            if (list.size() > 0)
            {
                ywDjxxMapper.batchYwDjmx(list);
            }
        }
    }

    /**
     * 获取单据匹配结果详情
     *
     * @param id 业务单据主键
     * @return 匹配结果详情
     */
    @Override
    public Map<String, Object> getDocumentMatchResults(String id)
    {
        Map<String, Object> result = new HashMap<>();

        // 查询单据基本信息
        YwDjxx document = ywDjxxMapper.selectYwDjxxById(id);
        if (document == null) {
            result.put("success", false);
            result.put("message", "单据不存在");
            return result;
        }

        // 查询匹配关系
        List<YwDjFpPp> matchRelations = ywDjFpPpMapper.selectByDjId(id);

        List<Map<String, Object>> matchedInvoices = new ArrayList<>();

        for (YwDjFpPp relation : matchRelations) {
            if ("1".equals(relation.getPpZt())) { // 有效的匹配关系
                Map<String, Object> matchInfo = new HashMap<>();

                // 获取发票信息
                DpFpxx invoice = dpFpxxMapper.selectDpFpxxById(relation.getFpId());
                if (invoice != null) {
                    matchInfo.put("fpDm", invoice.getFpDm()); // 发票代码
                    matchInfo.put("fpHm", invoice.getFpHm()); // 发票号码
                    matchInfo.put("kprq", invoice.getKprq()); // 开票日期
                    matchInfo.put("xsfMc", invoice.getXsfMc()); // 销售方名称
                }

                // 获取发票明细信息
                DpFpxxMx invoiceDetail = dpFpxxMapper.selectDpFpxxMxById(relation.getFpmxId());
                if (invoiceDetail != null) {
                    matchInfo.put("spmc", invoiceDetail.getSpmc()); // 商品名称
                    matchInfo.put("ggxh", invoiceDetail.getGgxh()); // 规格型号
                    matchInfo.put("dw", invoiceDetail.getDw()); // 单位
                    matchInfo.put("dj", invoiceDetail.getDj()); // 单价
                }

                // 匹配信息
                matchInfo.put("ppSl", relation.getPpSl()); // 匹配数量
                matchInfo.put("ppJe", relation.getPpJe()); // 匹配金额
                matchInfo.put("ppSe", relation.getPpSe()); // 匹配税额
                matchInfo.put("ppDf", relation.getPpDf()); // 匹配得分
                matchInfo.put("ppLx", relation.getPpLx()); // 匹配类型
                matchInfo.put("ppRq", relation.getPpRq()); // 匹配日期
                matchInfo.put("ppRy", relation.getPpRy()); // 匹配人员

                matchedInvoices.add(matchInfo);
            }
        }

        result.put("success", true);
        result.put("document", document);
        result.put("matchedInvoices", matchedInvoices);
        result.put("totalMatched", matchedInvoices.size());

        return result;
    }
}
