<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('操作日志详细')" />
	<th:block th:include="include :: jsonview-css" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
	<form class="form-horizontal m-t" id="signupForm">
		<div class="form-group">
			<label class="col-sm-2 control-label">操作模块：</label>
			<div class="form-control-static" th:text="${operLog.title} + ' / ' + ${@dict.getLabel('sys_oper_type',operLog.businessType)}">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">登录信息：</label>
			<div class="form-control-static" th:text="${operLog.operName} + ' / ' + ${operLog.deptName} + ' / ' + ${operLog.operIp}+ ' / ' + ${operLog.operLocation}">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">请求地址：</label>
			<div class="form-control-static" th:text="${operLog.requestMethod} + ' - ' + ${operLog.operUrl} + '  ' + '(' + '耗时' + ${operLog.costTime} + '毫秒)'">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">操作方法：</label>
			<div class="form-control-static" th:text="${operLog.method}">
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">请求参数：</label>
			<div class="form-control-static"><pre id="operParam"></pre></div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">返回参数：</label>
			<div class="form-control-static"><pre id="jsonResult"></pre></div>
		</div>
		<div class="form-group">
			<label class="col-sm-2 control-label">状态：</label>
			<div class="form-control-static" th:class="${operLog.status == 0 ? 'label label-primary' : 'label label-danger'}" th:text="${operLog.status == 0 ? '正常' : '异常'}">
			</div>
		</div>
		<div class="form-group" th:style="'display:' + ${operLog.status == 0 ? 'none' : 'block'}">
			<label class="col-sm-2 control-label">异常信息：</label>
			<div class="form-control-static" th:text="${operLog.errorMsg}">
			</div>
		</div>
	</form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jsonview-js" />
    <script th:inline="javascript">
	    $(function() {
	    	var operParam = [[${operLog.operParam}]];
	    	if ($.common.isNotEmpty(operParam) && operParam.length < 2000) {
	    		$("#operParam").JSONView(operParam);
	    	} else {
	    		$("#operParam").text(operParam);
	    	}
	    	var jsonResult = [[${operLog.jsonResult}]];
	    	if ($.common.isNotEmpty(jsonResult) && jsonResult.length < 2000) {
	    		$("#jsonResult").JSONView(jsonResult);
	    	} else {
	    		$("#jsonResult").text(jsonResult);
	    	}
	    });
    </script>
</body>
</html>