package com.ruoyi.swgx.util;

import lombok.extern.slf4j.Slf4j;

/**
 * 日志工具类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class LogUtils {

    /**
     * 记录API调用开始日志
     * 
     * @param apiName API名称
     * @param params 参数
     */
    public static void logApiStart(String apiName, Object params) {
        log.info("=== API调用开始 === API: {}, 参数: {}", apiName, JsonUtils.toJson(params));
    }

    /**
     * 记录API调用成功日志
     * 
     * @param apiName API名称
     * @param result 结果
     * @param duration 耗时（毫秒）
     */
    public static void logApiSuccess(String apiName, Object result, long duration) {
        log.info("=== API调用成功 === API: {}, 耗时: {}ms, 结果: {}", 
                apiName, duration, JsonUtils.toJson(result));
    }

    /**
     * 记录API调用失败日志
     * 
     * @param apiName API名称
     * @param error 错误信息
     * @param duration 耗时（毫秒）
     */
    public static void logApiError(String apiName, String error, long duration) {
        log.error("=== API调用失败 === API: {}, 耗时: {}ms, 错误: {}", apiName, duration, error);
    }

    /**
     * 记录API调用失败日志（带异常）
     * 
     * @param apiName API名称
     * @param error 错误信息
     * @param duration 耗时（毫秒）
     * @param throwable 异常
     */
    public static void logApiError(String apiName, String error, long duration, Throwable throwable) {
        log.error("=== API调用失败 === API: {}, 耗时: {}ms, 错误: {}", apiName, duration, error, throwable);
    }

    /**
     * 记录业务操作日志
     * 
     * @param operation 操作名称
     * @param details 操作详情
     */
    public static void logBusiness(String operation, String details) {
        log.info("=== 业务操作 === 操作: {}, 详情: {}", operation, details);
    }

    /**
     * 记录性能日志
     * 
     * @param operation 操作名称
     * @param duration 耗时（毫秒）
     */
    public static void logPerformance(String operation, long duration) {
        if (duration > 5000) { // 超过5秒记录警告
            log.warn("=== 性能警告 === 操作: {}, 耗时: {}ms", operation, duration);
        } else if (duration > 1000) { // 超过1秒记录信息
            log.info("=== 性能监控 === 操作: {}, 耗时: {}ms", operation, duration);
        } else {
            log.debug("=== 性能监控 === 操作: {}, 耗时: {}ms", operation, duration);
        }
    }

    /**
     * 脱敏处理敏感信息
     * 
     * @param sensitiveData 敏感数据
     * @return 脱敏后的数据
     */
    public static String maskSensitiveData(String sensitiveData) {
        if (sensitiveData == null || sensitiveData.length() <= 4) {
            return "****";
        }
        
        int length = sensitiveData.length();
        if (length <= 8) {
            return sensitiveData.substring(0, 2) + "****" + sensitiveData.substring(length - 2);
        } else {
            return sensitiveData.substring(0, 4) + "****" + sensitiveData.substring(length - 4);
        }
    }

    /**
     * 记录缓存操作日志
     * 
     * @param operation 操作类型（GET/PUT/EVICT）
     * @param cacheName 缓存名称
     * @param key 缓存键
     * @param hit 是否命中（仅GET操作）
     */
    public static void logCache(String operation, String cacheName, String key, Boolean hit) {
        if ("GET".equals(operation) && hit != null) {
            log.debug("=== 缓存操作 === 操作: {}, 缓存: {}, 键: {}, 命中: {}", 
                    operation, cacheName, key, hit ? "是" : "否");
        } else {
            log.debug("=== 缓存操作 === 操作: {}, 缓存: {}, 键: {}", operation, cacheName, key);
        }
    }
}
