<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('业务单据详情')" />

    <style>
        .form-header { margin-top: 20px; margin-bottom: 15px; border-bottom: 1px solid #e7eaec; padding-bottom: 5px; }
        .detail-container { padding: 15px; }
        .price-highlight { font-weight: bold; color: #d9534f; }
        .remaining-amount { font-weight: bold; color: #5bc0de; }
        .form-group { margin-bottom: 8px; }
        .row { margin-bottom: 5px; }
        .form-control-static { padding-top: 3px; padding-bottom: 3px; margin-bottom: 0; line-height: 1.3; }
        .compact-section .form-group { margin-bottom: 3px; }
        .compact-section .row { margin-bottom: 0px; }
        .empty-value { color: #999; font-style: italic; }
    </style>
</head>
<body class="white-bg">
    <div class="detail-container">
        <form class="form-horizontal m" id="form-djxx-detail">
            <input name="id" th:value="${ywDjxx.id}" type="hidden">
            
            <!-- 基本信息 -->
            <h4 class="form-header h4">基本信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">单据编号：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.djbh}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">单据类型：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${@dict.getLabel('swgx_yw_dj_type', ywDjxx.djlx)}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">单据状态：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static">
                                <span class="badge badge-primary" th:text="${@dict.getLabel('swgx_yw_dj_status', ywDjxx.djzt)}"></span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">匹配状态：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static">
                                <span class="badge badge-info" th:text="${@dict.getLabel('swgx_yw_pp_status', ywDjxx.ppzt)}"></span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">单据日期：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${#dates.format(ywDjxx.djrq, 'yyyy-MM-dd HH:mm:ss')}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">创建时间：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${#dates.format(ywDjxx.createTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 购买方信息 -->
            <h4 class="form-header h4">购买方信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">购买方名称：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${ywDjxx.gmfMc}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">纳税人识别号：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.gmfNsrsbh}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">联系电话：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.gmfDh != null and ywDjxx.gmfDh != '' ? ywDjxx.gmfDh : '未填写'}" th:class="${ywDjxx.gmfDh == null or ywDjxx.gmfDh == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">地址：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.gmfDz != null and ywDjxx.gmfDz != '' ? ywDjxx.gmfDz : '未填写'}" th:class="${ywDjxx.gmfDz == null or ywDjxx.gmfDz == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">银行账号：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.gmfYhzh != null and ywDjxx.gmfYhzh != '' ? ywDjxx.gmfYhzh : '未填写'}" th:class="${ywDjxx.gmfYhzh == null or ywDjxx.gmfYhzh == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 金额信息 -->
            <h4 class="form-header h4">金额信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">合计金额：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right" th:text="${#numbers.formatDecimal(ywDjxx.hjje, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">合计税额：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right" th:text="${#numbers.formatDecimal(ywDjxx.hjse, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">价税合计：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right price-highlight" th:text="${#numbers.formatDecimal(ywDjxx.jshj, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 剩余可匹配金额 -->
            <div class="row">
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">剩余金额：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right text-info" th:text="${#numbers.formatDecimal(ywDjxx.syHjje, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">剩余税额：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right text-info" th:text="${#numbers.formatDecimal(ywDjxx.syHjse, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">剩余价税合计：</label>
                        <div class="col-sm-6">
                            <p class="form-control-static text-right remaining-amount" th:text="${#numbers.formatDecimal(ywDjxx.syJshj, 1, 2)}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 其他信息 -->
            <h4 class="form-header h4">其他信息</h4>
            <div class="compact-section">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">红冲原因：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.hcYy}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">红冲次数：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.hcCs}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">业务系统ID：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.ywxtId != null and ywDjxx.ywxtId != '' ? ywDjxx.ywxtId : '未填写'}" th:class="${ywDjxx.ywxtId == null or ywDjxx.ywxtId == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">业务系统名称：</label>
                        <div class="col-sm-8">
                            <p class="form-control-static" th:text="${ywDjxx.ywxtMc != null and ywDjxx.ywxtMc != '' ? ywDjxx.ywxtMc : '未填写'}" th:class="${ywDjxx.ywxtMc == null or ywDjxx.ywxtMc == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${ywDjxx.bz != null and ywDjxx.bz != '' ? ywDjxx.bz : '无'}" th:class="${ywDjxx.bz == null or ywDjxx.bz == '' ? 'form-control-static empty-value' : 'form-control-static'}"></p>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <!-- 单据明细 -->
            <h4 class="form-header h4">单据明细</h4>
            <div class="row">
                <div class="col-sm-12">
                    <div th:if="${ywDjxx.ywDjmxList != null and !ywDjxx.ywDjmxList.isEmpty()}">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>商品名称</th>
                                    <th>商品编码</th>
                                    <th>规格型号</th>
                                    <th>单位</th>
                                    <th>数量</th>
                                    <th>单价</th>
                                    <th>金额</th>
                                    <th>税率</th>
                                    <th>税额</th>
                                    <th>匹配状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="item : ${ywDjxx.ywDjmxList}">
                                    <td th:text="${item.xh}"></td>
                                    <td th:text="${item.spMc}"></td>
                                    <td th:text="${item.spBm}"></td>
                                    <td th:text="${item.ggxh}"></td>
                                    <td th:text="${item.dw}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.sl, 1, 12)}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.dj, 1, 12)}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.je, 1, 2)}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.slv, 1, 3)}"></td>
                                    <td th:text="${#numbers.formatDecimal(item.se, 1, 2)}"></td>
                                    <td th:text="${@dict.getLabel('swgx_yw_pp_status', item.ppZt)}"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div th:if="${ywDjxx.ywDjmxList == null or ywDjxx.ywDjmxList.isEmpty()}">
                        <p class="text-muted">暂无明细数据</p>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
