package com.ruoyi.swgx.controller.apiTest;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.swgx.service.apiTest.CompanyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业配置管理控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/swgx/config")
public class CompanyConfigController extends BaseController {

    @Autowired
    private CompanyConfigService companyConfigService;

    /**
     * 获取企业配置信息
     */
    @GetMapping("/company")
    public AjaxResult getCompanyConfig() {
        try {
            CompanyConfigService.CompanyInfo companyInfo = companyConfigService.getCompanyInfo();
            
            Map<String, Object> result = new HashMap<>();
            result.put("企业名称", companyInfo.getName());
            result.put("纳税人识别号", companyInfo.getTaxNumber());
            result.put("企业唯一标识", companyInfo.getUniqueId());
            result.put("配置状态", companyConfigService.validateCompanyConfig() ? "正常" : "异常");
            
            return AjaxResult.success("获取成功", result);
            
        } catch (Exception e) {
            log.error("获取企业配置失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取企业唯一标识
     */
    @GetMapping("/company/uniqueId")
    public AjaxResult getCompanyUniqueId() {
        try {
            String uniqueId = companyConfigService.getCompanyUniqueId();
            
            Map<String, Object> result = new HashMap<>();
            result.put("企业唯一标识", uniqueId);
            result.put("说明", "企业唯一标识已缓存，后续调用将直接返回缓存值");
            
            return AjaxResult.success("获取成功", result);
            
        } catch (Exception e) {
            log.error("获取企业唯一标识失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    // 移除了刷新企业唯一标识缓存的接口

    /**
     * 验证企业配置
     */
    @GetMapping("/company/validate")
    public AjaxResult validateCompanyConfig() {
        try {
            boolean isValid = companyConfigService.validateCompanyConfig();
            
            Map<String, Object> result = new HashMap<>();
            result.put("配置状态", isValid ? "正常" : "异常");
            
            if (isValid) {
                result.put("说明", "企业配置验证通过，可以正常使用API功能");
                return AjaxResult.success("验证通过", result);
            } else {
                result.put("说明", "企业配置验证失败，请检查配置项");
                return AjaxResult.error("验证失败", result);
            }
            
        } catch (Exception e) {
            log.error("验证企业配置失败", e);
            return AjaxResult.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置帮助信息
     */
    @GetMapping("/help")
    public AjaxResult getConfigHelp() {
        Map<String, Object> help = new HashMap<>();
        
        // 配置说明
        Map<String, String> configItems = new HashMap<>();
        configItems.put("swgx.api.company.name", "企业名称，默认：河北九赋");
        configItems.put("swgx.api.company.tax-number", "纳税人识别号，必填");
        configItems.put("swgx.api.company.unique-id", "企业唯一标识，可选（为空时自动获取）");
        configItems.put("swgx.api.company.cache-enabled", "是否启用缓存，默认：true");
        configItems.put("swgx.api.company.cache-ttl", "缓存过期时间（秒），默认：86400");
        configItems.put("swgx.api.company.auto-fetch-unique-id", "是否自动获取企业唯一标识，默认：true");
        
        help.put("配置项说明", configItems);
        
        // 配置示例
        Map<String, Object> example = new HashMap<>();
        example.put("swgx.api.company.name", "河北九赋");
        example.put("swgx.api.company.tax-number", "your-tax-number");
        example.put("swgx.api.company.unique-id", "your-unique-id");
        example.put("swgx.api.company.cache-enabled", true);
        example.put("swgx.api.company.cache-ttl", 86400);
        example.put("swgx.api.company.auto-fetch-unique-id", true);
        
        help.put("配置示例", example);
        
        // 使用说明
        Map<String, String> usage = new HashMap<>();
        usage.put("推荐配置", "直接配置企业唯一标识，避免重复API调用");
        usage.put("自动获取", "如果不知道企业唯一标识，可启用自动获取功能");
        usage.put("缓存机制", "企业唯一标识会自动缓存24小时，提高性能");
        usage.put("单企业模式", "系统只支持单个企业，所有发票都使用同一企业信息");
        
        help.put("使用说明", usage);
        
        return AjaxResult.success("配置帮助", help);
    }
}
