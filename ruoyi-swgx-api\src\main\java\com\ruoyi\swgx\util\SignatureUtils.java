package com.ruoyi.swgx.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

/**
 * 百旺金穗云API签名工具类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class SignatureUtils {

    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String SHA256 = "SHA-256";

    /**
     * 生成API签名
     * 
     * @param method HTTP方法
     * @param uri 请求URI
     * @param queryString 查询字符串
     * @param headers 请求头
     * @param body 请求体
     * @param appSecret 应用密钥
     * @return 签名字符串
     */
    public static String generateSignature(String method, String uri, String queryString, 
                                         String headers, String body, String appSecret) {
        try {
            // 构建待签名字符串
            StringBuilder stringToSign = new StringBuilder();
            stringToSign.append(method.toUpperCase()).append("\n");
            stringToSign.append(uri).append("\n");
            stringToSign.append(queryString != null ? queryString : "").append("\n");
            stringToSign.append(headers).append("\n");
            stringToSign.append(body != null ? body : "");

            String signString = stringToSign.toString();
            log.debug("待签名字符串: {}", signString);

            // 使用HMAC-SHA256进行签名
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);
            
            byte[] signBytes = mac.doFinal(signString.getBytes(StandardCharsets.UTF_8));
            String signature = Base64.getEncoder().encodeToString(signBytes);
            
            log.debug("生成签名: {}", signature);
            return signature;
            
        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 生成请求体的SHA256哈希值
     * 
     * @param body 请求体
     * @return SHA256哈希值（Base64编码）
     */
    public static String sha256Hash(String body) {
        try {
            if (body == null || body.isEmpty()) {
                body = "";
            }
            
            MessageDigest digest = MessageDigest.getInstance(SHA256);
            byte[] hashBytes = digest.digest(body.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashBytes);
            
        } catch (Exception e) {
            log.error("生成SHA256哈希失败", e);
            throw new RuntimeException("生成SHA256哈希失败", e);
        }
    }

    /**
     * 构建规范化的请求头字符串
     * 
     * @param contentType Content-Type
     * @param date Date
     * @param host Host
     * @param nonce x-htjs-nonce
     * @param ua x-htjs-ua
     * @return 规范化的请求头字符串
     */
    public static String buildCanonicalHeaders(String contentType, String date, String host, 
                                             String nonce, String ua) {
        StringBuilder headers = new StringBuilder();
        headers.append("content-type:").append(contentType).append("\n");
        headers.append("date:").append(date).append("\n");
        headers.append("host:").append(host).append("\n");
        headers.append("x-htjs-nonce:").append(nonce).append("\n");
        headers.append("x-htjs-ua:").append(ua);
        
        return headers.toString();
    }

    /**
     * 生成Authorization头
     * 
     * @param appId 应用ID
     * @param signature 签名
     * @param signedHeaders 已签名的请求头列表
     * @return Authorization头值
     */
    public static String buildAuthorizationHeader(String appId, String signature, String signedHeaders) {
        return String.format("HTJS-HMAC-SHA256 Credential=%s, SignedHeaders=%s, Signature=%s",
                appId, signedHeaders, signature);
    }

    /**
     * 获取已签名的请求头列表
     * 
     * @return 已签名的请求头列表
     */
    public static String getSignedHeaders() {
        return "content-type;date;host;x-htjs-nonce;x-htjs-ua";
    }
}
