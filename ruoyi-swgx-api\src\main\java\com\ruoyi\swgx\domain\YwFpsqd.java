package com.ruoyi.swgx.domain;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 发票申请单信息对象 swgx_yw_fpsqd
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class YwFpsqd extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请单ID */
    private String swguid;

    /** 申请日期 */
    @Excel(name = "申请日期")
    private String swfpdate;

    /** 重试标志 */
    private Long swcsflag;

    /** 企业ID */
    @Excel(name = "企业ID")
    private String qyid;

    /** 业务单据代码 */
    private String ywdjdm;

    /** 是否单据 */
    private Long sfdj;

    /** 单据编号 */
    @Excel(name = "单据编号")
    private String djbh;

    /** 发票类型代码 */
    private String fplxdm;

    /** 开票类型 */
    private Long kplx;

    /** 开票状态 */
    private Long kpzt;

    /** 发票代码 */
    @Excel(name = "发票代码")
    private String fpdm;

    /** 发票号码 */
    @Excel(name = "发票号码")
    private String fphm;

    /** 开票日期 */
    @Excel(name = "开票日期")
    private String kprq;

    /** 特殊票种 */
    private String tspz;

    /** 征收方式 */
    private Long zsfs;

    /** 清单标志 */
    private Long qdbz;

    /** 销方名称 */
    @Excel(name = "销方名称")
    private String xsfmc;

    /** 销方税号 */
    @Excel(name = "销方税号")
    private String xsfnsrsbh;

    /** 销方地址 */
    private String xsfdz;

    /** 销方电话 */
    private String xsfdh;

    /** 销方银行 */
    private String xsfyh;

    /** 销方账号 */
    private String xsfzh;

    /** 购方名称 */
    @Excel(name = "购方名称")
    private String gmfmc;

    /** 购方税号 */
    @Excel(name = "购方税号")
    private String gmfnsrsbh;

    /** 购方地址 */
    private String gmfdz;

    /** 购方电话 */
    private String gmfdh;

    /** 购方银行 */
    private String gmfyh;

    /** 购方账号 */
    private String gmfzh;

    /** 购方手机 */
    private String gmfmobile;

    /** 购方邮箱 */
    private String gmfemail;

    /** 开票人 */
    private String kpr;

    /** 收款人 */
    private String skr;

    /** 复核人 */
    private String fhr;

    /** 申请人 */
    private String sqr;

    /** 价税合计 */
    private BigDecimal jshj;

    /** 合计金额 */
    private BigDecimal hjje;

    /** 合计税额 */
    private BigDecimal hjse;

    /** 备注 */
    private String bz;

    /** 附加要素 */
    private String fjys;

    /** 原发票代码 */
    private String yfpdm;

    /** 原发票号码 */
    private String yfphm;

    /** 库存额 */
    private String kce;

    /** 税收来源 */
    private String sslkjly;

    /** 预开票日期 */
    private String ykprq;

    /** 原发票类型代码 */
    private String yfplxdm;

    /** 冲红原因代码 */
    private String chyydm;

    /** 审核标志 */
    private Long checkaudit;

    /** 代理账号 */
    private String dlzh;

    /** 证件号码 */
    private String zjhm;

    /** 是否为增值税发票 */
    private String sfwzzfp;

    /** 纸票发票凭证代码 */
    private String zpfppzdm;

    /** 购买方自然人标识 */
    private String gmfzrrbs;

    /** 商品分类确认 */
    private String spflxconfirm;

    /** 是否展示购买方银行账号 */
    private String sfzsgmfyhzh;

    /** 是否展示销售方银行账号 */
    private String sfzsxsfyhzh;

    /** 是否展示购买方地址电话 */
    private String sfzsgmfdzdh;

    /** 是否展示销售方地址电话 */
    private String sfzsxsfdzdh;

    /** 农产品收购增值税类型 */
    private String ncpsgzjlx;

    /** 出口退税类型代码 */
    private String cktslxdm;

    /** 云端数据 */
    private String clouddata;

    /** 扩展字段1 */
    private String kz;

    /** 扩展字段2 */
    private String kz1;

    /** 错误信息 */
    private String errmsg;

    /** 创建者 */
    private String create_by;

    /** 创建时间 */
    private Date create_time;

    /** 更新者 */
    private String update_by;

    /** 更新时间 */
    private Date update_time;

    /** 发票申请单明细信息 */
    private List<YwFpsqdmx> ywFpsqdmxList;

    public void setSwguid(String swguid) 
    {
        this.swguid = swguid;
    }

    public String getSwguid() 
    {
        return swguid;
    }

    public void setSwfpdate(String swfpdate) 
    {
        this.swfpdate = swfpdate;
    }

    public String getSwfpdate() 
    {
        return swfpdate;
    }

    public void setSwcsflag(Long swcsflag) 
    {
        this.swcsflag = swcsflag;
    }

    public Long getSwcsflag() 
    {
        return swcsflag;
    }

    public void setQyid(String qyid) 
    {
        this.qyid = qyid;
    }

    public String getQyid() 
    {
        return qyid;
    }

    public void setYwdjdm(String ywdjdm) 
    {
        this.ywdjdm = ywdjdm;
    }

    public String getYwdjdm() 
    {
        return ywdjdm;
    }

    public void setSfdj(Long sfdj) 
    {
        this.sfdj = sfdj;
    }

    public Long getSfdj() 
    {
        return sfdj;
    }

    public void setDjbh(String djbh) 
    {
        this.djbh = djbh;
    }

    public String getDjbh() 
    {
        return djbh;
    }

    public void setFplxdm(String fplxdm) 
    {
        this.fplxdm = fplxdm;
    }

    public String getFplxdm() 
    {
        return fplxdm;
    }

    public void setKplx(Long kplx) 
    {
        this.kplx = kplx;
    }

    public Long getKplx() 
    {
        return kplx;
    }

    public void setKpzt(Long kpzt) 
    {
        this.kpzt = kpzt;
    }

    public Long getKpzt() 
    {
        return kpzt;
    }

    public void setFpdm(String fpdm) 
    {
        this.fpdm = fpdm;
    }

    public String getFpdm() 
    {
        return fpdm;
    }

    public void setFphm(String fphm) 
    {
        this.fphm = fphm;
    }

    public String getFphm() 
    {
        return fphm;
    }

    public void setKprq(String kprq) 
    {
        this.kprq = kprq;
    }

    public String getKprq() 
    {
        return kprq;
    }

    public void setTspz(String tspz) 
    {
        this.tspz = tspz;
    }

    public String getTspz() 
    {
        return tspz;
    }

    public void setZsfs(Long zsfs) 
    {
        this.zsfs = zsfs;
    }

    public Long getZsfs() 
    {
        return zsfs;
    }

    public void setQdbz(Long qdbz) 
    {
        this.qdbz = qdbz;
    }

    public Long getQdbz() 
    {
        return qdbz;
    }

    public void setXsfmc(String xsfmc) 
    {
        this.xsfmc = xsfmc;
    }

    public String getXsfmc() 
    {
        return xsfmc;
    }

    public void setXsfnsrsbh(String xsfnsrsbh) 
    {
        this.xsfnsrsbh = xsfnsrsbh;
    }

    public String getXsfnsrsbh() 
    {
        return xsfnsrsbh;
    }

    public void setXsfdz(String xsfdz) 
    {
        this.xsfdz = xsfdz;
    }

    public String getXsfdz() 
    {
        return xsfdz;
    }

    public void setXsfdh(String xsfdh) 
    {
        this.xsfdh = xsfdh;
    }

    public String getXsfdh() 
    {
        return xsfdh;
    }

    public void setXsfyh(String xsfyh) 
    {
        this.xsfyh = xsfyh;
    }

    public String getXsfyh() 
    {
        return xsfyh;
    }

    public void setXsfzh(String xsfzh) 
    {
        this.xsfzh = xsfzh;
    }

    public String getXsfzh() 
    {
        return xsfzh;
    }

    public void setGmfmc(String gmfmc) 
    {
        this.gmfmc = gmfmc;
    }

    public String getGmfmc() 
    {
        return gmfmc;
    }

    public void setGmfnsrsbh(String gmfnsrsbh) 
    {
        this.gmfnsrsbh = gmfnsrsbh;
    }

    public String getGmfnsrsbh() 
    {
        return gmfnsrsbh;
    }

    public void setGmfdz(String gmfdz) 
    {
        this.gmfdz = gmfdz;
    }

    public String getGmfdz() 
    {
        return gmfdz;
    }

    public void setGmfdh(String gmfdh) 
    {
        this.gmfdh = gmfdh;
    }

    public String getGmfdh() 
    {
        return gmfdh;
    }

    public void setGmfyh(String gmfyh) 
    {
        this.gmfyh = gmfyh;
    }

    public String getGmfyh() 
    {
        return gmfyh;
    }

    public void setGmfzh(String gmfzh) 
    {
        this.gmfzh = gmfzh;
    }

    public String getGmfzh() 
    {
        return gmfzh;
    }

    public void setGmfmobile(String gmfmobile) 
    {
        this.gmfmobile = gmfmobile;
    }

    public String getGmfmobile() 
    {
        return gmfmobile;
    }

    public void setGmfemail(String gmfemail) 
    {
        this.gmfemail = gmfemail;
    }

    public String getGmfemail() 
    {
        return gmfemail;
    }

    public void setKpr(String kpr) 
    {
        this.kpr = kpr;
    }

    public String getKpr() 
    {
        return kpr;
    }

    public void setSkr(String skr) 
    {
        this.skr = skr;
    }

    public String getSkr() 
    {
        return skr;
    }

    public void setFhr(String fhr) 
    {
        this.fhr = fhr;
    }

    public String getFhr() 
    {
        return fhr;
    }

    public void setSqr(String sqr) 
    {
        this.sqr = sqr;
    }

    public String getSqr() 
    {
        return sqr;
    }

    public void setJshj(BigDecimal jshj) 
    {
        this.jshj = jshj;
    }

    public BigDecimal getJshj() 
    {
        return jshj;
    }

    public void setHjje(BigDecimal hjje) 
    {
        this.hjje = hjje;
    }

    public BigDecimal getHjje() 
    {
        return hjje;
    }

    public void setHjse(BigDecimal hjse) 
    {
        this.hjse = hjse;
    }

    public BigDecimal getHjse() 
    {
        return hjse;
    }

    public void setBz(String bz) 
    {
        this.bz = bz;
    }

    public String getBz() 
    {
        return bz;
    }

    public void setFjys(String fjys) 
    {
        this.fjys = fjys;
    }

    public String getFjys() 
    {
        return fjys;
    }

    public void setYfpdm(String yfpdm) 
    {
        this.yfpdm = yfpdm;
    }

    public String getYfpdm() 
    {
        return yfpdm;
    }

    public void setYfphm(String yfphm) 
    {
        this.yfphm = yfphm;
    }

    public String getYfphm() 
    {
        return yfphm;
    }

    public void setKce(String kce) 
    {
        this.kce = kce;
    }

    public String getKce() 
    {
        return kce;
    }

    public void setSslkjly(String sslkjly) 
    {
        this.sslkjly = sslkjly;
    }

    public String getSslkjly() 
    {
        return sslkjly;
    }

    public void setYkprq(String ykprq) 
    {
        this.ykprq = ykprq;
    }

    public String getYkprq() 
    {
        return ykprq;
    }

    public void setYfplxdm(String yfplxdm) 
    {
        this.yfplxdm = yfplxdm;
    }

    public String getYfplxdm() 
    {
        return yfplxdm;
    }

    public void setChyydm(String chyydm) 
    {
        this.chyydm = chyydm;
    }

    public String getChyydm() 
    {
        return chyydm;
    }

    public void setCheckaudit(Long checkaudit) 
    {
        this.checkaudit = checkaudit;
    }

    public Long getCheckaudit() 
    {
        return checkaudit;
    }

    public void setDlzh(String dlzh) 
    {
        this.dlzh = dlzh;
    }

    public String getDlzh() 
    {
        return dlzh;
    }

    public void setZjhm(String zjhm) 
    {
        this.zjhm = zjhm;
    }

    public String getZjhm() 
    {
        return zjhm;
    }

    public void setSfwzzfp(String sfwzzfp) 
    {
        this.sfwzzfp = sfwzzfp;
    }

    public String getSfwzzfp() 
    {
        return sfwzzfp;
    }

    public void setZpfppzdm(String zpfppzdm) 
    {
        this.zpfppzdm = zpfppzdm;
    }

    public String getZpfppzdm() 
    {
        return zpfppzdm;
    }

    public void setGmfzrrbs(String gmfzrrbs) 
    {
        this.gmfzrrbs = gmfzrrbs;
    }

    public String getGmfzrrbs() 
    {
        return gmfzrrbs;
    }

    public void setSpflxconfirm(String spflxconfirm) 
    {
        this.spflxconfirm = spflxconfirm;
    }

    public String getSpflxconfirm() 
    {
        return spflxconfirm;
    }

    public void setSfzsgmfyhzh(String sfzsgmfyhzh) 
    {
        this.sfzsgmfyhzh = sfzsgmfyhzh;
    }

    public String getSfzsgmfyhzh() 
    {
        return sfzsgmfyhzh;
    }

    public void setSfzsxsfyhzh(String sfzsxsfyhzh) 
    {
        this.sfzsxsfyhzh = sfzsxsfyhzh;
    }

    public String getSfzsxsfyhzh() 
    {
        return sfzsxsfyhzh;
    }

    public void setSfzsgmfdzdh(String sfzsgmfdzdh) 
    {
        this.sfzsgmfdzdh = sfzsgmfdzdh;
    }

    public String getSfzsgmfdzdh() 
    {
        return sfzsgmfdzdh;
    }

    public void setSfzsxsfdzdh(String sfzsxsfdzdh) 
    {
        this.sfzsxsfdzdh = sfzsxsfdzdh;
    }

    public String getSfzsxsfdzdh() 
    {
        return sfzsxsfdzdh;
    }

    public void setNcpsgzjlx(String ncpsgzjlx) 
    {
        this.ncpsgzjlx = ncpsgzjlx;
    }

    public String getNcpsgzjlx() 
    {
        return ncpsgzjlx;
    }

    public void setCktslxdm(String cktslxdm) 
    {
        this.cktslxdm = cktslxdm;
    }

    public String getCktslxdm() 
    {
        return cktslxdm;
    }

    public void setClouddata(String clouddata) 
    {
        this.clouddata = clouddata;
    }

    public String getClouddata() 
    {
        return clouddata;
    }

    public void setKz(String kz) 
    {
        this.kz = kz;
    }

    public String getKz() 
    {
        return kz;
    }

    public void setKz1(String kz1) 
    {
        this.kz1 = kz1;
    }

    public String getKz1() 
    {
        return kz1;
    }

    public void setErrmsg(String errmsg) 
    {
        this.errmsg = errmsg;
    }

    public String getErrmsg() 
    {
        return errmsg;
    }

    public void setCreate_by(String create_by) 
    {
        this.create_by = create_by;
    }

    public String getCreate_by() 
    {
        return create_by;
    }

    public void setCreate_time(Date create_time) 
    {
        this.create_time = create_time;
    }

    public Date getCreate_time() 
    {
        return create_time;
    }

    public void setUpdate_by(String update_by) 
    {
        this.update_by = update_by;
    }

    public String getUpdate_by() 
    {
        return update_by;
    }

    public void setUpdate_time(Date update_time) 
    {
        this.update_time = update_time;
    }

    public Date getUpdate_time() 
    {
        return update_time;
    }

    public List<YwFpsqdmx> getYwFpsqdmxList()
    {
        return ywFpsqdmxList;
    }

    public void setYwFpsqdmxList(List<YwFpsqdmx> ywFpsqdmxList)
    {
        this.ywFpsqdmxList = ywFpsqdmxList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("swguid", getSwguid())
            .append("swfpdate", getSwfpdate())
            .append("swcsflag", getSwcsflag())
            .append("qyid", getQyid())
            .append("ywdjdm", getYwdjdm())
            .append("sfdj", getSfdj())
            .append("djbh", getDjbh())
            .append("fplxdm", getFplxdm())
            .append("kplx", getKplx())
            .append("kpzt", getKpzt())
            .append("fpdm", getFpdm())
            .append("fphm", getFphm())
            .append("kprq", getKprq())
            .append("tspz", getTspz())
            .append("zsfs", getZsfs())
            .append("qdbz", getQdbz())
            .append("xsfmc", getXsfmc())
            .append("xsfnsrsbh", getXsfnsrsbh())
            .append("xsfdz", getXsfdz())
            .append("xsfdh", getXsfdh())
            .append("xsfyh", getXsfyh())
            .append("xsfzh", getXsfzh())
            .append("gmfmc", getGmfmc())
            .append("gmfnsrsbh", getGmfnsrsbh())
            .append("gmfdz", getGmfdz())
            .append("gmfdh", getGmfdh())
            .append("gmfyh", getGmfyh())
            .append("gmfzh", getGmfzh())
            .append("gmfmobile", getGmfmobile())
            .append("gmfemail", getGmfemail())
            .append("kpr", getKpr())
            .append("skr", getSkr())
            .append("fhr", getFhr())
            .append("sqr", getSqr())
            .append("jshj", getJshj())
            .append("hjje", getHjje())
            .append("hjse", getHjse())
            .append("bz", getBz())
            .append("fjys", getFjys())
            .append("yfpdm", getYfpdm())
            .append("yfphm", getYfphm())
            .append("kce", getKce())
            .append("sslkjly", getSslkjly())
            .append("ykprq", getYkprq())
            .append("yfplxdm", getYfplxdm())
            .append("chyydm", getChyydm())
            .append("checkaudit", getCheckaudit())
            .append("dlzh", getDlzh())
            .append("zjhm", getZjhm())
            .append("sfwzzfp", getSfwzzfp())
            .append("zpfppzdm", getZpfppzdm())
            .append("gmfzrrbs", getGmfzrrbs())
            .append("spflxconfirm", getSpflxconfirm())
            .append("sfzsgmfyhzh", getSfzsgmfyhzh())
            .append("sfzsxsfyhzh", getSfzsxsfyhzh())
            .append("sfzsgmfdzdh", getSfzsgmfdzdh())
            .append("sfzsxsfdzdh", getSfzsxsfdzdh())
            .append("ncpsgzjlx", getNcpsgzjlx())
            .append("cktslxdm", getCktslxdm())
            .append("clouddata", getClouddata())
            .append("kz", getKz())
            .append("kz1", getKz1())
            .append("errmsg", getErrmsg())
            .append("create_by", getCreate_by())
            .append("create_time", getCreate_time())
            .append("update_by", getUpdate_by())
            .append("update_time", getUpdate_time())
            .append("ywFpsqdmxList", getYwFpsqdmxList())
            .toString();
    }
}
