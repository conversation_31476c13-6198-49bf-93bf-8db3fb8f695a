package com.ruoyi.swgx.service;

/**
 * 匹配状态管理服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IMatchStatusService {
    
    /**
     * 更新单据匹配状态
     * 根据单据下所有明细的匹配状态，计算并更新单据的整体匹配状态
     * 
     * @param djId 单据ID
     * @return 更新后的匹配状态
     */
    String updateDocumentMatchStatus(String djId);
    
    /**
     * 更新发票匹配状态
     * 根据发票下所有明细的匹配状态，计算并更新发票的整体匹配状态
     * 
     * @param fpId 发票ID
     * @return 更新后的匹配状态
     */
    String updateInvoiceMatchStatus(String fpId);
    
    /**
     * 批量更新单据匹配状态
     * 
     * @param djIds 单据ID列表
     */
    void batchUpdateDocumentMatchStatus(String[] djIds);
    
    /**
     * 批量更新发票匹配状态
     * 
     * @param fpIds 发票ID列表
     */
    void batchUpdateInvoiceMatchStatus(String[] fpIds);
    
    /**
     * 检查数据一致性
     * 检查主表和明细表的匹配状态是否一致
     * 
     * @return 不一致的记录数量
     */
    int checkDataConsistency();
    
    /**
     * 修复数据一致性
     * 修复主表和明细表匹配状态不一致的问题
     * 
     * @return 修复的记录数量
     */
    int fixDataConsistency();
}
