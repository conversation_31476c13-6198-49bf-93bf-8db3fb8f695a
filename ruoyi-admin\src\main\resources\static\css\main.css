/**
 * 首页样式
 * 河北九赋后台管理系统
 */

/* 统计卡片样式 */
.ibox {
    clear: both;
    margin-bottom: 25px;
    margin-top: 0;
    padding: 0;
}

.ibox.float-e-margins {
    margin-top: 25px;
}

.ibox-title {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background-color: #f5f5f6;
    border-color: #e7eaec;
    border-image: none;
    border-style: solid solid none;
    border-width: 2px 0 0;
    color: inherit;
    margin-bottom: 0;
    padding: 14px 15px 7px;
    min-height: 48px;
}

.ibox-content {
    background-color: #fff;
    color: inherit;
    padding: 15px 20px 20px 20px;
    border-color: #e7eaec;
    border-image: none;
    border-style: solid solid none;
    border-width: 1px 0;
}

.ibox-footer {
    color: inherit;
    border-top: 1px solid #e7eaec;
    font-size: 90%;
    background: #f5f5f6;
    padding: 10px 15px;
}

/* 统计数字样式 */
.no-margins {
    margin: 0;
}

.stat-percent {
    float: right;
    margin-top: 5px;
}

/* 公司logo样式 */
.company-logo-circle {
    width: 80px;
    height: 80px;
}

/* 公告列表样式 */
.feed-activity-list {
    max-height: 500px;
    overflow-y: auto;
}

.feed-element {
    border-bottom: 1px solid #e7eaec;
    padding: 15px 0;
}

.feed-element:last-child {
    border-bottom: none;
}

.feed-element .feed-date {
    color: #c1c1c1;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.feed-element .feed-title {
    font-size: 14px;
    font-weight: 600;
    color: #676a6c;
    margin-bottom: 5px;
    cursor: pointer;
}

.feed-element .feed-title:hover {
    color: #1ab394;
}

.feed-element .feed-content {
    color: #999;
    font-size: 12px;
    line-height: 1.5;
    margin-bottom: 8px;
}

.feed-element .feed-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: #c1c1c1;
}

.feed-element .feed-type {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.feed-element .feed-type.type-1 {
    background: #e8f4fd;
    color: #1c84c6;
}

.feed-element .feed-type.type-2 {
    background: #fef5e7;
    color: #f8ac59;
}

/* 系统信息样式 */
.stats-label {
    color: #676a6c;
    font-size: 10px;
    text-transform: uppercase;
    font-weight: 600;
}

/* 工具栏样式 */
.ibox-tools {
    display: block;
    float: right;
    margin-top: 0;
    position: relative;
    padding: 0;
    text-align: left;
}

.ibox-tools a {
    cursor: pointer;
    margin-left: 5px;
    color: #c4c4c4;
}

.ibox-tools a:hover {
    color: #1ab394;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
    .ibox-content h1, .ibox-content h2 {
        font-size: 1.5rem;
    }

    .stats-label {
        font-size: 9px;
    }

    .feed-element {
        padding: 10px 0;
    }

    .company-logo-circle {
        width: 60px;
        height: 60px;
    }
}