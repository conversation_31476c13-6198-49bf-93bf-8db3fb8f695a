package com.ruoyi.swgx.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 缓存配置
 * 支持Redis和内存缓存的自动切换
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Configuration
public class CacheConfig extends CachingConfigurerSupport {

    @PostConstruct
    public void init() {
        log.info("百旺金穗云API缓存配置已初始化");
    }

    /**
     * 缓存管理器 - 优先使用Redis，不可用时降级为内存缓存
     */
    @Bean
    @ConditionalOnMissingBean
    @Override
    public CacheManager cacheManager() {
        try {
            // 尝试创建Redis缓存管理器
            return createRedisCacheManager();
        } catch (Exception e) {
            log.warn("Redis不可用，降级使用内存缓存: {}", e.getMessage());
            return createMemoryCacheManager();
        }
    }

    /**
     * 创建Redis缓存管理器
     */
    private CacheManager createRedisCacheManager() {
        // 这里需要Redis依赖，如果没有则会抛出异常
        // 实际项目中应该检查Redis连接工厂是否可用
        throw new RuntimeException("Redis缓存暂不可用，使用内存缓存");
    }

    /**
     * 创建内存缓存管理器
     */
    private CacheManager createMemoryCacheManager() {
        log.info("使用内存缓存管理器");
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();

        // 设置缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
            "swgx:company:uniqueSign",
            "swgx:company:uniqueId"
        ));

        // 允许空值
        cacheManager.setAllowNullValues(false);

        return cacheManager;
    }

    /**
     * 缓存键生成器
     */
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getSimpleName()).append(":");
            sb.append(method.getName()).append(":");
            for (Object param : params) {
                if (param != null) {
                    sb.append(param.toString()).append(":");
                }
            }
            return sb.toString();
        };
    }
}
