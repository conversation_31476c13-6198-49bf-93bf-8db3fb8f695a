package com.ruoyi.swgx.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 百旺金穗云API通用响应
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SwgxApiResponse<T> {

    /**
     * 返回代码（0成功，其它失败）
     */
    private Integer code;

    /**
     * 返回信息（成功/失败）
     */
    private String message;

    /**
     * 额外的消息字段
     */
    private String msg;

    /**
     * 描述（失败原因具体描述）
     */
    private String renson;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return Integer.valueOf(0).equals(code);
    }

    /**
     * 创建成功响应
     */
    public static <T> SwgxApiResponse<T> success(T data) {
        SwgxApiResponse<T> response = new SwgxApiResponse<>();
        response.setCode(0);
        response.setMessage("成功");
        response.setRenson("");
        response.setData(data);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static <T> SwgxApiResponse<T> error(Integer code, String message, String reason) {
        SwgxApiResponse<T> response = new SwgxApiResponse<>();
        response.setCode(code);
        response.setMessage(message);
        response.setRenson(reason);
        return response;
    }
}
