<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发票申请单信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>企业ID：</label>
                                <input type="text" name="qyid"/>
                            </li>
                            <li>
                                <label>单据编号：</label>
                                <input type="text" name="djbh"/>
                            </li>
                            <li>
                                <label>发票代码：</label>
                                <input type="text" name="fpdm"/>
                            </li>
                            <li>
                                <label>发票号码：</label>
                                <input type="text" name="fphm"/>
                            </li>
                            <li>
                                <label>开票日期：</label>
                                <input type="text" class="time-input" placeholder="请选择开票日期" name="kprq"/>
                            </li>
                            <li>
                                <label>销方名称：</label>
                                <input type="text" name="xsfmc"/>
                            </li>
                            <li>
                                <label>销方税号：</label>
                                <input type="text" name="xsfnsrsbh"/>
                            </li>
                            <li>
                                <label>购方名称：</label>
                                <input type="text" name="gmfmc"/>
                            </li>
                            <li>
                                <label>购方税号：</label>
                                <input type="text" name="gmfnsrsbh"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="swgx:fpsqd:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="swgx:fpsqd:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="swgx:fpsqd:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="swgx:fpsqd:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('swgx:fpsqd:edit')}]];
        var removeFlag = [[${@permission.hasPermi('swgx:fpsqd:remove')}]];
        var kplxDatas = [[${@dict.getType('swgx_kplx')}]];
        var kpztDatas = [[${@dict.getType('swgx_fpzt')}]];
        var prefix = ctx + "swgx/fpsqd";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                detailUrl: prefix + "/detail/{id}",
                modalName: "发票申请单信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'swguid',
                    title: '申请单ID',
                    visible: false
                },
                {
                    field: 'swfpdate',
                    title: '申请日期'
                },
                {
                    field: 'qyid',
                    title: '企业ID'
                },
                {
                    field: 'djbh',
                    title: '单据编号'
                },
                {
                    field: 'fpdm',
                    title: '发票代码'
                },
                {
                    field: 'fphm',
                    title: '发票号码'
                },
                {
                    field: 'kprq',
                    title: '开票日期'
                },
                {
                    field: 'xsfmc',
                    title: '销方名称'
                },
                {
                    field: 'xsfnsrsbh',
                    title: '销方税号'
                },
                {
                    field: 'gmfmc',
                    title: '购方名称'
                },
                {
                    field: 'gmfnsrsbh',
                    title: '购方税号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.swguid + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.swguid + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.swguid + '\')"><i class="fa fa-search"></i>详情</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>