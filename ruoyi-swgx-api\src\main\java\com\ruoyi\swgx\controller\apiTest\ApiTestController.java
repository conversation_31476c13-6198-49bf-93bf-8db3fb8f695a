package com.ruoyi.swgx.controller.apiTest;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.swgx.common.client.SwgxHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 通用API测试控制器
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Slf4j
@Controller
@RequestMapping("/swgx/api-test")
public class ApiTestController extends BaseController {

    @Autowired
    private SwgxHttpClient swgxHttpClient;


    /**
     * 通用API测试页面
     */
    @GetMapping
    public String index() {
        return "swgx/api-test";
    }

    /**
     * 原始API调用接口 - 直接调用第三方API并返回原始响应
     */
    @PostMapping("/raw")
    @ResponseBody
    public ResponseEntity<String> rawApiCall(@RequestBody Map<String, String> request) {
        try {
            String apiPath = request.get("apiPath");
            String requestBody = request.get("requestBody");

            log.info("原始API调用: {} -> {}", apiPath, requestBody);

            // 直接调用SwgxHttpClient的底层方法，绕过异常处理
            String rawResponse = swgxHttpClient.callRawApi(apiPath, requestBody);

            return ResponseEntity.ok()
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(rawResponse);

        } catch (Exception e) {
            log.error("原始API调用失败", e);
            String errorResponse = String.format(
                    "{\"error\":\"API调用失败\",\"message\":\"%s\"}",
                    e.getMessage().replace("\"", "\\\"")
            );
            return ResponseEntity.status(500)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(errorResponse);
        }
    }


    /**
     * 获取API接口模板
     */
    @GetMapping("/template/{templateName}")
    @ResponseBody
    public AjaxResult getApiTemplate(@PathVariable String templateName) {
        try {
            Map<String, Object> template = new HashMap<>();

            switch (templateName) {
                case "applyInvoice":
                    template.put("path", "https://api.baiwangjs.com/swgx-saas/agentinvoiceservice-cloudservice-cloudservice/agentiscloud/cloud/v2/applyInvoice");
                    template.put("method", "POST");
                    template.put("description", "发票开具申请");

                    Map<String, Object> invoiceBody = new HashMap<>();
                    invoiceBody.put("fplxdm", "02");
                    invoiceBody.put("kplx", "0");
                    invoiceBody.put("dlzh", "13800000001");
                    invoiceBody.put("gmfMc", "北京测试科技有限公司");
                    invoiceBody.put("gmfNsrsbh", "91110000*********X");
                    invoiceBody.put("gmfDz", "北京市朝阳区测试大街123号");
                    invoiceBody.put("gmfDh", "010-12345678");
                    invoiceBody.put("kpr", "系统开票");
                    invoiceBody.put("sqr", "系统申请");
                    invoiceBody.put("bz", "测试发票开具");

                    Map<String, Object> detail = new HashMap<>();
                    detail.put("xh", 1);
                    detail.put("fphxz", "0");
                    detail.put("spmc", "软件开发服务");
                    detail.put("spbm", "3040407990000000000");
                    detail.put("sl", "1");
                    detail.put("dj", "10000.00");
                    detail.put("je", "10000.00");
                    detail.put("slv", "0.06");
                    detail.put("lslbs", "0");

                    invoiceBody.put("details", new Object[]{detail});
                    template.put("body", invoiceBody);
                    break;

                case "queryUniqueSign":
                    template.put("path", "https://api.baiwangjs.com/swgx-saas/agentinvoiceservice-cloudservice-cloudservice/agentiscloud/cloud/queryUniqueSign");
                    template.put("method", "POST");
                    template.put("description", "查询企业唯一标识");

                    Map<String, Object> companyBody = new HashMap<>();
                    companyBody.put("nsrmc", "河北九赋");
                    companyBody.put("nsrsbh", "*********");
                    template.put("body", companyBody);
                    break;

                default:
                    return AjaxResult.error("未找到指定的模板: " + templateName);
            }

            return AjaxResult.success("获取成功", template);

        } catch (Exception e) {
            log.error("获取API模板失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }
}
