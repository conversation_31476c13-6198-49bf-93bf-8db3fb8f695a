
$(function() {
    validateKickout();
    validateRule();
    $('.imgcode').click(function() {
        var url = ctx + "captcha/captchaImage?type=" + captchaType + "&s=" + Math.random();
        $(".imgcode").attr("src", url);
    });
});

function login() {
    var username = $.common.trim($("input[name='username']").val());
    var password = $.common.trim($("input[name='password']").val());
    var validateCode = $("input[name='validateCode']").val();
    var rememberMe = $("input[name='rememberme']").is(':checked');
    if($.common.isEmpty(validateCode) && captchaEnabled) {
        $.modal.msg("请输入验证码");
        return false;
    }
    $.ajax({
        type: "post",
        url: ctx + "login",
        data: {
            "username": username,
            "password": password,
            "validateCode": validateCode,
            "rememberMe": rememberMe
        },
        beforeSend: function () {
            $.modal.loading($("#btnSubmit").data("loading"));
        },
        success: function(r) {
            if (r.code == web_status.SUCCESS) {
                location.href = ctx + 'index';
            } else {
                $('.imgcode').click();
                $(".code").val("");
                $.modal.msg(r.msg);
            }
            $.modal.closeLoading();
        }
    });
}

function validateRule() {
    var icon = "<i class='fa fa-times-circle'></i> ";
    $("#signupForm").validate({
        rules: {
            username: {
                required: true
            },
            password: {
                required: true
            }
        },
        messages: {
            username: {
                required: icon + "请输入您的用户名",
            },
            password: {
                required: icon + "请输入您的密码",
            }
        },
        submitHandler: function(form) {
            login();
        }
    })
}

function validateKickout() {
    if (getParam("kickout") == 1) {
        layer.alert("<font color='red'>您已在别处登录，请您修改密码或重新登录</font>", {
            icon: 0,
            title: "系统提示"
        },
        function(index) {
            //关闭弹窗
            layer.close(index);
            if (top != self) {
                top.location = self.location;
            } else {
                var url = location.search;
                if (url) {
                    var oldUrl = window.location.href;
                    var newUrl = oldUrl.substring(0, oldUrl.indexOf('?'));
                    self.location = newUrl;
                }
            }
        });
    }
}

function getParam(paramName) {
    var reg = new RegExp("(^|&)" + paramName + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return decodeURI(r[2]);
    return null;
}

/**
 * 河北九赋现代化登录页面增强功能
 */

// 页面加载完成后执行增强功能
$(function() {
    initModernLogin();
});

/**
 * 初始化现代化登录功能
 */
function initModernLogin() {
    addInputFocusEffects();
    addDebugInfo();
    addBackupClickHandler();
    addInputAnimations();
    addButtonLoadingEffect();
    addKeyboardSupport();
    enhanceFormValidation();
    addCaptchaAnimation();
}

/**
 * 添加输入框焦点效果
 */
function addInputFocusEffects() {
    $('.form-control').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });
}

/**
 * 添加调试信息
 */
function addDebugInfo() {
    console.log('河北九赋登录页面加载完成');
    console.log('表单ID:', $('#signupForm').length);
    console.log('按钮ID:', $('#btnSubmit').length);
    console.log('ctx:', typeof ctx !== 'undefined' ? ctx : 'undefined');
    console.log('captchaEnabled:', typeof captchaEnabled !== 'undefined' ? captchaEnabled : 'undefined');
}

/**
 * 添加备用点击事件处理
 */
function addBackupClickHandler() {
    $('#btnSubmit').on('click', function(e) {
        console.log('登录按钮被点击');
        if ($(this).attr('type') !== 'submit') {
            e.preventDefault();
            $('#signupForm').submit();
        }
    });
}

/**
 * 添加输入框动画效果
 */
function addInputAnimations() {
    $('.form-control').each(function() {
        var $input = $(this);
        var $parent = $input.parent();

        // 添加浮动标签效果
        if ($input.val()) {
            $parent.addClass('has-value');
        }

        $input.on('input', function() {
            if ($(this).val()) {
                $parent.addClass('has-value');
            } else {
                $parent.removeClass('has-value');
            }
        });
    });
}

/**
 * 添加登录按钮加载效果
 */
function addButtonLoadingEffect() {
    $('#signupForm').on('submit', function() {
        var $btn = $('#btnSubmit');
        var loadingText = $btn.data('loading') || '正在登录...';

        $btn.prop('disabled', true);
        $btn.html('<i class="fa fa-spinner fa-spin"></i> ' + loadingText);

        // 如果登录失败，恢复按钮状态
        setTimeout(function() {
            if ($btn.prop('disabled')) {
                resetLoginButton();
            }
        }, 10000); // 10秒后自动恢复
    });
}

/**
 * 重置登录按钮状态
 */
function resetLoginButton() {
    var $btn = $('#btnSubmit');
    $btn.prop('disabled', false);
    $btn.html('<i class="fa fa-sign-in"></i> 立即登录');
}

/**
 * 添加键盘事件支持
 */
function addKeyboardSupport() {
    // 回车键登录
    $('.form-control').on('keypress', function(e) {
        if (e.which === 13) { // Enter键
            $('#signupForm').submit();
        }
    });

    // ESC键清空表单
    $(document).on('keydown', function(e) {
        if (e.which === 27) { // ESC键
            $('.form-control').val('');
        }
    });
}

/**
 * 添加表单验证增强
 */
function enhanceFormValidation() {
    // 实时验证用户名
    $('input[name="username"]').on('input', function() {
        var username = $(this).val().trim();
        var $parent = $(this).parent();

        if (username.length > 0 && username.length < 3) {
            $parent.addClass('has-warning');
            $parent.removeClass('has-success');
        } else if (username.length >= 3) {
            $parent.addClass('has-success');
            $parent.removeClass('has-warning');
        } else {
            $parent.removeClass('has-warning has-success');
        }
    });

    // 实时验证密码
    $('input[name="password"]').on('input', function() {
        var password = $(this).val();
        var $parent = $(this).parent();

        if (password.length > 0 && password.length < 6) {
            $parent.addClass('has-warning');
            $parent.removeClass('has-success');
        } else if (password.length >= 6) {
            $parent.addClass('has-success');
            $parent.removeClass('has-warning');
        } else {
            $parent.removeClass('has-warning has-success');
        }
    });
}

/**
 * 添加验证码刷新动画
 */
function addCaptchaAnimation() {
    $('.captcha-image').on('click', function() {
        var $img = $(this);
        $img.addClass('refreshing');

        setTimeout(function() {
            $img.removeClass('refreshing');
        }, 500);
    });
}