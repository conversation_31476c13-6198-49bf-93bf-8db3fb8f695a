# 百旺金穗云API配置
swgx:
  api:
    # 基础配置
    base-url: https://api.baiwangjs.com/swgx-saas/agentinvoiceservice-cloudservice-cloudservice/agentiscloud
    app-id: ${SWGX_APP_ID:}
    app-secret: ${SWGX_APP_SECRET:}

    # HTTP客户端配置
    http:
      connect-timeout: 30000
      read-timeout: 60000
      max-connections: 200
      max-connections-per-route: 50

    # 企业配置（单企业模式）
    company:
      # 企业基本信息
      name: ${SWGX_COMPANY_NAME:河北九赋}
      tax-number: ${SWGX_COMPANY_TAX_NUMBER:}
      # 企业唯一标识（必须直接配置）
      unique-id: ${SWGX_COMPANY_UNIQUE_ID:}

    # 日志配置
    logging:
      enabled: true
      level: INFO

# 日志配置
  log:
    level: INFO                       # 日志级别
    retention-days: 30                # 日志保留天数
    performance-enabled: true         # 是否启用性能监控

  # 第三方数据库配置
  external-db:
    type: MYSQL                       # 数据库类型
    host: ${RED_FLUSH_EXTERNAL_DB_HOST:localhost}
    port: ${RED_FLUSH_EXTERNAL_DB_PORT:3306}
    database: ${RED_FLUSH_EXTERNAL_DB_NAME:external_db}
    username: ${RED_FLUSH_EXTERNAL_DB_USERNAME:root}
    password: ${RED_FLUSH_EXTERNAL_DB_PASSWORD:}
    max-connections: 10               # 最大连接数
    connection-timeout: 30000         # 连接超时时间（毫秒）
    query-timeout: 60000              # 查询超时时间（毫秒）

# 日志配置
logging:
  level:
    com.ruoyi.swgx: debug
    org.apache.http: info
