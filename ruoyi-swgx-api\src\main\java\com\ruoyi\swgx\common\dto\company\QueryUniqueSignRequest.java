package com.ruoyi.swgx.common.dto.company;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询企业唯一标识请求
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class QueryUniqueSignRequest {

    /**
     * 纳税人名称
     */
    @NotBlank(message = "纳税人名称不能为空")
    private String nsrmc;

    /**
     * 纳税人识别号
     */
    @NotBlank(message = "纳税人识别号不能为空")
    private String nsrsbh;

    /**
     * 构造函数
     */
    public QueryUniqueSignRequest() {
    }

    /**
     * 构造函数
     */
    public QueryUniqueSignRequest(String nsrmc, String nsrsbh) {
        this.nsrmc = nsrmc;
        this.nsrsbh = nsrsbh;
    }
}
