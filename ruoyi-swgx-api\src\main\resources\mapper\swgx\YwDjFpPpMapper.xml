<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.swgx.mapper.YwDjFpPpMapper">
    
    <resultMap type="YwDjFpPp" id="YwDjFpPpResult">
        <result property="id"    column="id"    />
        <result property="djId"    column="dj_id"    />
        <result property="djmxId"    column="djmx_id"    />
        <result property="fpId"    column="fp_id"    />
        <result property="fpmxId"    column="fpmx_id"    />
        <result property="ppSl"    column="pp_sl"    />
        <result property="ppJe"    column="pp_je"    />
        <result property="ppSe"    column="pp_se"    />
        <result property="ppDf"    column="pp_df"    />
        <result property="ppLx"    column="pp_lx"    />
        <result property="ppZt"    column="pp_zt"    />
        <result property="ppRq"    column="pp_rq"    />
        <result property="ppRy"    column="pp_ry"    />
        <result property="hcZt"    column="hc_zt"    />
        <result property="hcRq"    column="hc_rq"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectYwDjFpPpVo">
        select id, dj_id, djmx_id, fp_id, fpmx_id, pp_sl, pp_je, pp_se, pp_df, pp_lx, pp_zt, pp_rq, pp_ry, hc_zt, hc_rq, create_by, create_time, update_by, update_time from swgx_yw_dj_fp_pp
    </sql>

    <select id="selectYwDjFpPpList" parameterType="YwDjFpPp" resultMap="YwDjFpPpResult">
        <include refid="selectYwDjFpPpVo"/>
        <where>  
            <if test="djId != null  and djId != ''"> and dj_id = #{djId}</if>
            <if test="djmxId != null  and djmxId != ''"> and djmx_id = #{djmxId}</if>
            <if test="fpId != null  and fpId != ''"> and fp_id = #{fpId}</if>
            <if test="fpmxId != null  and fpmxId != ''"> and fpmx_id = #{fpmxId}</if>
            <if test="ppZt != null  and ppZt != ''"> and pp_zt = #{ppZt}</if>
            <if test="hcZt != null  and hcZt != ''"> and hc_zt = #{hcZt}</if>
        </where>
        order by pp_rq desc
    </select>
    
    <select id="selectYwDjFpPpById" parameterType="String" resultMap="YwDjFpPpResult">
        <include refid="selectYwDjFpPpVo"/>
        where id = #{id}
    </select>

    <select id="selectByDjId" parameterType="String" resultMap="YwDjFpPpResult">
        <include refid="selectYwDjFpPpVo"/>
        where dj_id = #{djId}
        order by pp_rq desc
    </select>

    <select id="selectByDjmxId" parameterType="String" resultMap="YwDjFpPpResult">
        <include refid="selectYwDjFpPpVo"/>
        where djmx_id = #{djmxId}
        order by pp_rq desc
    </select>

    <select id="selectByDjmxAndFpmx" resultMap="YwDjFpPpResult">
        <include refid="selectYwDjFpPpVo"/>
        where djmx_id = #{djmxId} and fpmx_id = #{fpmxId}
        limit 1
    </select>
        
    <insert id="insertYwDjFpPp" parameterType="YwDjFpPp">
        insert into swgx_yw_dj_fp_pp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="djId != null and djId != ''">dj_id,</if>
            <if test="djmxId != null and djmxId != ''">djmx_id,</if>
            <if test="fpId != null and fpId != ''">fp_id,</if>
            <if test="fpmxId != null and fpmxId != ''">fpmx_id,</if>
            <if test="ppSl != null">pp_sl,</if>
            <if test="ppJe != null">pp_je,</if>
            <if test="ppSe != null">pp_se,</if>
            <if test="ppDf != null">pp_df,</if>
            <if test="ppLx != null">pp_lx,</if>
            <if test="ppZt != null">pp_zt,</if>
            <if test="ppRq != null">pp_rq,</if>
            <if test="ppRy != null">pp_ry,</if>
            <if test="hcZt != null">hc_zt,</if>
            <if test="hcRq != null">hc_rq,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="djId != null and djId != ''">#{djId},</if>
            <if test="djmxId != null and djmxId != ''">#{djmxId},</if>
            <if test="fpId != null and fpId != ''">#{fpId},</if>
            <if test="fpmxId != null and fpmxId != ''">#{fpmxId},</if>
            <if test="ppSl != null">#{ppSl},</if>
            <if test="ppJe != null">#{ppJe},</if>
            <if test="ppSe != null">#{ppSe},</if>
            <if test="ppDf != null">#{ppDf},</if>
            <if test="ppLx != null">#{ppLx},</if>
            <if test="ppZt != null">#{ppZt},</if>
            <if test="ppRq != null">#{ppRq},</if>
            <if test="ppRy != null">#{ppRy},</if>
            <if test="hcZt != null">#{hcZt},</if>
            <if test="hcRq != null">#{hcRq},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateYwDjFpPp" parameterType="YwDjFpPp">
        update swgx_yw_dj_fp_pp
        <trim prefix="SET" suffixOverrides=",">
            <if test="djId != null and djId != ''">dj_id = #{djId},</if>
            <if test="djmxId != null and djmxId != ''">djmx_id = #{djmxId},</if>
            <if test="fpId != null and fpId != ''">fp_id = #{fpId},</if>
            <if test="fpmxId != null and fpmxId != ''">fpmx_id = #{fpmxId},</if>
            <if test="ppSl != null">pp_sl = #{ppSl},</if>
            <if test="ppJe != null">pp_je = #{ppJe},</if>
            <if test="ppSe != null">pp_se = #{ppSe},</if>
            <if test="ppDf != null">pp_df = #{ppDf},</if>
            <if test="ppLx != null">pp_lx = #{ppLx},</if>
            <if test="ppZt != null">pp_zt = #{ppZt},</if>
            <if test="ppRq != null">pp_rq = #{ppRq},</if>
            <if test="ppRy != null">pp_ry = #{ppRy},</if>
            <if test="hcZt != null">hc_zt = #{hcZt},</if>
            <if test="hcRq != null">hc_rq = #{hcRq},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYwDjFpPpById" parameterType="String">
        delete from swgx_yw_dj_fp_pp where id = #{id}
    </delete>

    <delete id="deleteYwDjFpPpByIds" parameterType="String">
        delete from swgx_yw_dj_fp_pp where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据发票ID查询匹配关系 -->
    <select id="selectByFpId" parameterType="String" resultMap="YwDjFpPpResult">
        <include refid="selectYwDjFpPpVo"/>
        where fp_id = #{fpId} and pp_zt = '1'
        order by pp_rq desc
    </select>

    <!-- 根据发票明细ID查询匹配关系 -->
    <select id="selectByFpmxId" parameterType="String" resultMap="YwDjFpPpResult">
        <include refid="selectYwDjFpPpVo"/>
        where fpmx_id = #{fpmxId} and pp_zt = '1'
        order by pp_rq desc
    </select>






</mapper>
