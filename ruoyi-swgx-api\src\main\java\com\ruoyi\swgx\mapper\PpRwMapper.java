package com.ruoyi.swgx.mapper;

import com.ruoyi.swgx.domain.PpRw;
import java.util.List;

/**
 * 匹配任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface PpRwMapper {
    
    /**
     * 查询匹配任务
     * 
     * @param id 匹配任务主键
     * @return 匹配任务
     */
    public PpRw selectPpRwById(String id);

    /**
     * 查询匹配任务列表
     * 
     * @param ppRw 匹配任务
     * @return 匹配任务集合
     */
    public List<PpRw> selectPpRwList(PpRw ppRw);

    /**
     * 新增匹配任务
     * 
     * @param ppRw 匹配任务
     * @return 结果
     */
    public int insertPpRw(PpRw ppRw);

    /**
     * 修改匹配任务
     * 
     * @param ppRw 匹配任务
     * @return 结果
     */
    public int updatePpRw(PpRw ppRw);

    /**
     * 删除匹配任务
     * 
     * @param id 匹配任务主键
     * @return 结果
     */
    public int deletePpRwById(String id);

    /**
     * 批量删除匹配任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePpRwByIds(String[] ids);
}
