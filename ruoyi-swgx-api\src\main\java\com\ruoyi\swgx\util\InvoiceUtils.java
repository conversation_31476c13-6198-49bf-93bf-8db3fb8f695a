package com.ruoyi.swgx.util;

import com.ruoyi.swgx.common.constants.SwgxApiConstants;
import com.ruoyi.swgx.common.dto.invoice.InvoiceDetailParam;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 发票工具类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class InvoiceUtils {

    /**
     * 计算发票明细的税额
     * 
     * @param amount 不含税金额
     * @param taxRate 税率
     * @return 税额
     */
    public static BigDecimal calculateTaxAmount(BigDecimal amount, BigDecimal taxRate) {
        if (amount == null || taxRate == null) {
            return BigDecimal.ZERO;
        }
        
        return amount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算价税合计
     * 
     * @param amount 不含税金额
     * @param taxAmount 税额
     * @return 价税合计
     */
    public static BigDecimal calculateTotalAmount(BigDecimal amount, BigDecimal taxAmount) {
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
        if (taxAmount == null) {
            taxAmount = BigDecimal.ZERO;
        }
        
        return amount.add(taxAmount).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算发票汇总金额
     * 
     * @param details 发票明细列表
     * @return 汇总金额数组 [不含税金额, 税额, 价税合计]
     */
    public static BigDecimal[] calculateSummaryAmounts(List<InvoiceDetailParam> details) {
        if (details == null || details.isEmpty()) {
            return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalTaxAmount = BigDecimal.ZERO;

        for (InvoiceDetailParam detail : details) {
            if (detail.getJe() != null) {
                totalAmount = totalAmount.add(detail.getJe());
            }
            if (detail.getSe() != null) {
                totalTaxAmount = totalTaxAmount.add(detail.getSe());
            }
        }

        BigDecimal totalWithTax = totalAmount.add(totalTaxAmount);

        return new BigDecimal[]{
            totalAmount.setScale(2, RoundingMode.HALF_UP),
            totalTaxAmount.setScale(2, RoundingMode.HALF_UP),
            totalWithTax.setScale(2, RoundingMode.HALF_UP)
        };
    }

    /**
     * 验证发票类型代码是否有效
     * 
     * @param invoiceTypeCode 发票类型代码
     * @return 是否有效
     */
    public static boolean isValidInvoiceType(String invoiceTypeCode) {
        if (invoiceTypeCode == null || invoiceTypeCode.trim().isEmpty()) {
            return false;
        }

        return invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.TAX_CONTROL_ELECTRONIC) ||
               invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.TAX_CONTROL_NORMAL) ||
               invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.TAX_CONTROL_SPECIAL) ||
               invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.TAX_CONTROL_ROLL) ||
               invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.TAX_CONTROL_ELECTRONIC_SPECIAL) ||
               invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.DIGITAL_SPECIAL) ||
               invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.DIGITAL_NORMAL) ||
               invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.DIGITAL_VEHICLE) ||
               invoiceTypeCode.equals(SwgxApiConstants.InvoiceType.DIGITAL_USED_VEHICLE);
    }

    /**
     * 获取发票类型描述
     * 
     * @param invoiceTypeCode 发票类型代码
     * @return 发票类型描述
     */
    public static String getInvoiceTypeDescription(String invoiceTypeCode) {
        if (invoiceTypeCode == null) {
            return "未知类型";
        }

        switch (invoiceTypeCode) {
            case SwgxApiConstants.InvoiceType.TAX_CONTROL_ELECTRONIC:
                return "税控电子发票";
            case SwgxApiConstants.InvoiceType.TAX_CONTROL_NORMAL:
                return "税控普通发票";
            case SwgxApiConstants.InvoiceType.TAX_CONTROL_SPECIAL:
                return "税控专用发票";
            case SwgxApiConstants.InvoiceType.TAX_CONTROL_ROLL:
                return "税控卷票";
            case SwgxApiConstants.InvoiceType.TAX_CONTROL_ELECTRONIC_SPECIAL:
                return "税控电子专用发票";
            case SwgxApiConstants.InvoiceType.DIGITAL_SPECIAL:
                return "数电专票";
            case SwgxApiConstants.InvoiceType.DIGITAL_NORMAL:
                return "数电普票";
            case SwgxApiConstants.InvoiceType.DIGITAL_VEHICLE:
                return "数电机动车纸票";
            case SwgxApiConstants.InvoiceType.DIGITAL_USED_VEHICLE:
                return "数电二手车纸票";
            default:
                return "未知类型(" + invoiceTypeCode + ")";
        }
    }

    /**
     * 验证税率是否有效
     * 
     * @param taxRate 税率
     * @return 是否有效
     */
    public static boolean isValidTaxRate(BigDecimal taxRate) {
        if (taxRate == null) {
            return false;
        }

        // 常见税率：0%, 3%, 5%, 6%, 9%, 13%, 16%
        BigDecimal[] validRates = {
            new BigDecimal("0"),
            new BigDecimal("0.01"),
            new BigDecimal("0.03"),
            new BigDecimal("0.05"),
            new BigDecimal("0.06"),
            new BigDecimal("0.09"),
            new BigDecimal("0.13"),
            new BigDecimal("0.16")
        };

        for (BigDecimal validRate : validRates) {
            if (taxRate.compareTo(validRate) == 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 格式化金额显示
     * 
     * @param amount 金额
     * @return 格式化后的金额字符串
     */
    public static String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        
        return amount.setScale(2, RoundingMode.HALF_UP).toString();
    }

    /**
     * 验证纳税人识别号格式
     * 
     * @param taxNumber 纳税人识别号
     * @return 是否有效
     */
    public static boolean isValidTaxNumber(String taxNumber) {
        if (taxNumber == null || taxNumber.trim().isEmpty()) {
            return false;
        }

        // 统一社会信用代码：18位
        // 纳税人识别号：15位或18位
        String cleanTaxNumber = taxNumber.trim();
        return cleanTaxNumber.length() == 15 || cleanTaxNumber.length() == 18;
    }
}
