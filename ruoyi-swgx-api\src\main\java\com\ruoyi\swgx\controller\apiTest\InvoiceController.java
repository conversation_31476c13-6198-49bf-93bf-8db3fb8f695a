package com.ruoyi.swgx.controller.apiTest;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.swgx.common.dto.invoice.ApplyInvoiceRequest;
import com.ruoyi.swgx.common.dto.invoice.ApplyInvoiceResponse;
import com.ruoyi.swgx.common.dto.invoice.InvoiceCallbackData;
import com.ruoyi.swgx.service.apiTest.InvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 发票相关接口控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/swgx/invoice")
@Validated
public class InvoiceController extends BaseController {

    @Autowired
    private InvoiceService invoiceService;

    /**
     * 发票开具申请(V2)
     */
    @PostMapping("/apply")
    public AjaxResult applyInvoice(@Valid @RequestBody ApplyInvoiceRequest request) {
        try {
            log.info("发票开具申请: ddlsh={}", request.getDdlsh());
            
            ApplyInvoiceResponse response = invoiceService.applyInvoice(request);
            
            return AjaxResult.success("申请成功", response);
            
        } catch (Exception e) {
            log.error("发票开具申请失败", e);
            return AjaxResult.error("申请失败: " + e.getMessage());
        }
    }

    /**
     * 快速发票开具申请
     * 自动获取企业唯一标识，简化调用流程
     */
    @PostMapping("/quickApply")
    public AjaxResult quickApplyInvoice(@Valid @RequestBody ApplyInvoiceRequest request) {
        try {
            log.info("快速发票开具申请: ddlsh={}", request.getDdlsh());

            ApplyInvoiceResponse response = invoiceService.quickApplyInvoice(request);

            return AjaxResult.success("申请成功", response);

        } catch (Exception e) {
            log.error("快速发票开具申请失败", e);
            return AjaxResult.error("申请失败: " + e.getMessage());
        }
    }

    /**
     * 发票开具回调接口
     * 用于接收百旺金穗云的回调通知
     */
    @PostMapping("/callback")
    public AjaxResult invoiceCallback(@RequestBody InvoiceCallbackData callbackData) {
        try {
            log.info("收到发票开具回调: ddlsh={}, status={}", 
                    callbackData.getDdlsh(), callbackData.getStatus());
            
            invoiceService.handleInvoiceCallback(callbackData);
            
            return AjaxResult.success("回调处理成功");
            
        } catch (Exception e) {
            log.error("发票开具回调处理失败", e);
            return AjaxResult.error("回调处理失败: " + e.getMessage());
        }
    }
}
