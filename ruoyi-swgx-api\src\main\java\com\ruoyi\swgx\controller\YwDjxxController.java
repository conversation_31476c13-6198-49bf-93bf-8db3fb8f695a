package com.ruoyi.swgx.controller;

import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.swgx.domain.YwDjxx;
import com.ruoyi.swgx.service.IYwDjxxService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.swgx.service.IMatchService;
import com.ruoyi.swgx.domain.dto.MatchRequestDto;
import com.ruoyi.swgx.domain.dto.MatchProgressDto;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 业务单据主Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Controller
@RequestMapping("/swgx/djxx")
public class YwDjxxController extends BaseController
{
    private String prefix = "swgx/djxx";

    @Autowired
    private IYwDjxxService ywDjxxService;

    @Autowired
    private IMatchService matchService;

    @RequiresPermissions("swgx:djxx:view")
    @GetMapping()
    public String djxx()
    {
        return prefix + "/djxx";
    }

    /**
     * 查询业务单据主列表
     */
    @RequiresPermissions("swgx:djxx:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(YwDjxx ywDjxx)
    {
        startPage();
        List<YwDjxx> list = ywDjxxService.selectYwDjxxList(ywDjxx);
        return getDataTable(list);
    }

    /**
     * 导出业务单据主列表
     */
    @RequiresPermissions("swgx:djxx:export")
    @Log(title = "业务单据主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(YwDjxx ywDjxx)
    {
        List<YwDjxx> list = ywDjxxService.selectYwDjxxList(ywDjxx);
        ExcelUtil<YwDjxx> util = new ExcelUtil<YwDjxx>(YwDjxx.class);
        return util.exportExcel(list, "业务单据主数据");
    }

    /**
     * 新增业务单据主
     */
    @RequiresPermissions("swgx:djxx:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存业务单据主
     */
    @RequiresPermissions("swgx:djxx:add")
    @Log(title = "业务单据主", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(YwDjxx ywDjxx)
    {
        return toAjax(ywDjxxService.insertYwDjxx(ywDjxx));
    }

    /**
     * 查看业务单据详情
     */
    @RequiresPermissions("swgx:djxx:view")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") String id, ModelMap mmap)
    {
        YwDjxx ywDjxx = ywDjxxService.selectYwDjxxById(id);
        if (ywDjxx == null) {
            mmap.put("errorMsg", "单据不存在或已被删除");
            return "error/404";
        }
        mmap.put("ywDjxx", ywDjxx);
        return prefix + "/detail";
    }

    /**
     * 修改业务单据主
     */
    @RequiresPermissions("swgx:djxx:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        YwDjxx ywDjxx = ywDjxxService.selectYwDjxxById(id);
        mmap.put("ywDjxx", ywDjxx);
        return prefix + "/edit";
    }

    /**
     * 修改保存业务单据主
     */
    @RequiresPermissions("swgx:djxx:edit")
    @Log(title = "业务单据主", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(YwDjxx ywDjxx)
    {
        return toAjax(ywDjxxService.updateYwDjxx(ywDjxx));
    }

    /**
     * 删除业务单据主
     */
    @RequiresPermissions("swgx:djxx:remove")
    @Log(title = "业务单据主", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(ywDjxxService.deleteYwDjxxByIds(ids));
    }

    /**
     * 启动匹配任务
     */
    @RequiresPermissions("swgx:djxx:match")
    @Log(title = "单据匹配", businessType = BusinessType.OTHER)
    @PostMapping("/startMatch")
    @ResponseBody
    public AjaxResult startMatch(@RequestBody MatchRequestDto request)
    {
        try {
            String taskId = matchService.startMatchTask(request);
            return AjaxResult.success("匹配任务已启动").put("taskId", taskId);
        } catch (Exception e) {
            return AjaxResult.error("启动匹配任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取匹配进度
     */
    @RequiresPermissions("swgx:djxx:match")
    @GetMapping("/getMatchProgress/{taskId}")
    @ResponseBody
    public AjaxResult getMatchProgress(@PathVariable("taskId") String taskId)
    {
        try {
            MatchProgressDto progress = matchService.getMatchProgress(taskId);
            if (progress != null) {
                return AjaxResult.success(progress);
            } else {
                return AjaxResult.error("任务不存在");
            }
        } catch (Exception e) {
            return AjaxResult.error("获取匹配进度失败：" + e.getMessage());
        }
    }

    /**
     * 取消匹配任务
     */
    @RequiresPermissions("swgx:djxx:match")
    @Log(title = "取消匹配", businessType = BusinessType.OTHER)
    @PostMapping("/cancelMatch/{taskId}")
    @ResponseBody
    public AjaxResult cancelMatch(@PathVariable("taskId") String taskId)
    {
        try {
            boolean result = matchService.cancelMatchTask(taskId);
            if (result) {
                return AjaxResult.success("匹配任务已取消");
            } else {
                return AjaxResult.error("取消匹配任务失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("取消匹配任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取单据匹配结果详情
     */
    @RequiresPermissions("swgx:djxx:view")
    @GetMapping("/getMatchResults/{id}")
    @ResponseBody
    public AjaxResult getMatchResults(@PathVariable("id") String id)
    {
        try {
            Map<String, Object> matchResults = ywDjxxService.getDocumentMatchResults(id);
            return AjaxResult.success(matchResults);
        } catch (Exception e) {
            return AjaxResult.error("获取匹配结果失败：" + e.getMessage());
        }
    }

    /**
     * 跳转到匹配结果页面
     */
    @RequiresPermissions("swgx:djxx:view")
    @GetMapping("/matchResults/{id}")
    public String matchResults(@PathVariable("id") String id, ModelMap mmap)
    {
        YwDjxx ywDjxx = ywDjxxService.selectYwDjxxById(id);
        mmap.put("ywDjxx", ywDjxx);
        return prefix + "/matchResults";
    }
}
