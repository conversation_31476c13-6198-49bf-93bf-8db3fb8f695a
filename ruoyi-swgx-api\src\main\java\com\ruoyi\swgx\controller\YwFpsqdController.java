package com.ruoyi.swgx.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.swgx.domain.YwFpsqd;
import com.ruoyi.swgx.service.IYwFpsqdService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 发票申请单信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Controller
@RequestMapping("/swgx/fpsqd")
public class YwFpsqdController extends BaseController
{
    private String prefix = "swgx/fpsqd";

    @Autowired
    private IYwFpsqdService ywFpsqdService;

    @RequiresPermissions("swgx:fpsqd:view")
    @GetMapping()
    public String fpsqd()
    {
        return prefix + "/fpsqd";
    }

    /**
     * 查询发票申请单信息列表
     */
    @RequiresPermissions("swgx:fpsqd:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(YwFpsqd ywFpsqd)
    {
        startPage();
        List<YwFpsqd> list = ywFpsqdService.selectYwFpsqdList(ywFpsqd);
        return getDataTable(list);
    }

    /**
     * 导出发票申请单信息列表
     */
    @RequiresPermissions("swgx:fpsqd:export")
    @Log(title = "发票申请单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(YwFpsqd ywFpsqd)
    {
        List<YwFpsqd> list = ywFpsqdService.selectYwFpsqdList(ywFpsqd);
        ExcelUtil<YwFpsqd> util = new ExcelUtil<YwFpsqd>(YwFpsqd.class);
        return util.exportExcel(list, "发票申请单信息数据");
    }

    /**
     * 新增发票申请单信息
     */
    @RequiresPermissions("swgx:fpsqd:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存发票申请单信息
     */
    @RequiresPermissions("swgx:fpsqd:add")
    @Log(title = "发票申请单信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(YwFpsqd ywFpsqd)
    {
        return toAjax(ywFpsqdService.insertYwFpsqd(ywFpsqd));
    }

    /**
     * 查看发票申请单详情
     */
    @RequiresPermissions("swgx:fpsqd:detail")
    @GetMapping("/detail/{swguid}")
    public String detail(@PathVariable("swguid") String swguid, ModelMap mmap)
    {
        YwFpsqd ywFpsqd = ywFpsqdService.selectYwFpsqdBySwguid(swguid);
        mmap.put("ywFpsqd", ywFpsqd);
        return prefix + "/detail";
    }

    /**
     * 修改发票申请单信息
     */
    @RequiresPermissions("swgx:fpsqd:edit")
    @GetMapping("/edit/{swguid}")
    public String edit(@PathVariable("swguid") String swguid, ModelMap mmap)
    {
        YwFpsqd ywFpsqd = ywFpsqdService.selectYwFpsqdBySwguid(swguid);
        mmap.put("ywFpsqd", ywFpsqd);
        return prefix + "/edit";
    }

    /**
     * 修改保存发票申请单信息
     */
    @RequiresPermissions("swgx:fpsqd:edit")
    @Log(title = "发票申请单信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(YwFpsqd ywFpsqd)
    {
        return toAjax(ywFpsqdService.updateYwFpsqd(ywFpsqd));
    }

    /**
     * 删除发票申请单信息
     */
    @RequiresPermissions("swgx:fpsqd:remove")
    @Log(title = "发票申请单信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(ywFpsqdService.deleteYwFpsqdBySwguids(ids));
    }
}
