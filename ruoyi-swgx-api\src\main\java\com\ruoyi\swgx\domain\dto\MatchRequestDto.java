package com.ruoyi.swgx.domain.dto;

import lombok.Data;
import java.util.List;

/**
 * 匹配请求DTO
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
public class MatchRequestDto {
    
    /** 单据ID列表 */
    private List<String> djIds;
    
    /** 匹配类型：SINGLE-单个匹配，BATCH-批量匹配，MERGE-合并匹配 */
    private String matchType;
    
    /** 匹配规则列表：EXACT-精确匹配，FUZZY-模糊匹配 */
    private List<String> matchRules;
    
    /** 匹配字段列表：spmc-商品名称，dj-单价，slv-税率，spbm-商品编码，ggxh-规格型号，dw-单位 */
    private List<String> matchFields;
    
    /** 优化策略：MIN_INVOICES-最少发票数量，MAX_MATCH_RATE-最大匹配率，BALANCE-平衡策略 */
    private String optimizeStrategy;
}
