/**
 * 首页JavaScript功能
 * 河北九赋后台管理系统
 */

$(function() {
    // 初始化页面
    initPage();
    
    // 加载公告列表
    loadNotices();
    
    // 加载系统信息
    loadSystemInfo();
    
    // 绑定事件
    bindEvents();
});

/**
 * 初始化页面
 */
function initPage() {
    // 更新当前时间
    updateCurrentTime();
    
    // 每秒更新时间
    setInterval(updateCurrentTime, 1000);
}

/**
 * 更新当前时间
 */
function updateCurrentTime() {
    var now = new Date();
    var timeString = now.getFullYear() + '-' + 
                    String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(now.getDate()).padStart(2, '0') + ' ' + 
                    String(now.getHours()).padStart(2, '0') + ':' + 
                    String(now.getMinutes()).padStart(2, '0') + ':' + 
                    String(now.getSeconds()).padStart(2, '0');
    $('#currentTime').text(timeString);
}

/**
 * 加载公告列表
 */
function loadNotices() {
    var $noticeList = $('#noticeList');
    
    // 显示加载状态
    $noticeList.html(
        '<div class="text-center" style="padding: 40px 20px; color: #999;">' +
        '<i class="fa fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px; display: block;"></i>' +
        '<p>正在加载公告...</p>' +
        '</div>'
    );
    
    $.ajax({
        url: ctx + 'system/main/notices',
        type: 'POST',
        dataType: 'json',
        success: function(result) {
            if (result.code === 0 && result.data && result.data.length > 0) {
                renderNotices(result.data);
            } else {
                showEmptyNotice();
            }
        },
        error: function() {
            showEmptyNotice('加载公告失败，请稍后重试');
        }
    });
}

/**
 * 渲染公告列表
 */
function renderNotices(notices) {
    var $noticeList = $('#noticeList');
    var html = '';

    $.each(notices, function(index, notice) {
        var typeClass = notice.noticeType === '1' ? 'type-1' : 'type-2';
        var typeName = notice.noticeType === '1' ? '通知' : '公告';
        var createTime = formatDate(notice.createTime);
        var content = notice.noticeContent || '';

        // 移除HTML标签并截取内容
        content = content.replace(/<[^>]*>/g, '').substring(0, 80);
        if (content.length >= 80) {
            content += '...';
        }

        html += '<div class="feed-element" data-id="' + notice.noticeId + '">' +
                '<div class="feed-date">' + createTime + '</div>' +
                '<div class="feed-title">' + (notice.noticeTitle || '无标题') + '</div>';

        if (content) {
            html += '<div class="feed-content">' + content + '</div>';
        }

        html += '<div class="feed-meta">' +
                '<span><i class="fa fa-user"></i> ' + (notice.createBy || '系统') + '</span>' +
                '<span class="feed-type ' + typeClass + '">' + typeName + '</span>' +
                '</div>' +
                '</div>';
    });

    $noticeList.html(html);
}

/**
 * 显示空公告状态
 */
function showEmptyNotice(message) {
    var $noticeList = $('#noticeList');
    var msg = message || '暂无公告信息';

    $noticeList.html(
        '<div class="text-center" style="padding: 60px 20px; color: #999;">' +
        '<i class="fa fa-bullhorn" style="font-size: 4rem; color: #ddd; margin-bottom: 20px; display: block;"></i>' +
        '<p style="font-size: 1.1rem; margin: 0;">' + msg + '</p>' +
        '</div>'
    );
}

/**
 * 加载系统信息
 */
function loadSystemInfo() {
    // 获取系统信息
    var userAgent = navigator.userAgent;
    var platform = navigator.platform;
    
    // 更新系统信息显示
    $('#browserInfo').text(getBrowserInfo());
    $('#platformInfo').text(platform);
    $('#screenInfo').text(screen.width + ' × ' + screen.height);
    $('#languageInfo').text(navigator.language || navigator.userLanguage);
}

/**
 * 获取浏览器信息
 */
function getBrowserInfo() {
    var userAgent = navigator.userAgent;
    var browser = 'Unknown';
    
    if (userAgent.indexOf('Chrome') > -1) {
        browser = 'Chrome';
    } else if (userAgent.indexOf('Firefox') > -1) {
        browser = 'Firefox';
    } else if (userAgent.indexOf('Safari') > -1) {
        browser = 'Safari';
    } else if (userAgent.indexOf('Edge') > -1) {
        browser = 'Edge';
    } else if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident') > -1) {
        browser = 'Internet Explorer';
    }
    
    return browser;
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 刷新数据按钮
    $('#refreshData').on('click', function() {
        loadNotices();
        loadSystemInfo();
        $.modal.msg('数据已刷新');
    });
    
    // 公告项点击事件
    $(document).on('click', '.feed-element', function() {
        var noticeId = $(this).data('id');
        if (noticeId) {
            viewNotice(noticeId);
        }
    });

    // 折叠面板事件
    $(document).on('click', '.collapse-link', function() {
        var ibox = $(this).closest('.ibox');
        var content = ibox.find('.ibox-content');
        var icon = $(this).find('i');

        content.slideToggle(200);
        icon.toggleClass('fa-chevron-up').toggleClass('fa-chevron-down');
    });
}

/**
 * 查看公告详情
 */
function viewNotice(noticeId) {
    var url = ctx + 'system/notice/view/' + noticeId;
    $.modal.open('公告详情', url, '800', '600');
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '';
    
    var date = new Date(dateString);
    var now = new Date();
    var diff = now.getTime() - date.getTime();
    var days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
        var hours = Math.floor(diff / (1000 * 60 * 60));
        if (hours === 0) {
            var minutes = Math.floor(diff / (1000 * 60));
            if (minutes === 0) {
                return '刚刚';
            } else {
                return minutes + '分钟前';
            }
        } else {
            return hours + '小时前';
        }
    } else if (days === 1) {
        return '昨天';
    } else if (days < 7) {
        return days + '天前';
    } else {
        return date.getFullYear() + '-' + 
               String(date.getMonth() + 1).padStart(2, '0') + '-' + 
               String(date.getDate()).padStart(2, '0');
    }
}

/**
 * 打开菜单页面
 */
function openMenu(url) {
    if (parent && parent.$ && parent.$) {
        // 在父窗口中打开菜单
        parent.$('a[href="' + url + '"]').click();
    } else {
        // 直接跳转
        window.location.href = ctx + url;
    }
}
